import axios from "axios";
import __errorHandler from "./errorHandler";
import __popper<PERSON>andler from "./popperHandler";
import __poppyHandler from "./poppyHandler";

window.__acm = {
    toggleOther: function(elm) {

        let parent = elm.closest('.price-table-row');

        if (parent) {
            parent.classList.toggle('price-table-row--show-other');
        }

        return false;

    },

    removeRow: function(elm) {
        let parent = elm.closest('.price-table-row');

        if (parent) {

            __popperHandler.confirm({
                title: 'Konaklama satırını sil',
                body: 'Bu konaklama satırını silmek istediğinize emin misiniz?\n\nDaha önce bu konaklama türünü tercih edenler değişiklikten etkilenmeyecekler.',
                confirm: {
                    label: 'Sil',
                    class: 'button--red',
                    handler: function() {
                        parent.remove();
                        return true;
                    },
                },
                cancel: {
                    label: 'Vazgeç',
                    class: 'button--gray',
                    handler: function() {
                        return true;
                    }
                },
            });

        }

        return false;
    },

    addRow: function(options) {
     
        options = options || {};

        let price = options.price || "";
        let price_student = options.price_student || "";
        let title = options.title || "";
        let priceEarly = options.price_early || "";
        let priceEarlyDate = options.price_early_date || "";
        let priceLate = options.price_late || "";
        let priceLateDate = options.price_late_date || "";

        let priceTable = document.getElementById('price-table');

        if (priceTable) {

            let latestId = 0;

            let lastRow = priceTable.querySelectorAll('.price-table-row:last-child');
            if (lastRow.length) {
                latestId = parseInt(lastRow[0].getAttribute('data-id'));
                latestId++;
            }

            let newRow = document.createElement('div');
            newRow.classList.add('price-table-row');
            newRow.classList.add('px-3');
            newRow.setAttribute('data-id', latestId);

            newRow.innerHTML = `
            <div class="row">
                <div class="col-12 col-md-6 my-2">
                    <p class="fs-sm fw-500 color-grey-1 mb-1">Konaklama adı <span class="color-red">*</span></p>
                    <input type="text" name="price_table[${latestId}][title]" placeholder="Konaklama Tercihi" value="${title}" class="form-control" required readonly>
                </div>
                <div class="col-12 col-md-3 my-2">
                    <p class="fs-sm fw-500 color-grey-1 mb-1">Fiyat <span class="color-red">*</span></p>
                    <div class="form-input">
                        <span class="form-input-prefix">
                            <span class="fw-600 fs-md color-black">TL</span>
                        </span>
                        <input type="text" name="price_table[${latestId}][price]" placeholder="Konaklama Ücreti" value="${price}" class="form-control" required>
                    </div>
                </div>
                <div class="col-12 col-md-3 my-2">
                    <p class="fs-sm fw-500 color-grey-1 mb-1">Öğrenci Fiyatı <span class="color-red"></span></p>
                    <div class="form-input">
                        <span class="form-input-prefix">
                            <span class="fw-600 fs-md color-black">TL</span>
                        </span>
                        <input type="text" name="price_table[${latestId}][price_student]" placeholder="Öğrenci Ücreti" value="${price_student}" class="form-control" >
                    </div>
                </div>
            </div>
            <div class="d-flex align-items-center justify-content-between">
                <a href="#" onclick="return __acm.toggleOther(this)" class="fw-500 fs-sm link show__price-table--other">
                    <span class="show-other">Ödeme planını göster</span>
                    <span class="hide-other">Ödeme planını gizle</span>
                </a>
                <button onclick="return __acm.removeRow(this)" type="button" class="button button--red">Kaldır</button>
            </div>
            <div class="price-table--other">
                <div class="row">
                    <div class="col-12 col-md-6 my-2">
                        <p class="fs-sm fw-500 color-grey-1 mb-1">Erken Ödeme Tarihi</p>
                        <input type="text" name="price_table[${latestId}][price_early_date]" value="${priceEarlyDate}" class="form-control datetimepicker" placeholder="Erken Ödeme Tarihi" data-time="false" data-dateformat="Y-m-d">
                        <p class="fs-sm color-grey-2 pt-1">Bu tarihte ve öncesinde yapılan başvurular için buradaki fiyat uygulanacaktır</p>
                    </div>
                    <div class="col-12 col-md-6 my-2">
                        <p class="fs-sm fw-500 color-grey-1 mb-1">Erken Ödeme Fiyatı</p>
                        <div class="form-input">
                            <span class="form-input-prefix">
                                <span class="fw-600 fs-md color-black">TL</span>
                            </span>
                            <input type="text" name="price_table[${latestId}][price_early]" value="${priceEarly}" class="form-control">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 col-md-6 my-2">
                        <p class="fs-sm fw-500 color-grey-1 mb-1">Geç Ödeme Tarihi</p>
                        <input type="text" name="price_table[${latestId}][price_late_date]" value="${priceLateDate}" class="form-control datetimepicker" placeholder="Geç Ödeme Tarihi" data-time="false" data-dateformat="Y-m-d">
                        <p class="fs-sm color-grey-2 pt-1">Bu tarihte ve sonrasında yapılan başvurular için buradaki fiyat uygulanacaktır</p>
                    </div>
                    <div class="col-12 col-md-6 my-2">
                        <p class="fs-sm fw-500 color-grey-1 mb-1">Geç Ödeme Fiyatı</p>
                        <div class="form-input">
                            <span class="form-input-prefix">
                                <span class="fw-600 fs-md color-black">TL</span>
                            </span>
                            <input type="text" name="price_table[${latestId}][price_late]" value="${priceLate}" class="form-control">
                        </div>
                    </div>
                </div>
            </div>
            `;

            priceTable.appendChild(newRow);


            Array.from(newRow.querySelectorAll('.datetimepicker')).forEach(function(dtpicker) {

                let options = {
                    enableTime: true,
                    dateFormat: "Y-m-d H:i",
                    minuteIncrement: 30,
                    time_24hr: true,
                    defaultHour: 0,
                    monthSelectorType: 'static',
                    onChange: function(dateObject, dateString, instance) {
            
                        if (dtpicker.getAttribute('data-onchange')) {
                            let callback = dtpicker.getAttribute('data-onchange');
                            __dtHandler[callback](dateObject, dateString, instance);
                        }
                    },
                    onOpen: function(dateObject, dateString, instance) {
                        if (dtpicker.getAttribute('data-onopen')) {
                            let callback = dtpicker.getAttribute('data-onopen');
                            __dtHandler[callback](dateObject, dateString, instance);
                        }
                    }
                };
            
                if (dtpicker.getAttribute('data-mode')) {
                    options['mode'] = dtpicker.getAttribute('data-mode');
                }
            
                if (dtpicker.getAttribute('data-time')) {
                    if (dtpicker.getAttribute('data-time') == 'false') {
                        options['enableTime'] = false;
                    }
                }
            
                if (dtpicker.getAttribute('data-dateformat')) {
                    options['dateFormat'] = dtpicker.getAttribute('data-dateformat');
                }
            
                if (dtpicker.getAttribute('data-mindate')) {
                    options['minDate'] = dtpicker.getAttribute('data-mindate');
                }
            
                if (dtpicker.getAttribute('data-nocalendar')) {
                    if (dtpicker.getAttribute('data-nocalendar') == 'true') {
                        options['noCalendar'] = true;
                    }
                }
            
                flatpickr(dtpicker, options);
            });

        }

        return false;

    },

    loadFromTemplate: function(template) {

        let priceTable = document.getElementById('price-table');
        if (priceTable) {
            priceTable.innerHTML = '';
        }

        if (typeof template == "string") {
            template = JSON.parse(template);
        }

        for (let i = 0; i < template.length; i++) {
            __acm.addRow(template[i]);
        }

    },

    loadFromClosestTemplate: function(elm) {

        let parent = elm.closest('.card');
        let textarea = parent.querySelector('textarea');

        if (textarea) {
            __acm.loadFromTemplate(textarea.value);
            __poppyHandler.info('Konaklama ödeme şablonu başarıyla yüklendi.');
        }

        let popperDismissBtn = document.querySelector('.popper-header button');
        if (popperDismissBtn) {
            popperDismissBtn.click();
        }

        return false;

    },

    selectTemplate: function() {

        axios.get('/admin/ajax/price-table/template').then(function(response) {
           
            let data = response.data;

            let bodyOutput = '<div style="max-height: 200px; overflow: auto">';

            for (let i = 0; i < data.length; i++) {
             
                let template = data[i];                

                bodyOutput += (`
                <div class="col-12 card border-y py-2">
                    <div class="d-flex flex-col flex-md-row justify-content-between align-items-center">
                        <div class="d-flex flex-col flex-1 mb-2">
                            <p class="fs-sm fw-500 color-grey-1">${template.title}</p>
                            <p class="fs-xs color-grey-1 lh-1">${template.created_at_text}</p>                            
                        </div>
                        <div class="mx-4 mb-2">${template.price_template_text}</div>
                        <button onclick="return __acm.loadFromClosestTemplate(this)" class="button button--small mt-2">Yükle</button>
                        <textarea style="display: none;">${JSON.stringify(template.price_table)}</textarea>
                    </div>
                </div>`).replace(/\n/g, '');

            }

            bodyOutput += '</div>';

            __popperHandler.confirm({
                title: 'Konaklama Fiyat Tablosu Şablonu Seç',
                body: bodyOutput
            })

        }).catch(function(error) {
            __errorHandler(error);
        });

    } 


}

if (document.querySelector('#edit_price')) {
    document.querySelector('#edit_price').onclick = function() {

        let priceDisplayElm = document.querySelector('#price_display');
        let priceEditingElm = document.querySelector('#price_editing');

        if (priceEditingElm.className.indexOf('d-none') == -1) {
            this.innerText = 'Düzenle';
            priceEditingElm.classList.add('d-none');
            priceEditingElm.setAttribute('name', '__new_price');
        } else {
            this.innerText = 'Vazgeç';
            priceEditingElm.setAttribute('name', 'new_price');
            priceEditingElm.value = priceEditingElm.getAttribute('data-previous-price');
            priceEditingElm.classList.remove('d-none');
        }

        return false;
    }
}

if (document.querySelector('#edit_funds')) {
    document.querySelector('#edit_funds').onclick = function() {

        let priceeDisplayElm = document.querySelector('#funds_display');
        let priceeEditingElm = document.querySelector('#funds_editing');

        if (priceeEditingElm.className.indexOf('d-none') == -1) {
            this.innerText = 'Düzenle';
            priceeEditingElm.classList.add('d-none');
            priceeEditingElm.setAttribute('name', '__new_funds');
        } else {
            this.innerText = 'Vazgeç';
            priceeEditingElm.setAttribute('name', 'new_funds');
            priceeEditingElm.value = priceeEditingElm.getAttribute('data-previous-price');
            priceeEditingElm.classList.remove('d-none');
        }

        return false;
    }
}