<style>
    @import url('https://fonts.googleapis.com/css2?family=Jost:ital,wght@0,100..900;1,100..900&display=swap');

    :root{
        --jost: 'Jost', sans-serif;
    }

    @media (max-width: 991px){


    }


    .side-group{
        padding: 1rem;
    }


    .sidebar-active{
        background-color: rgba(38, 45, 51, 1) !important;
        color:  white !important;
    }

    .side-item{
        padding: 1rem 0.75rem;
        width: 15rem;
    }

    .side-items a{
        font-family: var(--jost);
        font-size: 14px;
        font-weight: 500;
        color: rgba(82, 82, 86, 1);
        letter-spacing: 0.3px;
        border-radius: 0;
    }

    .dropdown-menu {
        width: 15rem;
        display: none;
        position: absolute;
        background-color: white;
        border: 1px solid rgba(0, 0, 0, 0.15);
    }

    .dropdown-item {
        display: block;
        width: 100%;
        clear: both;
        font-weight: 400;
        color: rgba(82, 82, 86, 1);
        text-align: inherit;
        white-space: nowrap;
        background-color: transparent;
        border: 0;
        cursor: pointer;
        padding: 0.75rem;
    }


    .dropdown-item:hover,
    .dropdown-item:focus {
        color: white;
        background-color: rgba(38, 45, 51, 1);
        border-radius: 0;
    }

    .rotated {
        transform: rotate(-180deg); /* 180 derece döndür */
        transition: all 0.3s ease-in-out;
    }
</style>



{% set masterData = app.request.session.get('master_data') %}

<div class="sidebar mt-5">
    <div class="side-list">
        <div class="side-group">
            {% if admin_user.super_admin == 1 %}
            <div class="form-control-wrapper" style="width: 15rem;">
                <div class="form-label">
                    <label for="company_id">{{ trans('Firma Değiştir') }}</label>
                </div>
                <div class="form-input">
                    <select name="company_id" id="company_id" class="form-control" required>
                        <option value="" disabled selected>{{ trans('Seçiniz') }}</option>
                        <option value="master_data" {% if masterData %}selected{% endif %}>İştegeliş Master Data</option>
                        {% for chooseCompany in chooseCompanyList %}
                            <option value="{{ chooseCompany.company_id }}" {% if masterData is null and admin_user.company_id == chooseCompany.company_id %}selected{% endif %}>{{ chooseCompany.company_name }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            {% endif %}
            <ul class="side-items">
                {% if masterData is null %}
                <a href="{{ route('admin.company.detail') }}">
                    <li class="side-item {% if app.request.pathinfo == '/admin/company/detail' %} sidebar-active {% endif %}">
                            <span>{{ trans('Firma Profili') }}</span>
                    </li>
                </a>
                <a class="dropdown-toggle" id="kurslar" style="cursor: pointer;" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <li class="side-item ">
                        <div class="d-flex justify-content-between align-items-center">
                            <span> {{ trans('Kurslar') }}</span>
                            <span class="iconify" style="font-size: 20px;" data-icon="material-symbols:keyboard-arrow-down-rounded" data-inline="false"></span>
                        </div>
                    </li>
                </a>
                <div class="dropdown-menu" aria-labelledby="kurslar">
                    {% if admin_user.super_admin == 1 %}
                        <a class="dropdown-item {% if app.request.pathinfo == '/admin/course' %} sidebar-active {% endif %}" href="{{ route('admin.course.index') }}" > {{trans('Kurslar')}} </a>
                    {% endif %}
                    <a class="dropdown-item {% if app.request.pathinfo == '/admin/course/enrolment' %} sidebar-active {% endif %}" href="{{ route('admin.course.enrolment') }}" > {{trans('Kurs Kayıt')}} </a>
                    {% if admin_user.super_admin == 1 %}
                        <a class="dropdown-item {% if app.request.pathinfo == '/admin/categories' %} sidebar-active {% endif %}" href="{{ route('admin.category.index') }}"> {{trans('Kategoriler')}} </a>
                    {% endif %}
                </div>
                <a href="{{ route('admin.users.index') }}">
                    <li class="side-item {% if app.request.pathinfo == '/admin/users' %} sidebar-active {% endif %}">
                            <span>{{ trans('Kullanıcılar') }}</span>
                    </li>
                </a>
                <li class="py-0 side-item">
                    <hr>
                </li>
                {% if admin_user.super_admin == 1 %}
                    <a href="{{ route('admin.educators.index') }}">
                        <li class="side-item {% if app.request.pathinfo == '/admin/educators' %} sidebar-active {% endif %}">
                                <span>{{ trans('Eğitmenler') }}</span>
                        </li>
                    </a>
                {% endif %}
                {% endif %}

                {% if masterData %}
                {% if admin_user.super_admin == 1 %}
                    <a href="{{ route('admin.dashboard.index') }}">
                        <li class="side-item mt-3 {% if app.request.pathinfo == '/admin' %} sidebar-active {% endif %}">
                                <span>{{ trans('Firma Listesi') }}</span>
                        </li>
                    </a>
                    <a href="{{ route('admin.course.index') }}">
                        <li class="side-item {% if app.request.pathinfo == '/admin/course' %} sidebar-active {% endif %}">
                            <span>{{ trans('Kurslar') }}</span>
                        </li>
                    </a>
                    <a href="{{ route('admin.educators.index') }}">
                        <li class="side-item {% if app.request.pathinfo == '/admin/educators' %} sidebar-active {% endif %}">
                            <span>{{ trans('Eğitmenler') }}</span>
                        </li>
                    </a>
                    <a href="{{ route('admin.smstemplate.index') }}">
                        <li class="side-item {% if app.request.pathinfo == '/admin/smstemplate' %} sidebar-active {% endif %}">
                                <span>{{ trans('SMS Şablonları') }}</span>
                        </li>
                    </a>
                    <a href="{{ route('admin.mailtemplate.index') }}">
                        <li class="side-item {% if app.request.pathinfo == '/admin/mailtemplate' %} sidebar-active {% endif %}">
                                <span>{{ trans('Mail Şablonları') }}</span>
                        </li>
                    </a>
                {% endif %}
                {% endif %}

                {% if masterData is null %}
                {% if admin_user.super_admin == 1 %}
                    <a href="{{ route('admin.administrators.index') }}">
                        <li class="side-item {% if app.request.pathinfo == '/admin/administrators' %} sidebar-active {% endif %}">
                                <span>{{ trans('Yöneticiler') }}</span>
                        </li>
                    </a>
                {% endif %}
                {% if admin_user.super_admin == 1 %}
                    <a href="{{ route('admin.demo.index') }}">
                        <li class="side-item {% if app.request.pathinfo == '/admin/demo' %} sidebar-active {% endif %}">
                                <span>{{ trans('Demo Talepleri') }}</span>
                        </li>
                    </a>
                {% endif %}

                <a href="{{ route('admin.profile.index') }}">
                    <li class="side-item {% if app.request.pathinfo == '/admin/profile' %} sidebar-active {% endif %}">
                            <span>{{ trans('Profilim') }}</span>
                    </li>
                </a>
                <a href="{{ route('admin.feedback.index') }}">
                    <li class="side-item {% if app.request.pathinfo == '/admin/feedback' %} sidebar-active {% endif %}">
                            <span>{{ trans('Geri Bildirimler') }}</span>
                    </li>
                </a>
                {% if admin_user.super_admin == 1 %}
                <a href="{{ route('admin.banner.index') }}">
                    <li class="side-item {% if app.request.pathinfo == '/admin/banner' %} sidebar-active {% endif %}">
                            <span>{{ trans('Banner Ayarları') }}</span>
                    </li>
                </a>
                    {% endif %}
                {% endif %}
                <a href="{{ route('logout') }}">
                    <li class="side-item">
                        <span>{{ trans('Çıkış Yap') }}</span>
                    </li>
                </a>
                <a href="{{
                    route('home.locale', {
                        locale: app.getLocale == 'en' ? 'tr' : 'en'
                    })
                }}">
                    <i class="side-item d-flex align-items-center">
                        <img height="20" src="/images/locale/{{ locale }}.png" />

                        <span style="font-family: var(--roboto); font-weight: bold; font-size: 14px; margin-left: 8px;">
                            {{ trans("Dil Değiştir") }}
                        </span>
                    </i>
                </a>
            </ul>
        </div>
    </div>
</div>


<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>

<script>
$('.dropdown-toggle').click(function() {
    var $dropdownMenu = $(this).next('.dropdown-menu');
    var $icon = $(this).find('.iconify');
    var isVisible = $dropdownMenu.is(':visible');
    $('.dropdown-menu').hide();
    $('.iconify').removeClass('rotated');

    if (!isVisible) {
        $dropdownMenu.show();
        $icon.addClass('rotated');
        setTimeout(function() {
            $icon.css('transition', 'transform 0.3s ease-in-out');
        }, 0);
    } else {
        $icon.removeClass('rotated');
    }
});
</script>
{% if admin_user.super_admin == 1 %}
{% block scripts %}
    <script>
        $('.sidebar #company_id').on('change', function() {
            var company_id = $(this).val();
            window.location.href = '{{ url("admin/company/change") }}/?company_id=' + company_id;
            //window.location.href = '/admin?company_id=' + company_id;
        });
    </script>
{% endblock %}
{% endif %}
