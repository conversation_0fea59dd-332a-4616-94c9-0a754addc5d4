<style>
  @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap');
  @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700;900&display=swap');
    :root{
          --white: #fafafa;
          --red: #ba272d;
          --softblack: #434551;
          --roboto: 'Roboto', sans-serif;
          --poppins: 'Poppins', sans-serif;
    }

        .modal-action-group--push-right{
            margin-right: 0 !important;
        }


            @media (max-width:460px){
        .modal-header{
            display:flex !important;
            flex-direction:column !important;
            width:100% !important;
        }
        .modal-header .button--small{
            margin:0 !important;
            margin-top: 1rem !important;
        }
        .modal-header h4{
            font-size: 20px !important;
        }
        .table-checkbox{
        width:0;
        display:none;
    }
    .table-cell{
        padding:0;
    }
    .table-item{
        margin-top: 1rem;
    }
    .table-preview{
        width: 100%;
        height: 10vh;
                margin-top:1rem;
    }
    .table-item{
        display:inline;
    }
    .table-keep{
        width:100%;
        display:inline !important;
    }
    .table-preview+.table-details{
        padding:0 !important;
    }

    }

     @media (max-width:767px){
         .table-cell{
             display: table-cell!important;
             padding-left: 20px!important;
         }
         .table-row {
             display: table-row!important;
         }
         .table-header {
             display: table-header-group!important;
         }
         .table{
             display: table!important;
         }
         .table-items {
             display: table-row-group!important;
         }
         .modal-body{
             overflow-x: auto;
         }
     }
     @media (max-width:330px){
    .sidebar-content, .sidebar{
        width: auto;
    }

    .sidebar-item{
        overflow:hidden;
        width:1.3rem;
        height:1.3rem;
        margin-block:1rem;
    }

    .main-content{
        padding:0;
        margin:0;
        margin-block: 1rem;

    }
    .first-col, .second-col{
            width: 100% !important;
            justify-content:start !important;
        }
                .table-checkbox{
        width:0;
        display:none;
    }
    .table-cell{
        padding:0;
    }
    .table-item{
        margin-top: 1rem;
    }
    .table-preview{
        width:100%;
        height: 10vh;
    }
    }


    @media (min-width:330.02px) and (max-width: 499.98px){

    .sidebar-content, .sidebar{
        width: auto;
    }

    .sidebar-item{
        overflow:hidden;
        width:1.6rem;
        height:1.6rem;
        margin-block:1rem;
    }

    .main-content{
        padding:0;
        margin:0;
        margin-top: 1rem;

    }

   }
   @media (min-width:430.02px) and (max-width: 500px){
    .sidebar-item{
        overflow:hidden;
        width:1.5rem;
        height:1.5rem;
        margin-block:1rem;
    }
    .sidebar-content, .sidebar{
        max-width: 80% ;
    }
        .main-content{
        padding:0;
        margin:0;
        margin-top: 1rem;

    }
   }
   @media (min-width: 501px) and (max-width: 576px){

    .sidebar-content, .sidebar{
        width: auto;
    }

    .sidebar-item{
        overflow:hidden;
    }

    .main-content{
        padding:0;
        margin:0;
        margin-block: 1rem;

    }
   }

    @media (max-width: 576px){
        .first-col, .second-col{
            width: 50%;
            display:flex;
            justify-content:center;
        }
    }

    @media (min-width:576.02px) and (max-width:767px){
    .main-content{
        padding:0;
        margin:0;
        margin-block: 1rem;
        width:100%;
    }
    .row>*{
        padding-left:0 !important;
    }
     .sidebar-item{
        width:2.5rem;
        height:2.5rem;
        margin-block:0.75rem;
        margin-left: 0.5rem;
    }
    .sidebar-content, .sidebar{
        width: 85%;
    }
    .modal-actions{
            display:flex;
            flex-direction:column;
            align-items:start;
        }
        .modal-action-col{
            width:auto;
        }
        .arrows, .fs-sm{
            width:100%;
        }
    }


    @media (min-width:576.02px) and (max-width:1275px){
        .modal-actions{
            display:flex !important;
            flex-direction:column;
            justify-content:center !important;
            align-items:center !important;
        }
        .first-row, .second-row{
            margin-block:1rem;
        }

    }

    @media(min-width:1750px){
        .main-content{
        padding:0;
        margin:0;
        margin-block: 1rem;
        width:100%;
        }
    }


    .add-new {
        background: var(--white) !important;
        border: solid 1px var(--softblack);
        border-radius: 8px;
        color: var(--red) !important;
       padding: 1.5rem !important;
        font-weight:bold !important;
        font-size:14px !important;
    }

    .add-new:hover{
        background: var(--red) !important;
        color: var(--white) !important;
        transition: all .3s ease-in-out;
    }


    .modal-header h4{
    font-family: var(--poppins);
    font-size: 20px;
    font-weight: 500;
    letter-spacing: 0.3px;
    margin-top: 3rem;
}

.table-details{
    padding: 1rem;
}

</style>


<div class="container">
    <div class="row">
        <div class="col-12 col-md-7 mx-auto">
            {% include "_messageBag.twig" %}
        </div>
        <div class="col-12 first-modal">

            <div data-listing-resource="{{ listing.resource ?: "" }}">
                <div class="modal-header d-flex w-100 align-items-center justify-content-between">
                    <h4>{{ listing.title ? trans(listing.title ~ ' Listesi') : 'Liste' }}</h4>
                    <div class="d-inline-flex align-items-center header-content">
                    {% if listing.resource and route_has('admin.' ~ listing.resource ~ '.sort') %}
                    <a href="{{ route('admin.' ~ listing.resource ~ '.sort') }}" class="button button--small button--gray ms-4">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                            <path opacity=".4" fill="#696974" d="M6.7 12.89c-.52 0-.94.42-.94.94L5.5 18.4c0 .66.53 1.2 1.19 1.2 .65 0 1.19-.55 1.19-1.21l-.26-4.58c0-.53-.42-.95-.94-.95Z"></path>
                            <path fill="#696974" d="M7.98 3.67s-.27-.28-.44-.4c-.25-.19-.54-.28-.84-.28 -.34 0-.65.1-.9.3 -.05.04-.24.22-.41.38 -1.01.95-2.66 3.43-3.16 4.73 -.08.19-.26.69-.27.96 0 .25.05.49.17.73 .16.28.41.52.7.64 .2.08.82.2.83.2 .67.12 1.77.19 2.98.19 1.15 0 2.2-.07 2.89-.18 .01-.02.77-.14 1.04-.28 .47-.26.77-.76.77-1.29v-.05c-.02-.35-.32-1.08-.33-1.08C10.5 7 8.93 4.58 7.89 3.6Z"></path>
                            <path opacity=".4" fill="#696974" d="M17.29 11.1c.51 0 .93-.43.93-.95l.25-4.58c0-.67-.54-1.21-1.2-1.21 -.66 0-1.2.54-1.2 1.2l.25 4.57c0 .52.41.94.93.94Z"></path>
                            <path fill="#696974" d="M21.82 13.88c-.17-.29-.42-.53-.71-.65 -.21-.09-.83-.21-.84-.21 -.68-.13-1.78-.2-2.99-.2 -1.16 0-2.21.06-2.9.17 -.02.01-.78.14-1.05.27 -.49.25-.78.75-.78 1.28v.04c.01.34.3 1.07.32 1.07 .5 1.23 2.06 3.65 3.11 4.63 0 0 .26.27.43.39 .24.18.53.27.83.27 .33 0 .63-.11.89-.31 .04-.05.24-.23.4-.39 1-.96 2.65-3.44 3.15-4.74 .08-.2.25-.7.26-.97 0-.26-.06-.51-.18-.74Z"></path>
                        </svg>
                        <span>{{ trans('Sıralamayı Düzenle') }}</span>
                    </a>
                    {% endif %}
                    {% if listing.resource and route_has('admin.' ~ listing.resource ~ '.create') %}
                    <a href="{{ route('admin.' ~ listing.resource ~ '.create') }}" class="button button--small ms-4 add-new" >
                     <span style="margin-right:10px !important;">
                                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path opacity="0.4" d="M16.6667 2H7.33333C3.92889 2 2 3.92889 2 7.33333V16.6667C2 20.0622 3.92 22 7.33333 22H16.6667C20.0711 22 22 20.0622 22 16.6667V7.33333C22 3.92889 20.0711 2 16.6667 2Z" fill="var(--softblack)"/>
                            <path d="M15.3205 12.7083H12.7495V15.257C12.7495 15.6673 12.4139 16 12 16C11.5861 16 11.2505 15.6673 11.2505 15.257V12.7083H8.67955C8.29342 12.6687 8 12.3461 8 11.9613C8 11.5765 8.29342 11.2539 8.67955 11.2143H11.2424V8.67365C11.2824 8.29088 11.6078 8 11.996 8C12.3842 8 12.7095 8.29088 12.7495 8.67365V11.2143H15.3205C15.7066 11.2539 16 11.5765 16 11.9613C16 12.3461 15.7066 12.6687 15.3205 12.7083Z" fill="var(--white)"/>
                        </svg>
                     </span>
                        <span >{{ trans('Yeni Oluştur') }}</span>
                    </a>
                    {% endif %}
                    </div>
                </div>
                <div class="modal-header"></div>
                <div class="modal-actions">
                    <div class="modal-action-group order-1 modal-action-group--push-right">
                        {% if listing.sort.enabled %}
                        <div class="modal-action-col">
                            <div class="dropdown" data-type="sort">
                                <select name="sorting">
                                    {% for value, label in listing.sort.options %}
                                    <option value="{{ value }}" {{ value == listing.sort.current ? 'selected="selected"' }}>{{ label }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        {% endif %}
                        <div class="modal-action-col">
                            <div class="table-sorting-row">
                                {% if listing.search.enabled %}
                                <div class="search-input">
                                    <div class="search-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="11.7666" cy="11.7666" r="8.98856" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M18.0183 18.4851L21.5423 22" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </div>
                                    <div class="search-control">
                                        <input type="search" name="search" spellcheck="false" placeholder="Ara" value="{{ listing.search.current }}">
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="modal-action-group order-2 order-md-3 ms-auto">
                        {% if listing.count.found > 0 %}
                        <div class="modal-action-col d-none d-md-flex">
                            <div class="dropdown dropdown--small">
                                <select name="per_page" id="per_page">
                                    {% for pp_rows in [25, 50, 100, 250, 500] %}
                                        <option value="{{ pp_rows }}" {% if listing.pagination.per_page == pp_rows %}
                                            selected="selected"
                                        {% endif %}>{{ pp_rows }} kayıt</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>

                        {% endif %}
                        {# eğer just user isteniyorsa filtreleme seçeneklerini gösterme #}
                        {% if listing.filter.enabled and not ( app.router.current.getName starts with 'admin.user' ) %}
                        <div class="modal-action-col">
                            <button type="button" class="button button--big button--gray toggle-filter-options-button"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M10.11 17.98H2.87"></path><path fill="none" fill-rule="evenodd" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M21.11 17.98c0 1.59-1.29 2.88-2.88 2.88 -1.6 0-2.88-1.29-2.88-2.88 0-1.6 1.28-2.88 2.88-2.88 1.59 0 2.88 1.28 2.88 2.88Z"></path><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M13.88 6.26h7.23"></path><path fill="none" fill-rule="evenodd" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M2.88 6.26c0 1.59 1.28 2.88 2.88 2.88 1.59 0 2.88-1.29 2.88-2.88 0-1.6-1.29-2.88-2.88-2.88 -1.6 0-2.88 1.28-2.88 2.88Z"></path></svg></button>
                        </div>
                        {% endif %}
                        {% if listing.export.enabled %}
                        <div class="modal-action-col ps-0">
                            {# <div class="actions">
                                <button class="button button--gray" data-options="item-actions">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M7.38948 8.984H6.45648C4.42148 8.984 2.77148 10.634 2.77148 12.669V17.544C2.77148 19.578 4.42148 21.228 6.45648 21.228H17.5865C19.6215 21.228 21.2715 19.578 21.2715 17.544V12.659C21.2715 10.63 19.6265 8.984 17.5975 8.984L16.6545 8.984" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M12.0214 2.19051V14.2315" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M9.10626 5.1188L12.0213 2.1908L14.9373 5.1188" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                    <span>Dışa Aktar</span>
                                </button>
                            </div> #}
                            <div class="actions">
                                <button type="button" data-options="listing-actions" class="button" style="background-color: transparent;"><span class="iconify" data-icon="material-symbols:upload" style="font-size: 26px; color: black;" data-inline="false"></span></button>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% if listing.filter.enabled %}
                <div class="filter-options-panel">
                    <div class="modal-actions flex-col align-items-start">
                        <div class="modal-action-group">
                            <div class="modal-action-col mb-2">
                                <p class="fw-500 fs-sm">{{ trans('Filtreleme Seçenekleri:') }}</p>
                            </div>
                        </div>
                        <div class="modal-action-group justify-content-start">
                            {% for filter_key, filter_opt in listing.filter.binds %}
                            {% if listing.filter.binds[filter_key]['item_list'] is not empty %}
                            <div class="modal-action-col{{ loop.last ? ' mb-0' }}">
                                <div class="dropdown">
                                    <select class="filter-select" name="filter[{{ filter_key }}]">
                                        <option value="">{{ filter_opt.label }}</option>
                                        {% for filter_item_value, filter_item_label in filter_opt.item_list %}
                                            <option value="{{ filter_item_value }}" {% if filter_item_value in listing.filter.current[filter_key] %}selected="selected"{% endif %}>{{ filter_item_label }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            {% endif %}
                            {% endfor %}
                        </div>
                    </div>
                </div>
                {% endif %}
                {% if ( listing.filter.current is not empty ) and not ( app.router.current.getName starts with 'admin.user' )%}
                <div class="modal-actions justify-content-start">
                    <div class="modal-action-group">
                    {% for key, values in listing.filter.current %}
                        <div class="modal-action-col{{ loop.last ? ' mb-0' }}">
                            <button class="button button--small button--white button--outline filter-button" data-filter="{{ key }}">
                                <svg xmlns="http://www.w3.org/2000/svg" width="8" height="8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="4" d="M0 24L24 0M0 0l24 24"></path>
                                </svg>
                                <span class="fw-600 color-black">{{ listing.filter.binds[key]['label'] }}:&nbsp;</span>
                                {% if listing.filter.binds[key]['item_list'] is not empty %}
                                <span class="color-blue">{{ listing.filter.binds[key]['item_list']|get_filter_labels(values) }}</span>
                                {% else %}
                                <span class="color-blue">{{ values|join(', ') }}</span>
                                {% endif %}
                            </button>
                        </div>
                    {% endfor %}
                    </div>
                </div>
                {% endif %}
                <div class="modal-body">
                    <div class="table">

                        <div class="table-header">
                            {# Tablo başlıkları #}
                            {% include "admin/listing/_table/_header.twig" %}
                        </div>

                        <div class="table-items" tabindex="0">
                            {# Tablo Satırları #}
                            {% include "admin/listing/_table/_items.twig" %}
                        </div>
                        {# <div class="table-row">

                            <div class="table-cell table-keep">
                                <a class="table-item" href="#">
                                    <div class="table-preview"><img class="table-pic" src="img/product-pic.png" alt=""></div>
                                    <div class="table-details">
                                        <div class="table-title">Abstract 3D Kit</div>
                                        <div class="table-info">3D Illustration</div>
                                    </div>
                                </a>
                            </div>
                            <div class="table-cell table-keep">39511350 1234 1234&nbsp;</div>
                            <div class="table-cell">$68.00</div>
                            <div class="table-cell color-red">-68.00</div>
                            <div class="table-cell text-right">
                            <div class="table-status caption bg-green">Sale</div>
                            </div>
                        </div>

                        <div class="table-body">
                            <div class="table-bg bg-green"></div>
                            <div class="table-line">
                                <div class="table-col color-gray">17 Aug 2020</div>
                                <div class="table-col">$68.00</div>
                                <div class="table-col color-red">-68.00</div>
                            </div>
                        </div>
                        <div class="table-row">
                            <div class="table-cell table-checkbox"><label class="checkbox"><input type="checkbox"><div class="checkbox-control"><span class="checkbox-tick"></span></div></label></div>
                            <div class="table-cell color-grey">17 Aug 2020</div>
                            <div class="table-cell table-keep">39511350</div>
                            <div class="table-cell">$68.00</div>
                            <div class="table-cell color-red">-68.00</div>
                            <div class="table-cell text-right">
                                <div class="table-status caption bg-green">Sale</div>
                            </div>
                        </div>
                        <div class="table-body">
                            <div class="table-bg bg-green"></div>
                            <div class="table-line">
                                <div class="table-col color-gray">17 Aug 2020</div>
                                <div class="table-col">$68.00</div>
                                <div class="table-col color-red">-68.00</div>
                            </div>
                        </div>
                        <div class="table-row">
                            <div class="table-cell table-checkbox"><label class="checkbox"><input type="checkbox"><div class="checkbox-control"><span class="checkbox-tick"></span></div></label></div>
                            <div class="table-cell color-grey">17 Aug 2020</div>
                            <div class="table-cell table-keep">39511350</div>
                            <div class="table-cell">
                                <div class="table-color">
                                    <div class="table-bg" style="background-color: #6C5DD3;"></div>
                                    <div class="table-text">Purple</div>
                                </div>
                            </div>
                            <div class="table-cell color-red">-68.00</div>
                            <div class="table-cell text-right">
                                <div class="table-status caption bg-green">Sale</div>
                            </div>
                        </div> #}

                    </div>
                    {% if items is empty %}
                        <div class="d-flex flex-col align-items-center justify-content-center pt-4">
                            <p class="sh3">{{trans('Hiç')}} {{ listing.title ? listing.title|tr_lower ~ trans(' kaydı') : trans('kayıt') }} {{trans('bulunamadı')}}</p>
                            {% if listing.resource and route_has('admin.' ~ listing.resource ~ '.create') %}
                            <a href="{{ route('admin.' ~ listing.resource ~ '.create') }}" class="button mt-3 add-new">
                            <span style="margin-right:10px !important;">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path opacity="0.4" d="M16.6667 2H7.33333C3.92889 2 2 3.92889 2 7.33333V16.6667C2 20.0622 3.92 22 7.33333 22H16.6667C20.0711 22 22 20.0622 22 16.6667V7.33333C22 3.92889 20.0711 2 16.6667 2Z" fill="var(--softblack)"/>
                                    <path d="M15.3205 12.7083H12.7495V15.257C12.7495 15.6673 12.4139 16 12 16C11.5861 16 11.2505 15.6673 11.2505 15.257V12.7083H8.67955C8.29342 12.6687 8 12.3461 8 11.9613C8 11.5765 8.29342 11.2539 8.67955 11.2143H11.2424V8.67365C11.2824 8.29088 11.6078 8 11.996 8C12.3842 8 12.7095 8.29088 12.7495 8.67365V11.2143H15.3205C15.7066 11.2539 16 11.5765 16 11.9613C16 12.3461 15.7066 12.6687 15.3205 12.7083Z" fill="var(--white)"/>
                                    </svg>
                            </span>

                                <span >{{ trans('Yeni Oluştur') }}</span>
                            </a>
                            {% endif %}
                        </div>
                    {% endif %}

                  <div class="d-flex justify-content-center align-items-center mt-3">
                    <div class="modal-action-col d-flex mb-0">
                        <a class="button button--gray" {% if listing.pagination.previous == false %}disabled="disabled"{% else %}href="{{ app.request.fullUrlWithQuery({ page: listing.pagination.previous })  }}"{% endif %}>
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M15.5 19L8.5 12L15.5 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </a>
                        <div class="d-flex align-items-center mx-2">
                            <p>{{ listing.pagination.current }} / {{ listing.pagination.max }}</p>
                        </div>
                        <a class="button button--gray" {% if listing.pagination.next == false %}disabled="disabled"{% else %}href="{{ app.request.fullUrlWithQuery({ page: listing.pagination.next })  }}"{% endif %}>
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M8.5 5L15.5 12L8.5 19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </a>
                    </div>
                  </div>
                </div>
            </div>

        </div>
    </div>
</div>
