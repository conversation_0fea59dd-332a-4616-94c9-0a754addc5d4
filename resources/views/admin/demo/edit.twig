{% extends "admin/html.twig" %}
{% block body %}
    <div class="container mx-auto">
        <div class="row form-modal">
            <div class="row">
                <div class="col-12 mx-auto">
                    {% include "_messageBag.twig" %}
                </div>
            </div>

            {% if item %}
                {{ form_model(item, {'route': ['admin.' ~ listing.resource ~ '.update', item.id], 'method': 'PUT'}) }}
            {% else %}
                {{ form_model(null, {'route': ['admin.' ~ listing.resource ~ '.store']}) }}
            {% endif %}
            {# Başlık #}
            <div class="col-12 col-md-8 mx-auto">
                <div class="row">
                    <div class="col-12">
                        <div class="modal py-3">
                            <div class="modal-header">
                                <div class="d-flex align-items-md-center justify-content-between flex-col flex-md-row">
                                    <h4><PERSON><PERSON><PERSON><PERSON><PERSON></h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-12 col-md-8 mx-auto">

                <div class="row">

                    {# Sol taraf #}
                    <div class="col-12" spellcheck="false">

                        <div class="row">

                            <div class="col-12">

                                <div class="col-12 form modal mt-4" spellcheck="false">
                                    <div class="modal-body">

                                        <div class="row">
                                            <div class="col-12">
                                                <div class="row">
                                                    <div class="col-12 col-md-6">
                                                        <div class="col-12">
                                                            <p class="fw-500 fs-xs">Ad Soyad</p>
                                                            <p class="fw-500 fs-md color-black">{{ item.name }}</p>
                                                        </div>
                                                        <div class="col-12 my-2">
                                                            <p class="fw-500 fs-xs">E-Posta</p>
                                                            <p class="fw-500 fs-xs">
                                                                <a class="link" href="mailto:{{ item.email }}">{{ item.email }}</a>
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <div class="col-12 col-md-6 text-left text-md-right">
                                                        <div class="col-12">
                                                            <p class="fw-500 fs-xs">Tarih</p>
                                                            <p class="fw-500 fs-xs color-grey-1">{{ item.created_at|date_iso('llll') }}</p>
                                                        </div>
                                                        <div class="col-12 my-2">
                                                            <p class="fw-500 fs-xs">IP Adresi</p>
                                                            <p class="fw-500 fs-xs">
                                                                <p class="fw-500 fs-xs color-grey-1">{{ item.ip }}</p>
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-12">
                                                <div class="col-12 my-2">
                                                    <p class="fw-500 fs-xs">Mesaj</p>
                                                    <p class="fw-500 fs-sm color-black ws-pw" style="">{{ item.message }}</p>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>

                            </div>

                        </div>

                    </div>

                </div>

            </div>
            {{ form_close() }}
        </div>
        {# {% block footer %}{% endblock %} #}
    </div>

    {# {% if item and listing.actions.enabled and route_has('admin.' ~ listing.resource ~ '.destroy') %}
{{ form_model(item, {'route': ['admin.' ~ listing.resource ~ '.destroy', item.id], 'method': 'DELETE', 'class': 'form-destroy-item'}) }}
{{ form_close() }}
{% endif %} #}

{% endblock %}