<div id="enrolledModal" class="custom-modal-overlay">
    <div class="custom-modal-content">
        <div class="loader-content d-none">
            <span class="loader"></span>
        </div>
        <span class="iconify color-orange" style="font-size: 60px;" data-icon="ion:warning-outline" data-inline="false"></span>
        <h4>{{ trans('Başarılı') }}!</h4>
        <p>{{ app.session.get('message') }}</p>
        <p>{{ trans("Fakat listedeki kullanıcılar maalesef eklenemedi. Abonelik paketinizi yükseleterek kullanıcıları kursa kayıt edebilirsiniz.") }}</p>
        <ul class="mt-3" style="font-size: 14px;list-style: inside;">
            {% for notEnrolment in app.session.get('notEnrolment') %}
                <li data-email="{{ notEnrolment.email }}">
                    {{ notEnrolment.name }}
                    <button type="button" class="user-enrol-ajax" data-user-id="{{ notEnrolment.id }}" data-user-name="{{ notEnrolment.name }}">
                        <span class="iconify color-blue" style="font-size: 20px;" data-icon="fluent-mdl2:open-enrollment" data-inline="false"></span>
                    </button>
                </li>
            {% endfor %}

        </ul>
        <div class="text-right">
            <button class="close-modal button button--red mt-2" data-target="#enrolledModal">{{ trans('Kapat') }}</button>
        </div>
    </div>
</div>

{% block styles %}
<!-- CSS (Basit Stil) -->
<style>
    .loader-content {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(5px);
    }
    .loader {
        width: 38px;
        height: 38px;
        border-radius: 50%;
        position: relative;
        display: inline-block;
        animation: rotate 1s linear infinite;
    }
    .loader::before {
        content: "";
        box-sizing: border-box;
        position: absolute;
        inset: 0px;
        border-radius: 50%;
        border: 4px solid #2196F3;
        animation: prixClipFix 2s linear infinite;
    }

    @keyframes rotate {
        100%   {transform: rotate(360deg)}
    }

    @keyframes prixClipFix {
        0%   {clip-path:polygon(50% 50%,0 0,0 0,0 0,0 0,0 0)}
        25%  {clip-path:polygon(50% 50%,0 0,100% 0,100% 0,100% 0,100% 0)}
        50%  {clip-path:polygon(50% 50%,0 0,100% 0,100% 100%,100% 100%,100% 100%)}
        75%  {clip-path:polygon(50% 50%,0 0,100% 0,100% 100%,0 100%,0 100%)}
        100% {clip-path:polygon(50% 50%,0 0,100% 0,100% 100%,0 100%,0 0)}
    }

    .user-enrol-ajax,.user-enrol-add-ajax{
        padding: 0;
        background: transparent;
        border: 0;
        vertical-align: middle;
        cursor: pointer;
    }
    .custom-modal-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1000;
    }
    .custom-modal-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        padding: 20px;
        border-radius: 5px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        z-index: 1001;
        max-width: 500px;
    }
</style>
{% endblock %}

{% block scripts %}
<script>
    let refresh = false;

    $(document).ready(function() {

        $('body').on('click', '.close-modal', function() {
            const target = $(this).data('target');
            $(target).fadeOut(function() {
                if(target == "#enrolledModal" && refresh){
                    location.reload();
                }
                //$(this).remove(); // Modalı DOM'dan kaldır
            });
        });

        $(`#enrolledModal`).fadeIn();

        $(".user-enrol-ajax").on('click',function (){
            $(`#enrolledModal`).find('.loader-content').removeClass('d-none');
            var userId = $(this).data('user-id');
            var userName = $(this).data('user-name');

            $.ajax(
                {
                    type: "POST",
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    url: "{{ url('admin/course/enrolled') }}/"+userId+'/{{ item.id }}/{{ course_session.id }}',
                    success: function (data)
                    {
                        $(`#enrolledModal`).find('.loader-content').addClass('d-none');
                        const innerModalID = 'innerModal' + Math.floor(Math.random() * 1000);

                        let table = `<div class="table">
                                                <div class="table-header">
                                                    <div class="table-row table-row-head">
                                                        <div class="table-cell table-keep">#</div>
                                                        <div class="table-cell table-keep">{{ trans("Kurs Adı") }}</div>
                                                        <div class="table-cell table-keep">{{ trans("Tarih") }}</div>
                                                        <div class="table-cell table-keep">{{ trans("İşlemler") }}</div>
                                                    </div>
                                                </div>`;
                        $.each(data, function(index, value) {

                            const date = new Date(value.session_date);


                            const day = String(date.getDate()).padStart(2, '0');
                            const month = String(date.getMonth() + 1).padStart(2, '0'); // Ay 0-indexli olduğu için +1
                            const year = date.getFullYear();


                            const formattedDate = `${day}/${month}/${year}`;
                            table = table + `<div class="table-items">
                                                        <div class="table-row">
                                                            <div class="table-cell">${index + 1}</div>
                                                            <div class="table-cell">${value.course_title}</div>
                                                            <div class="table-cell"><span>${formattedDate}</span><br><small class="color-grey-3" style="font-size:12px">${removeSeconds(value.start_time)}-${removeSeconds(value.end_time)}</small></div>
                                                            <div class="table-cell table-keep py-2"><button type="button" class="button button--red enrolledRemove" data-user-id="${userId}" data-course-id="${value.course_id}" data-session-id="${value.session_id}">{{ trans('Çıkar') }}</button></div>
                                                        </div>
                                                    </div>`;
                        });
                        table = table + '</div>';
                        const innerModalContent = `
                                    <div id="${innerModalID}" class="custom-modal-overlay">
                                        <div class="custom-modal-content">
                                            <h4>${userName}</h4>
                                            <p>{{ trans("Kullanıcının kayıtlı olduğu kurslar.") }}</p>
                                            ${table}
                                            <div class="text-right mt-2">
                                                <button class="close-modal button button--red" data-target="#${innerModalID}">{{ trans("Kapat") }}</button>
                                            </div>
                                        </div>
                                    </div>
                                `;
                        $('body').append(innerModalContent);
                        $(`#${innerModalID}`).fadeIn();
                        enrolledUserRemove();
                    },
                }
            );
            function removeSeconds(time) {
                // Zaman stringini iki nokta (:) ile ayırıyoruz
                const parts = time.split(':');

                // İlk iki parçayı (saat ve dakika) alıyoruz
                const hoursAndMinutes = parts.slice(0, 2);

                // Saat ve dakikayı yeniden birleştiriyoruz
                return hoursAndMinutes.join(':');
            }

            $('body').on('click', '.close-modal', function() {
                const target = $(this).data('target');
                $(target).fadeOut(function() {
                    //$(this).remove(); // Modalı DOM'dan kaldır
                });
            });
        });
    });

    function enrolledUserRemove() {
        $(".enrolledRemove").on('click', function () {
            var deleteBtn = $(this);
            swal({
                title: "{{ trans('Emin misiniz?') }}",
                text: "{{ trans('Bu kullanıcıyı kurstan çıkarırsanız kullanıcı eğitime erişemez!') }}",
                icon: "warning",
                buttons: true,
                dangerMode: true,
            })
                .then((willDelete) => {
                    if (willDelete) {
                        var userId = deleteBtn.data('user-id');
                        var courseId = deleteBtn.data('course-id');
                        var sessionId = deleteBtn.data('session-id');
                        $.ajax(
                            {
                                type: "DELETE",
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                                },
                                url: "{{ url('admin/course/enrolment/') }}/"+courseId+"/"+sessionId+"/"+userId+"/user/delete",
                                success: function (data) {
                                    if (data['status']) {
                                        swal("{{ trans('Kullanıcı kurstan çıkartıldı!') }}", {
                                            icon: "success",
                                        }).then((willDelete) => {
                                            let userEnrolAjaxButton = $(".user-enrol-ajax[data-user-id='"+userId+"']");
                                            userEnrolAjaxButton.after('<button type="button" class="user-enrol-add-ajax" data-course-id="{{ item.id }}"> <span class="iconify color-green" style="font-size: 20px;" data-icon="bi:plus" data-inline="false"></span> </button>');
                                            userEnrolAjaxButton.remove();
                                            deleteBtn.closest('.table-row').remove();
                                            enrolUserAdd();
                                        });
                                    } else {
                                        swal("{{ trans('Kullanıcı kurstan çıkarılırken hata oluştu!') }}", {
                                            icon: "error",
                                        });
                                    }
                                },
                            }
                        );

                    } else {
                        swal("{{ trans('Kullanıcı güvende!') }}");
                    }
                });

        });
    }
    function enrolUserAdd(){
        $(".user-enrol-add-ajax").on('click',function (){
            let addButton = $(this);
            let courseId = addButton.data('course-id');
            let email = decodeURIComponent(addButton.closest('li').data('email'));
            $.ajax(
                {
                    type: "POST",
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    url: "{{ url('admin/course/enrolment') }}/"+courseId+"/{{course_session.id}}/save",
                    data: JSON.stringify({'email_list':email,'isAjax':true}),
                    success: function (data) {
                        if (data['status']) {
                            swal("{{ trans('Kullanıcı başarı ile kursa kayıt edildi!') }}", {
                                icon: "success",
                            }).then((willDelete) => {
                                addButton.closest('li').remove();
                                refresh = true;
                            });
                        } else {
                            swal("{{ trans('Kullanıcı kursa kayıt edilirken hata oluştu!') }}", {
                                icon: "error",
                            });
                        }
                    },
                }
            );

        });
    }
</script>
{% endblock %}
