<style>

@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700;900&display=swap');

    :root{
          --white: #fafafa;
          --red: #ba272d;
          --softblack: #434551;
          --roboto: 'Roboto', sans-serif;
    }
        .modal-action-group--push-right{
            margin-right: 0 !important;
        }


    @media (max-width: 576px){
        .table{
            width: max-content !important;
        }
    }

    @media (max-width: 768px){
        .modal{
            overflow-x: auto;
        }
    }

    @media (max-width:820px){

        .modal-header{
            flex-direction: column !important;
        }
        .custom-add-new{
            margin-top: 1rem;
        }
        .search-input{
            margin-inline: 1rem;
        }

    }

    .custom-add-new{
        cursor: pointer;
        outline: none;
        border: 1px solid white;
        padding: 1rem;
        display: flex;
        align-items: center;
        background-color: rgba(186, 39, 45, 1);
        border-radius: 8px;
        transition: all 0.3s ease-in-out;
    }

    .custom-add-new:hover{
        border: 1px solid rgba(186, 39, 45, 1);
        background-color: white;
    }

    .custom-add-new:hover span, .custom-add-new:hover .iconify{
        color: rgba(186, 39, 45, 1);
    }


    .custom-add-new span{
        font-family: var(--roboto);
        font-size: 12px;
        font-weight: 400;
        color: white;
        text-decoration: underline;
        margin-left: 0.25rem;
    }

    .custom-add-new .iconify{
        font-size: 18px;
        color: white;
    }
    @media only screen and (max-width: 767px) {
        .table-row {
            justify-content: space-between;
        }
    }

</style>


<div class="container">
    <div class="row">
        <div class="col-12 col-md-7 mx-auto">
            {% include "_messageBag.twig" %}
        </div>
        <div class="col-12 first-modal">
            <div class="mt-4">
                {% include 'admin/header.twig' with {'title': trans(listing.title)} %}
            </div>
            <div class="modal" data-listing-resource="{{ listing.resource ?: "" }}">
                <div class="modal-header d-flex align-items-center justify-content-between flex-wrap">
                        <div class="d-flex align-items-center flex-wrap">
                            {% if listing.sort.enabled %}
                                <div class="modal-action-col">
                                    <div class="dropdown" data-type="sort">
                                        <select name="sorting">
                                            {% for value, label in listing.sort.options %}
                                                <option value="{{ value }}" {{ value == listing.sort.current ? 'selected="selected"' }}> {{ trans(label) }} </option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                            {% endif %}
                            <div class="modal-action-col ">
                                <div class="table-sorting-row">
                                    {% if listing.search.enabled %}
                                        <div class="search-input">
                                            <div class="search-icon">
                                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <circle cx="11.7666" cy="11.7666" r="8.98856" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                                    <path d="M18.0183 18.4851L21.5423 22" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                                </svg>
                                            </div>
                                            <div class="search-control">
                                                <input type="search" name="search" spellcheck="false" placeholder="{{trans('Ara')}}" value="{{ listing.search.current }}">
                                            </div>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            {% if route_name() == "admin.course.show" %}
                                <div class="modal-action-col">
                                    <div class="checkbox-wrapper-14">
                                        <input id="s1-14" name="expired_date" value="1" {% if app.request.get('expired_date') == "1" %}checked{% endif %} type="checkbox" class="switch">
                                        <label for="s1-14">{{ trans('Tarihi geçmiş eğitimler') }}</label>
                                    </div>

                                    <style>
                                        @supports (-webkit-appearance: none) or (-moz-appearance: none) {
                                            .checkbox-wrapper-14 input[type=checkbox] {
                                                --active: #275EFE;
                                                --active-inner: #fff;
                                                --focus: 2px rgba(39, 94, 254, .3);
                                                --border: #BBC1E1;
                                                --border-hover: #275EFE;
                                                --background: #fff;
                                                --disabled: #F6F8FF;
                                                --disabled-inner: #E1E6F9;
                                                -webkit-appearance: none;
                                                -moz-appearance: none;
                                                height: 21px;
                                                outline: none;
                                                display: inline-block;
                                                vertical-align: top;
                                                position: relative;
                                                margin: 0;
                                                cursor: pointer;
                                                border: 1px solid var(--bc, var(--border));
                                                background: var(--b, var(--background));
                                                transition: background 0.3s, border-color 0.3s, box-shadow 0.2s;
                                            }
                                            .checkbox-wrapper-14 input[type=checkbox]:after {
                                                content: "";
                                                display: block;
                                                left: 0;
                                                top: 0;
                                                position: absolute;
                                                transition: transform var(--d-t, 0.3s) var(--d-t-e, ease), opacity var(--d-o, 0.2s);
                                            }
                                            .checkbox-wrapper-14 input[type=checkbox]:checked {
                                                --b: var(--active);
                                                --bc: var(--active);
                                                --d-o: .3s;
                                                --d-t: .6s;
                                                --d-t-e: cubic-bezier(.2, .85, .32, 1.2);
                                            }
                                            .checkbox-wrapper-14 input[type=checkbox]:disabled {
                                                --b: var(--disabled);
                                                cursor: not-allowed;
                                                opacity: 0.9;
                                            }
                                            .checkbox-wrapper-14 input[type=checkbox]:disabled:checked {
                                                --b: var(--disabled-inner);
                                                --bc: var(--border);
                                            }
                                            .checkbox-wrapper-14 input[type=checkbox]:disabled + label {
                                                cursor: not-allowed;
                                            }
                                            .checkbox-wrapper-14 input[type=checkbox]:hover:not(:checked):not(:disabled) {
                                                --bc: var(--border-hover);
                                            }
                                            .checkbox-wrapper-14 input[type=checkbox]:focus {
                                                box-shadow: 0 0 0 var(--focus);
                                            }
                                            .checkbox-wrapper-14 input[type=checkbox]:not(.switch) {
                                                width: 21px;
                                            }
                                            .checkbox-wrapper-14 input[type=checkbox]:not(.switch):after {
                                                opacity: var(--o, 0);
                                            }
                                            .checkbox-wrapper-14 input[type=checkbox]:not(.switch):checked {
                                                --o: 1;
                                            }
                                            .checkbox-wrapper-14 input[type=checkbox] + label {
                                                display: inline-block;
                                                vertical-align: middle;
                                                cursor: pointer;
                                                margin-left: 4px;
                                            }

                                            .checkbox-wrapper-14 input[type=checkbox]:not(.switch) {
                                                border-radius: 7px;
                                            }
                                            .checkbox-wrapper-14 input[type=checkbox]:not(.switch):after {
                                                width: 5px;
                                                height: 9px;
                                                border: 2px solid var(--active-inner);
                                                border-top: 0;
                                                border-left: 0;
                                                left: 7px;
                                                top: 4px;
                                                transform: rotate(var(--r, 20deg));
                                            }
                                            .checkbox-wrapper-14 input[type=checkbox]:not(.switch):checked {
                                                --r: 43deg;
                                            }
                                            .checkbox-wrapper-14 input[type=checkbox].switch {
                                                width: 38px;
                                                border-radius: 11px;
                                            }
                                            .checkbox-wrapper-14 input[type=checkbox].switch:after {
                                                left: 2px;
                                                top: 1px;
                                                border-radius: 50%;
                                                width: 17px;
                                                height: 17px;
                                                background: var(--ab, var(--border));
                                                transform: translateX(var(--x, 0));
                                            }
                                            .checkbox-wrapper-14 input[type=checkbox].switch:checked {
                                                --ab: var(--active-inner);
                                                --x: 17px;
                                            }
                                            .checkbox-wrapper-14 input[type=checkbox].switch:disabled:not(:checked):after {
                                                opacity: 0.6;
                                            }
                                        }

                                        .checkbox-wrapper-14 * {
                                            box-sizing: inherit;
                                        }
                                        .checkbox-wrapper-14 *:before,
                                        .checkbox-wrapper-14 *:after {
                                            box-sizing: inherit;
                                        }
                                    </style>
                                    <script>
                                        $(document).ready(function(){
                                            $('#s1-14').change(function(){
                                                if($(this).is(':checked')){
                                                    if(window.location.href.indexOf('?') > -1){
                                                        window.location.href = window.location.href + '&expired_date=1';
                                                    }else {
                                                        window.location.href = window.location.href + '?expired_date=1';
                                                    }
                                                }else{
                                                    if (window.location.href.indexOf('?expired_date=1') > -1) {
                                                        window.location.href = window.location.href.replace('?expired_date=1', '');
                                                    }else if (window.location.href.indexOf('&expired_date=1') > -1) {
                                                        window.location.href = window.location.href.replace('&expired_date=1', '');
                                                    }
                                                }
                                            });
                                        });
                                    </script>
                                </div>
                            {% endif %}
                        </div>
                        <div class="d-inline-flex align-items-center header-content">
                                {% if listing.resource and route_has('admin.' ~ listing.resource ~ '.create') and 'course/enrolment' not in app.request.uri %}
                                    <a href="{{ route('admin.' ~ listing.resource ~ '.create') }}" >
                                        <button class="custom-add-new">
                                            <span class="iconify" data-icon="material-symbols:add-rounded" data-inline="false"></span>
                                            <span> {{trans('Yeni Kurs Oluştur')}} </span>
                                        </button>
                                    </a>
                                {% endif %}

                            </div>
                </div>
                <div class="modal-actions">
                    <div class="modal-action-group  second-row  order-3 order-md-2">
                        {% if listing.count.found > 0 %}
                        <div class="modal-action-col d-none d-md-flex">
                            <div class="dropdown dropdown--small">
                                <select name="per_page" id="per_page">
                                    {% for pp_rows in [25, 50, 100, 250, 500] %}
                                        <option value="{{ pp_rows }}" {% if listing.pagination.per_page == pp_rows %}
                                            selected="selected"
                                        {% endif %}>{{ pp_rows }} {{trans('kayıt')}} </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="modal-action-col">
                            <span class="fs-sm">{{ listing.pagination.showing.from ~ ' - ' ~ listing.pagination.showing.to }} ({{ listing.count.found }} kayıt)</span>
                        </div>
                        <div class="modal-action-col arrows d-flex mb-0">
                            <a class="button button--gray" {% if listing.pagination.previous == false %}disabled="disabled"{% else %}href="{{ app.request.fullUrlWithQuery({ page: listing.pagination.previous })  }}"{% endif %}>
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M15.5 19L8.5 12L15.5 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </a>
                            <div class="d-flex align-items-center mx-2">
                                <p>{{ listing.pagination.current }} / {{ listing.pagination.max }}</p>
                            </div>
                            <a class="button button--gray" {% if listing.pagination.next == false %}disabled="disabled"{% else %}href="{{ app.request.fullUrlWithQuery({ page: listing.pagination.next })  }}"{% endif %}>
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M8.5 5L15.5 12L8.5 19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                    <div class="modal-action-group order-2 third-row order-md-3 ">
                        {% endif %}
                        {# eğer just user isteniyorsa filtreleme seçeneklerini gösterme
                        {% if listing.filter.enabled and not ( app.router.current.getName starts with 'admin.user' ) %}
                        <div class="modal-action-col first-col">
                            <button type="button" class="button button--big button--gray toggle-filter-options-button"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M10.11 17.98H2.87"></path><path fill="none" fill-rule="evenodd" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M21.11 17.98c0 1.59-1.29 2.88-2.88 2.88 -1.6 0-2.88-1.29-2.88-2.88 0-1.6 1.28-2.88 2.88-2.88 1.59 0 2.88 1.28 2.88 2.88Z"></path><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M13.88 6.26h7.23"></path><path fill="none" fill-rule="evenodd" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M2.88 6.26c0 1.59 1.28 2.88 2.88 2.88 1.59 0 2.88-1.29 2.88-2.88 0-1.6-1.29-2.88-2.88-2.88 -1.6 0-2.88 1.28-2.88 2.88Z"></path></svg></button>
                        </div>
                        {% endif %} #}
                        {% if listing.export.enabled %}
                        <div class="modal-action-col ps-0 second-col">
                            {# <div class="actions">
                                <button class="button button--gray" data-options="item-actions">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M7.38948 8.984H6.45648C4.42148 8.984 2.77148 10.634 2.77148 12.669V17.544C2.77148 19.578 4.42148 21.228 6.45648 21.228H17.5865C19.6215 21.228 21.2715 19.578 21.2715 17.544V12.659C21.2715 10.63 19.6265 8.984 17.5975 8.984L16.6545 8.984" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M12.0214 2.19051V14.2315" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M9.10626 5.1188L12.0213 2.1908L14.9373 5.1188" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                    <span>Dışa Aktar</span>
                                </button>
                            </div> #}
                            <div class="actions">
                                <button type="button" data-options="listing-actions" class="button button--big button--transparent"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="none" fill-rule="evenodd" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 2.75c5.1 0 9.25 4.14 9.25 9.24 0 5.1-4.15 9.25-9.25 9.25 -5.11 0-9.26-4.15-9.26-9.25 0-5.11 4.14-9.25 9.25-9.25Z"></path><path fill="currentColor" d="M7.52 13.19c-.67 0-1.2-.54-1.2-1.2 0-.66.53-1.2 1.19-1.2 .66 0 1.19.53 1.19 1.19 0 .66-.54 1.19-1.2 1.19Z"></path><path fill="currentColor" d="M12 13.19c-.67 0-1.2-.54-1.2-1.2 0-.66.53-1.2 1.19-1.2 .66 0 1.19.53 1.19 1.19 0 .66-.54 1.19-1.2 1.19Z"></path><path fill="currentColor" d="M16.47 13.19c-.67 0-1.2-.54-1.2-1.2 0-.66.53-1.2 1.19-1.2 .66 0 1.19.53 1.19 1.19 0 .66-.54 1.19-1.2 1.19Z"></path></svg></button>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% if listing.filter.enabled %}
                <div class="filter-options-panel">
                    <div class="modal-actions flex-col align-items-start">
                        <div class="modal-action-group">
                            <div class="modal-action-col  mb-2">
                                <p class="fw-500 fs-sm">{{ trans('Filtreleme Seçenekleri:') }}</p>
                            </div>
                        </div>
                        <div class="modal-action-group justify-content-start">
                            {% for filter_key, filter_opt in listing.filter.binds %}
                            {% if listing.filter.binds[filter_key]['item_list'] is not empty %}
                            <div class="modal-action-col{{ loop.last ? ' mb-0' }}">
                                <div class="dropdown">
                                    <select class="filter-select" name="filter[{{ filter_key }}]">
                                        <option value="">{{ filter_opt.label }}</option>
                                        {% for filter_item_value, filter_item_label in filter_opt.item_list %}
                                            <option value="{{ filter_item_value }}" {% if filter_item_value in listing.filter.current[filter_key] %}selected="selected"{% endif %}>{{ filter_item_label }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            {% endif %}
                            {% endfor %}
                        </div>
                    </div>
                </div>
                {% endif %}
                {% if ( listing.filter.current is not empty ) and not ( app.router.current.getName starts with 'admin.user' )%}
                <div class="modal-actions justify-content-start">
                    <div class="modal-action-group">
                    {% for key, values in listing.filter.current %}
                        <div class="modal-action-col{{ loop.last ? ' mb-0' }}">
                            <button class="button button--small button--white button--outline filter-button" data-filter="{{ key }}">
                                <svg xmlns="http://www.w3.org/2000/svg" width="8" height="8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="4" d="M0 24L24 0M0 0l24 24"></path>
                                </svg>
                                <span class="fw-600 color-black">{{ listing.filter.binds[key]['label'] }}:&nbsp;</span>
                                {% if listing.filter.binds[key]['item_list'] is not empty %}
                                <span class="color-blue">{{ listing.filter.binds[key]['item_list']|get_filter_labels(values) }}</span>
                                {% else %}
                                <span class="color-blue">{{ values|join(', ') }}</span>
                                {% endif %}
                            </button>
                        </div>
                    {% endfor %}
                    </div>
                </div>
                {% endif %}
                <div class="modal-body">
                    <div class="table">

                        <div class="table-header">
                            {# Tablo başlıkları #}
                            {% include "admin/course/listing/_table/_header.twig" %}
                        </div>

                        <div class="table-items" tabindex="0">
                            {# Tablo Satırları #}
                            {% include "admin/course/listing/_table/_items.twig" %}
                        </div>
                        {# <div class="table-row">

                            <div class="table-cell table-keep">
                                <a class="table-item" href="#">
                                    <div class="table-preview"><img class="table-pic" src="img/product-pic.png" alt=""></div>
                                    <div class="table-details">
                                        <div class="table-title">Abstract 3D Kit</div>
                                        <div class="table-info">3D Illustration</div>
                                    </div>
                                </a>
                            </div>
                            <div class="table-cell table-keep">39511350 1234 1234&nbsp;</div>
                            <div class="table-cell">$68.00</div>
                            <div class="table-cell color-red">-68.00</div>
                            <div class="table-cell text-right">
                            <div class="table-status caption bg-green">Sale</div>
                            </div>
                        </div>

                        <div class="table-body">
                            <div class="table-bg bg-green"></div>
                            <div class="table-line">
                                <div class="table-col color-gray">17 Aug 2020</div>
                                <div class="table-col">$68.00</div>
                                <div class="table-col color-red">-68.00</div>
                            </div>
                        </div>
                        <div class="table-row">
                            <div class="table-cell table-checkbox"><label class="checkbox"><input type="checkbox"><div class="checkbox-control"><span class="checkbox-tick"></span></div></label></div>
                            <div class="table-cell color-grey">17 Aug 2020</div>
                            <div class="table-cell table-keep">39511350</div>
                            <div class="table-cell">$68.00</div>
                            <div class="table-cell color-red">-68.00</div>
                            <div class="table-cell text-right">
                                <div class="table-status caption bg-green">Sale</div>
                            </div>
                        </div>
                        <div class="table-body">
                            <div class="table-bg bg-green"></div>
                            <div class="table-line">
                                <div class="table-col color-gray">17 Aug 2020</div>
                                <div class="table-col">$68.00</div>
                                <div class="table-col color-red">-68.00</div>
                            </div>
                        </div>
                        <div class="table-row">
                            <div class="table-cell table-checkbox"><label class="checkbox"><input type="checkbox"><div class="checkbox-control"><span class="checkbox-tick"></span></div></label></div>
                            <div class="table-cell color-grey">17 Aug 2020</div>
                            <div class="table-cell table-keep">39511350</div>
                            <div class="table-cell">
                                <div class="table-color">
                                    <div class="table-bg" style="background-color: #6C5DD3;"></div>
                                    <div class="table-text">Purple</div>
                                </div>
                            </div>
                            <div class="table-cell color-red">-68.00</div>
                            <div class="table-cell text-right">
                                <div class="table-status caption bg-green">Sale</div>
                            </div>
                        </div> #}

                    </div>
                    {% if items is empty %}
                        <div class="d-flex flex-col align-items-center justify-content-center pt-4">
                            <p class="sh3">{{trans('Hiç')}} {{ listing.title ? listing.title|tr_lower ~ trans(' kaydı') : trans('kayıt') }} {{trans('bulunamadı')}}</p>
                            {% if admin_user.super_admin == 1 %}
                                {% if listing.resource and route_has('admin.' ~ listing.resource ~ '.create')  and 'course/enrolment' not in app.request.uri %}
                                <a href="{{ route('admin.' ~ listing.resource ~ '.create') }}" class="button mt-3 add-new">
                                <span style="margin-right:10px !important;">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path opacity="0.4" d="M16.6667 2H7.33333C3.92889 2 2 3.92889 2 7.33333V16.6667C2 20.0622 3.92 22 7.33333 22H16.6667C20.0711 22 22 20.0622 22 16.6667V7.33333C22 3.92889 20.0711 2 16.6667 2Z" fill="var(--softblack)"/>
                                        <path d="M15.3205 12.7083H12.7495V15.257C12.7495 15.6673 12.4139 16 12 16C11.5861 16 11.2505 15.6673 11.2505 15.257V12.7083H8.67955C8.29342 12.6687 8 12.3461 8 11.9613C8 11.5765 8.29342 11.2539 8.67955 11.2143H11.2424V8.67365C11.2824 8.29088 11.6078 8 11.996 8C12.3842 8 12.7095 8.29088 12.7495 8.67365V11.2143H15.3205C15.7066 11.2539 16 11.5765 16 11.9613C16 12.3461 15.7066 12.6687 15.3205 12.7083Z" fill="var(--white)"/>
                                        </svg>
                                </span>

                                    <span >{{ trans('Yeni Oluştur') }}</span>
                                </a>
                                {% endif %}
                                {% endif %}

                        </div>
                    {% endif %}
                </div>
            </div>

        </div>
    </div>
</div>
{% block styles %}
    <style>
        @media only screen and (max-width: 767px) {
            .modal-body{
                overflow-x: auto;
            }
            .table-header{
                display: table-header-group;
            }
            .table-items {
                display: table-row-group;
                width: 100%;
            }
            .table-row {
                display: table-row;
            }
            .table {
                display: table;
            }
            .table-cell {
                display: table-cell;
            }
            .table-cell:last-child {
                white-space: nowrap;
            }
        }
    </style>
{% endblock %}
