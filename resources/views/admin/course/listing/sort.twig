{% extends "admin/html.twig" %}
{% block body %}
<div class="container mx-auto">
    <div class="row form-modal">

        <div class="col-12 col-md-7 px-0 mx-auto">
            {% include "_messageBag.twig" %}
        </div>
        <div class="col-12 col-md-7 form modal mx-auto" spellcheck="false">
            <div class="modal-header">
                <h4>{{ listing.title ? listing.title ~ ' ' }} sıralama düzeni</h4>
            </div>
            {{ form_model(null, {'route': ['admin.' ~ listing.resource ~ '.sort', item.id], 'method': 'PUT'}) }}
            <input id="sorting_order" name="sorting_order" type="hidden" value="{{ current_order }}">
            <div class="modal-body">
                <div class="sorting-order-wrapper">
                    {% for item in items %}
                        <div class="sorting-order-row" data-sort-item-id="{{ item.id }}">
                            <div class="sorting-order-handle"></div>
                            <div class="sorting-order-content">
                                <div class="sorting-order-label">
                                    <p>{{ item.title|default(item.label)|default(item.name)|default(item.fullname)|default(item.slug) }}</p>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
            <div class="modal-footer">
                <div class="form-row">
                    <div class="form-control-wrapper text-right">
                        <button type="submit" class="button button--big">Sıralamayı Kaydet</button>
                    </div>
                </div>
            </div>
            {{ form_close() }}
        </div>

    </div>

</div>
{% endblock %}