{% extends "admin/html.twig" %}
{% block body %}

<style>
    @media (max-width: 576px){

        .modal-body{
            overflow-x: auto;
        }

        .table{
           width: max-content !important;
        }

    }

    .multiselect {
  width: 100%;
}

.selectBox {
  position: relative;
}

.selectBox select {
  width: 100%;
}

.overSelect {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

#mySelectOptions {
  display: none;
  max-height: 150px;
  overflow-y: scroll;
}

#mySelectOptions label {
  display: block;
  font-weight: normal;
  display: block;
  white-space: nowrap;
  min-height: 1.2em;
  background-color: #ffffff00;
  padding: 0.5rem 1rem;
  /* padding: .375rem 2.25rem .375rem .75rem; */
}

#mySelectOptions label:hover {
  background-color: #1e90ff;
}

.selected-options {
    -webkit-appearance: none;
    background-color: #f1f1f5;
    border: 2px solid transparent;
    border-radius: 16px;
    color: #44444f;
    font-family: <PERSON><PERSON>, -apple-system, BlinkMacSystemFont, Segoe UI, Oxygen, Ubuntu, Cantarell, Open Sans, Helvetica Neue, sans-serif;
    font-size: .875rem;
    font-weight: 400;
    letter-spacing: .1px;
    line-height: 1;
    margin: 0;
    padding: 1rem;
    transition: border .25s, background .25s;

    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}


.selected-item {
    border: 1px solid gray;
    border-radius: 4px;
    padding: 0.5rem 1.5rem 0.5rem 1rem;
    position: relative;
    display: inline-block;

}

.close-button {
    position: absolute; /* Konumlandırma için */
    right: 2px; /* Sağ köşeye hizala */
    top: 2px; /* Dikey merkezle */
    z-index: 10; /* Diğer elementlerin üstünde görünmesi için */
    cursor: pointer; /* İmleci göster */
    font-size: 20px;
}






</style>


    {{ form_model(item, {'route': ['admin.course.enrolment.save', {'id':item.id,'session_id':course_session.id}], 'method': 'POST'}) }}


    <div class="container mx-auto" id="translate">

        <div class="row form-modal">

            <div class="col-12 px-0 mx-auto">
                {% include "_messageBag.twig" %}
            </div>

            <div class="col-12 d-flex align-items-center mt-4">
                 <span class="iconify me-2" style="font-size: 26px; cursor: pointer;"  onclick="goBack()" data-icon="solar:round-arrow-left-outline" data-inline="false"></span>
                <div>
                    {% include 'admin/header.twig' with {'title':item.title ~ " "~trans('Kurs Kaydı')} %}
                </div>
            </div>

            {# Sol Taraf #}
            <div class="col-12">

                {# Basit program bilgileri #}
                <div class="col-12 form modal mt-4" spellcheck="false">
                    <div class="modal-body">
                        <div class="form-row">
                            <!-- <div class="form-control-wrapper">
                                <div class="form-label">
                                    {{ form_label('user_list', trans('Kullanıcı Listesi')) }}
                                </div>
                                <div class="form-input w-100">
                                    <select id="user_list" class="js-example-basic-single form-control" multiple="multiple" name="state">
                                        {% for select_user in userList %}
                                            {% if select_user.email not in enrolledEmails %}
                                                <option value="{{ select_user.email }}">
                                                {{ select_user.name ?? select_user.email }}
                                                </option>
                                            {% endif %}
                                        {% endfor %}
                                    </select>
                                </div>
                            </div> -->
                            <div class="form-group p-0">
                                <div class="form-label">
                                    {{ form_label('user_list', trans('Kullanıcı Listesi')) }}
                                </div>
                                <div id="myMultiselect" class="multiselect form-input">
                                    <div id="mySelectLabel" class="selectBox" onclick="toggleCheckboxArea()">
                                        <div class="selected-options"></div>
                                        <div class="overSelect"></div>
                                    </div>
                                    <div id="mySelectOptions" class="my-3" style="display: none;">
                                        {% for select_user in userList %}
                                            {% if select_user.email not in enrolledEmails %}
                                                <label for="checkbox_{{ loop.index }}">
                                                    <input type="checkbox" id="checkbox_{{ loop.index }}" onchange="checkboxStatusChange()" value="{{ select_user.email }}" class="user-checkbox" />
                                                    {{ select_user.name ?? select_user.email }}
                                                </label>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>



                        </div>
                        <div class="form-row">
                            <div class="form-control-wrapper">
                                <div class="form-label">
                                    <input type="checkbox" id="select-all" onclick="selectAllCheckboxes()" /> {{ trans('Hepsini Seç') }}
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-control-wrapper form-control-wrapper--is-required">
                                <div class="form-label">
                                    {{ form_label('email_list', trans('E-mail Listesi')) }}
                                </div>
                                <div class="form-input">
                                    {{ form_textarea('email_list',null, {'class': 'form-control' }) }}
                                </div>
                            </div>
                        </div>

                        {# Güncelleme / Oluşturma Butonu  #}
                        <div class="col-12" spellcheck="false">
                            <div class=" mt-4 py-0">
                                <div class="form-row">
                                    <div class="form-control-wrapper text-right">
                                        <button id="application-save-button" type="submit" class="custom-red-btn button me-0">{{ trans('Kaydet') }}</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% if enrolledUserList | length > 0 %}
                            {{ form_label('', trans('Kursa Kayıtlı Kullanıcılar')) }}
                        <div class="table">
                            <div class="table-header">
                            <div class="table-row table-row-head">
                                <div class="table-cell table-keep">#</div>
                                <div class="table-cell table-keep">{{ trans('İsim') }}</div>
                                <div class="table-cell table-keep">{{ trans('Email') }}</div>
                                <div class="table-cell table-keep">{{ trans('İşlemler') }}</div>
                            </div>
                            </div>
                            <div class="table-items">
                            {% for index,enrolledUser in enrolledUserList %}
                            <div class="table-row">
                                <div class="table-cell table-keep py-2">{{ index + 1 }}</div>
                                <div class="table-cell table-keep py-2">{{ enrolledUser.name }}</div>
                                <div class="table-cell table-keep py-2">{{ enrolledUser.email }}</div>
                                <div class="table-cell table-keep py-2"><button type="button" class="button button--red remove" data-url="{{ route('admin.course.enrolment.user.delete',{'course_id':item.id,'session_id':course_session.id,'user_id':enrolledUser.id}) }}">{{ trans('Çıkar') }}</button></div>
                            </div>
                            {% endfor %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>


            </div>



        </div>

    </div>

    {{ form_close() }}

    {% if app.session.get('message') %}
        {% include "admin/course/include/enrolled_message.twig" %}
    {% endif %}


{% endblock %}
{% block styles %}
    <style>
        .select2-container--default .select2-selection--multiple{
            -webkit-appearance: none;
            background-color: #f1f1f5!important;
            border: 2px solid transparent!important;
            border-radius: 16px!important;
            color: #44444f;
            font-family: Roboto, -apple-system, BlinkMacSystemFont, Segoe UI, Oxygen, Ubuntu, Cantarell, Open Sans, Helvetica Neue, sans-serif;
            font-size: .875rem;
            font-weight: 400;
            min-height: 56px!important;
            letter-spacing: .1px;
            line-height: 1;
            margin: 0;
            padding: 1rem!important;
            transition: border .25s, background .25s;
            width: 100%;
        }
        .select2-container--default .select2-selection--single .select2-selection__arrow{
            height: 56px !important;
        }
        .select2-container--default.select2-container--open.select2-container--below .select2-selection--single, .select2-container--default.select2-container--open.select2-container--below .select2-selection--multiple{
            border-bottom-left-radius: 0!important;
            border-bottom-right-radius: 0!important;
        }
    </style>
{% endblock %}

{% block scripts %}

    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>

    <script>
        $(document).ready(function() {
            $('#select-all').on('change', function() {
                if ($(this).is(':checked')) {
                    // Tüm seçenekleri textarea içine ekle
                    let allEmails = [];
                    $('#user_list option').each(function() {
                        $(this).prop('selected', true); // Tüm seçenekleri seçili yap
                        allEmails.push($(this).val()); // E-postaları listeye ekle
                    });
                    $('#email_list').val(allEmails.join('\n')); // Textarea'ya ekle
                    $('.js-example-basic-single').trigger('change'); // Select'i güncelle
                } else {
                    // Checkbox kaldırılırsa, textarea'dan tüm e-postaları sil
                    $('#email_list').val('');
                    $('#user_list option').each(function() {
                        $(this).prop('selected', false); // Seçimleri kaldır
                    });
                    $('.js-example-basic-single').trigger('change'); // Select'i güncelle
                }
            });
        });
    </script>


    <script>
        $(".remove").on('click',function (){
            var deleteBtn = $(this);
            swal({
                title: "{{ trans('Emin misiniz?') }}",
                text: "{{ trans('Bu kullanıcıyı kurstan çıkarırsanız kullanıcı eğitime erişemez!') }}",
                icon: "warning",
                buttons: true,
                dangerMode: true,
            })
                .then((willDelete) => {
                    if (willDelete) {
                        var url = deleteBtn.data('url');
                        $.ajax(
                            {
                                type: "DELETE",
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                                },
                                url: url,
                                success: function (data)
                                {
                                    if(data['status']) {
                                        swal("{{ trans('Kullanıcı kurstan çıkarıldı!') }}", {
                                            icon: "success",
                                        }) .then((willDelete) => {
                                            if (willDelete) {
                                                location.reload();
                                            }
                                        });
                                    }else{
                                        swal("{{ trans('Kullanıcı kurstan çıkarılırken hat oluştu!') }}", {
                                            icon: "error",
                                        });
                                    }
                                },
                            }
                        );

                    } else {
                        swal("{{ trans('Kullanıcı güvende!') }}");
                    }
                });

        });
    </script>

    <script>
        $(document).ready(function() {

            $('.js-example-basic-single').select2({
                templateSelection: formatState
            });

            $('.js-example-basic-single').on('select2:unselect', function (e) {
                var removedState = e.params.data;

                let emailList = $('#email_list').val().split('\n').filter(item => item.trim() !== '');

                emailList = emailList.filter(email => email !== removedState.id);

                $('#email_list').val(emailList.join('\n'));
            });
        });


        function formatState(state) {
            if (!state.id) {
                return state.text;
            }

            let emailList = $('#email_list').val().split('\n').filter(item => item.trim() !== '');

            if (state.selected) {
                if (!emailList.includes(state.id)) {
                    emailList.push(state.id);
                }
            } else {
                emailList = emailList.filter(email => email !== state.id);
            }

            $('#email_list').val(emailList.join('\n'));

            return state.text;
        };

    </script>

    <script>
        window.onload = (event) => {
  initMultiselect();
};

function initMultiselect() {
  checkboxStatusChange();

  document.addEventListener("click", function(evt) {
    var flyoutElement = document.getElementById('myMultiselect'),
      targetElement = evt.target; // clicked element

    do {
      if (targetElement == flyoutElement) {
        // This is a click inside. Do nothing, just return.
        //console.log('click inside');
        return;
      }

      // Go up the DOM
      targetElement = targetElement.parentNode;
    } while (targetElement);

    // This is a click outside.
    toggleCheckboxArea(true);
    //console.log('click outside');
  });
}

function checkboxStatusChange() {
    var selectedOptionsContainer = document.querySelector('.selected-options');
    selectedOptionsContainer.innerHTML = ''; // Önceki seçilenleri temizle

    var checkboxes = document.getElementById("mySelectOptions");
    var checkedCheckboxes = checkboxes.querySelectorAll('input[type=checkbox]:checked');

    // Her bir seçili checkbox için element oluştur
    checkedCheckboxes.forEach(item => {
        var checkboxLabel = item.nextSibling.nodeValue.trim(); // Label metni

        // Yeni bir span oluştur
        var optionElement = document.createElement('span');
        optionElement.textContent = checkboxLabel;
        optionElement.classList.add('selected-item');

        // Çarpı simgesi oluştur
        var closeButton = document.createElement('span');
        closeButton.innerHTML = '<span class="iconify" data-icon="material-symbols-light:close-small-rounded" data-inline="false"></span>'; // İkonu ekle
        closeButton.classList.add('close-button');
        closeButton.onclick = function(event) {
            event.stopPropagation(); // Tıklamanın yayılmasını engelle
            item.checked = false; // Checkbox'ı işaretini kaldır
            checkboxStatusChange(); // Güncellemeyi tetikle
        };

        // Çarpı butonunu span içine ekle
        optionElement.appendChild(closeButton);
        selectedOptionsContainer.appendChild(optionElement); // Seçenekleri göster
    });
    updateEmailList(); // E-posta listesini güncelle
    if (checkedCheckboxes.length === 0) {
        selectedOptionsContainer.innerHTML = 'Seçiniz'; // Varsayılan ifade
    }
}

        function updateEmailList() {
            // Mevcut email listesini al ve boş satırları filtrele
            let allEmails = $('#email_list').val().split('\n').filter(function(item) {
                return item.trim() !== ''; // Boş satırları çıkart
            });

            // Checkbox seçili olanları kontrol et
            $('.user-checkbox:checked').each(function() {
                let email = $(this).val();
                if (!allEmails.includes(email)) {
                    allEmails.push(email); // Eğer zaten yoksa, ekle
                }
            });

            // Seçimi kaldırılanları kontrol et
            $('.user-checkbox:not(:checked)').each(function() {
                let email = $(this).val();
                allEmails = allEmails.filter(function(item) {
                    return item !== email; // Eğer seçim kaldırılmışsa, email'i çıkar
                });
            });

            // Güncellenmiş email listesini textarea'ya yaz
            $('#email_list').val(allEmails.join('\n'));
        }

function toggleCheckboxArea(event) {
    var checkboxes = document.getElementById("mySelectOptions");
    checkboxes.style.display = checkboxes.style.display === "block" ? "none" : "block";
    updateEmailList(); // E-posta listesini güncelle
    event.stopPropagation(); // Dropdown'un açılmasını engelle
}





function toggleCheckboxArea(onlyHide = false) {
  var checkboxes = document.getElementById("mySelectOptions");
  var displayValue = checkboxes.style.display;

  if (displayValue != "block") {
    if (onlyHide == false) {
      checkboxes.style.display = "block";
    }
  } else {
    checkboxes.style.display = "none";
  }
}
    </script>

    <script>
        function selectAllCheckboxes() {
    var selectAllCheckbox = document.getElementById("select-all");
    var checkboxes = document.querySelectorAll("#mySelectOptions input[type=checkbox]");

    checkboxes.forEach(function(checkbox) {
        checkbox.checked = selectAllCheckbox.checked;

    });

    checkboxStatusChange(); // Durumu güncelle
}

        $(document).ready(function() {
            // Hepsini seç veya kaldır
            $('#select-all').on('change', function() {
                if ($(this).is(':checked')) {
                    $('.user-checkbox').each(function() {
                        $(this).prop('checked', true).trigger('change'); // Checkbox'ı işaretle ve değişikliği tetikle
                    });
                } else {
                    $('.user-checkbox').each(function() {
                        $(this).prop('checked', false).trigger('change'); // Checkbox'ı işaretini kaldır ve değişikliği tetikle
                    });
                }
            });
            // Checkbox seçim değişikliği
            $('.user-checkbox').on('change', function() {
                checkboxStatusChange();
                updateEmailList();
            });
        });
    </script>


{% endblock %}

