{% extends "admin/html.twig" %}
{% block body %}

<style>
     @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

    :root{
        --poppins: 'Poppins', sans-serif;
    }

    .custom-red-btn{
        background-color: #BA272D;
        border-radius: 10px;
        color: white;
        padding: 0.75rem 1rem;
        font-family: var(--poppins);
        font-size: 12px;
        font-weight: 500;
        letter-spacing: 0.3px;
        border: none;
        outline: none;
    }
    .custom-gray-btn{
        background-color: #F1F1F5;
        border-radius: 10px;
        border: none;
        outline: none;
        color: #696974;
        padding: 0.75rem 1rem;
        font-family: var(--poppins);
        font-size: 12px;
        font-weight: 500;
        letter-spacing: 0.3px;
        cursor: pointer;
    }

    #application-custom-questions:empty:before {
        content: "{{ trans('<PERSON><PERSON>') }}";
        text-align: center;
        display: block;
        font-size: 0.75rem;
        font-weight: 500; }

    .custom-red-btn:hover{
        background-color: #F1F1F5;
        color: #BA272D;
        border: none;
        outline: none;
    }

    .custom-gray-btn:hover{
        background-color: #696974;
        border: none;
        outline: none;
        color: #F1F1F5;
    }
    .modal{
        margin: 0;
    }

    .col-md-8{
        padding-left: 2rem;
    }
    .col-md-4{
        padding-right: 2rem;
    }
</style>

{% if item %}
    {{ form_model(item, {'route': ['admin.' ~ listing.resource ~ '.update', item.id], 'method': 'PUT'}) }}
{% else %}
    {{ form_model(null, {'route': ['admin.' ~ listing.resource ~ '.store']}) }}
{% endif %}

<div class="container mx-auto" id="translate">

    <div class="row form-modal">

        <div class="col-12 px-0 mx-auto">
            {% include "_messageBag.twig" %}
        </div>

        <div class="d-flex align-items-center mt-4">
            <span class="iconify me-2" style="font-size: 26px; cursor: pointer;"  onclick="goBack()" data-icon="solar:round-arrow-left-outline" data-inline="false"></span>
            <div >
                {% include 'admin/header.twig' with {'title': trans('Kurs Oluşturma')} %}
            </div>
        </div>

        {# Sol Taraf #}
        <div class="col-12 col-md-8">

            {# Basit program bilgileri #}
            <div class="col-12 form modal mt-4" spellcheck="false">
                <div class="modal-header">
                    <h5>{{ trans('Kurs detayları') }}</h5>
                </div>
                <div class="modal-body">
                    <div class="form-row">
                        <div class="form-control-wrapper form-control-wrapper--is-required">
                            <div class="form-label">
                                {{ form_label('title', trans('Kurs Adı')) }}
                            </div>
                            <div class="form-input">
                                {{ form_text('title', null, {'class': 'form-control','required':true }) }}
                            </div>
                        </div>
                    </div>
                    <div id="main-container">
                        {% for course_session in item.sessions %}
                            <div class="container-item mt-3">
                                {{ form_hidden('course_session_id_0',course_session.id,{'name':'course_sessions[0][id]'}) }}
                                <div class="form-row">
                                    <div class="form-control-wrapper form-control-wrapper--is-required">
                                        <div class="form-label">
                                            {{ form_label('zoom_link_0', trans('Kurs Linki')) }}
                                        </div>
                                        <div class="form-input">
                                            {{ form_text('zoom_link_0', course_session.zoom_link, {'class': 'form-control','required':true,'name':'course_sessions[0][zoom_link]' }) }}
                                        </div>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-control-wrapper form-control-wrapper--is-required">
                                        <div class="form-label">
                                            {{ form_label('quota_0', trans('Kurs Kontenjan')) }}
                                        </div>
                                        <div class="form-input">
                                            {{ form_number('quota_0', course_session.quota, {'class': 'form-control' ,'required':true,'name':'course_sessions[0][quota]'}) }}
                                        </div>
                                    </div>
                                    <div class="form-control-wrapper form-control-wrapper--is-required">
                                        <div class="form-label">
                                            {{ form_label('date_0', trans('Kurs Tarih')) }}
                                        </div>
                                        <div class="form-input">
                                            {{ form_text('date_0', course_session.date | date('d-m-Y'), {'class': 'form-control datetimepicker', 'required':true, 'data-time':'false','data-dateformat': 'd-m-Y','data-nocalendar':'false','name':'course_sessions[0][date]'}) }}
                                        </div>

                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-control-wrapper form-control-wrapper--is-required">
                                        <div class="form-label">
                                            {{ form_label('start_time_0', trans('Başlangıç Saati')) }}
                                        </div>
                                        <div class="form-input">
                                            {{ form_text('start_time_0', course_session.start_time, {'class': 'form-control datetimepicker', 'required':true,'data-time': 'true','data-nocalendar':'true', 'data-dateformat': 'H:i','name':'course_sessions[0][start_time]'}) }}
                                        </div>
                                    </div>
                                    <div class="form-control-wrapper form-control-wrapper--is-required">
                                        <div class="form-label">
                                            {{ form_label('end_time_0', trans('Bitiş Saati')) }}
                                        </div>
                                        <div class="form-input">
                                            {{ form_text('end_time_0', course_session.end_time, {'class': 'form-control datetimepicker', 'required':true,'data-time': 'true','data-nocalendar':'true', 'data-dateformat': 'H:i','name':'course_sessions[0][end_time]'}) }}
                                        </div>

                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-control-wrapper form-control-wrapper--is-required">
                                        <div class="form-label">
                                            {{ form_label('session', trans('Yayımla')) }}
                                        </div>
                                        <div class="form-input">
                                            <label class="radio">
                                                <input type="radio"
                                                       name="course_sessions[0][status]"
                                                       value="{{ constant('App\\Enums\\CourseStatus::PUBLISHED') }}"
                                                       required 
                                                    {% if course_session.status == constant('App\\Enums\\CourseStatus::PUBLISHED') %}
                                                        checked
                                                    {% endif %}
                                                >
                                                <div class="radio-control">
                                                    <span class="radio-tick"></span>
                                                    <span class="radio-text">{{ trans('Göster') }}</span>
                                                </div>
                                            </label>
                                            <label class="radio ms-3">
                                                <input type="radio"
                                                       name="course_sessions[0][status]"
                                                       value="{{ constant('App\\Enums\\CourseStatus::CLOSED') }}"
                                                       required
                                                    {% if course_session.status == constant('App\\Enums\\CourseStatus::CLOSED') %}
                                                        checked
                                                    {% endif %}
                                                >
                                                <div class="radio-control">
                                                    <span class="radio-tick"></span>
                                                    <span class="radio-text">{{ trans('Gizle') }}</span>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <a href="javascript:void(0)" class="remove-item custom-red-btn button me-0">{{ trans('Tarihi Sil') }}</a>
                            </div>
                        {% else %}
                            <div class="container-item mt-3">
                                {{ form_hidden('course_session_id_0',null,{'name':'course_sessions[0][id]'}) }}
                                <div class="form-row">
                                    <div class="form-control-wrapper form-control-wrapper--is-required">
                                        <div class="form-label">
                                            {{ form_label('zoom_link_0', trans('Kurs Linki')) }}
                                        </div>
                                        <div class="form-input">
                                            {{ form_text('zoom_link_0', null, {'class': 'form-control','required':true,'name':'course_sessions[0][zoom_link]' }) }}
                                        </div>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-control-wrapper form-control-wrapper--is-required">
                                        <div class="form-label">
                                            {{ form_label('quota_0', trans('Kurs Kontenjan')) }}
                                        </div>
                                        <div class="form-input">
                                            {{ form_number('quota_0', null, {'class': 'form-control' ,'required':true,'name':'course_sessions[0][quota]'}) }}
                                        </div>
                                    </div>
                                    <div class="form-control-wrapper form-control-wrapper--is-required">
                                        <div class="form-label">
                                            {{ form_label('date_0', trans('Kurs Tarih')) }}
                                        </div>
                                        <div class="form-input">
                                            {{ form_text('date_0', null, {'class': 'form-control datetimepicker', 'required':true, 'data-time': 'false','data-dateformat': 'Y-m-d','data-nocalendar':'false','name':'course_sessions[0][date]'}) }}
                                        </div>

                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-control-wrapper form-control-wrapper--is-required">
                                        <div class="form-label">
                                            {{ form_label('start_time_0', trans('Başlangıç Saati')) }}
                                        </div>
                                        <div class="form-input">
                                            {{ form_text('start_time_0', null, {'class': 'form-control datetimepicker', 'required':true,'data-time': 'true','data-nocalendar':'true', 'data-dateformat': 'H:i','name':'course_sessions[0][start_time]'}) }}
                                        </div>
                                    </div>
                                    <div class="form-control-wrapper form-control-wrapper--is-required">
                                        <div class="form-label">
                                            {{ form_label('end_time_0', trans('Bitiş Saati')) }}
                                        </div>
                                        <div class="form-input">
                                            {{ form_text('end_time_0', null, {'class': 'form-control datetimepicker', 'required':true,'data-time': 'true','data-nocalendar':'true', 'data-dateformat': 'H:i','name':'course_sessions[0][end_time]'}) }}
                                        </div>

                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-control-wrapper form-control-wrapper--is-required">
                                        <div class="form-label">
                                            {{ form_label('session', trans('Yayımla')) }}
                                        </div>
                                        <div class="form-input">
                                            <label class="radio">
                                                <input type="radio"
                                                       name="course_sessions[0][status]"
                                                       value="{{ constant('App\\Enums\\CourseStatus::PUBLISHED') }}"
                                                       required
                                                >
                                                <div class="radio-control">
                                                    <span class="radio-tick"></span>
                                                    <span class="radio-text">{{ trans('Göster') }}</span>
                                                </div>
                                            </label>
                                            <label class="radio ms-3">
                                                <input type="radio"
                                                       name="course_sessions[0][status]"
                                                       value="{{ constant('App\\Enums\\CourseStatus::CLOSED') }}"
                                                       required
                                                >
                                                <div class="radio-control">
                                                    <span class="radio-tick"></span>
                                                    <span class="radio-text">{{ trans('Gizle') }}</span>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <a href="javascript:void(0)" class="remove-item custom-red-btn button me-0">{{ trans('Tarihi Sil') }}</a>
                            </div>
                        {% endfor %}
                    </div>
                    <div class="text-right">
                        <a id="add-more" class="button" href="javascript:void(0);">{{ trans('Yeni Tarih Ekle') }}</a>
                    </div>
                    <div class="form-row">
                        <div class="form-control-wrapper form-control-wrapper--is-required">
                            <div class="form-label">
                                {{ form_label('educator_id', trans('Eğitmen')) }}
                            </div>
                            <div class="form-input">
                                {{ form_select('educator_id',educatorList, item.educator_id, {'class': 'form-control','placeholder' : trans('Eğitmen seçiniz')}) }}
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-control-wrapper">
                            <div class="form-label">
                                {{ form_label('content', trans('Açıklama')) }}
                            </div>
                            <div class="form-input">
                                {{ form_textarea('description', null, {'class': 'editor tinymce d-none'}) }}
                            </div>
                        </div>
                    </div>

                    {# Güncelleme / Oluşturma Butonu  #}
                    <div class="col-12" spellcheck="false">
                        <div class=" mt-4 py-0">
                            <div class="form-row">
                                <div class="form-control-wrapper text-right">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            {% if item %}
                                            <a href="{{ route('admin.course.copy', item.id) }}" class="custom-gray-btn button" style="height: 38px;">{{ trans('Kursu Çoğalt') }}</a>
                                            {% endif %}
                                        </div>
                                        <div class="d-flex align-items-center">
                                            {% if item and listing.actions.enabled %}
                                                <div class="actions me-2">
                                                    <button type="button" data-options="{{ listing.actions.option }}" class="button button--transparent" style="height: 38px; width: 38px; padding: 0; display: flex; align-items: center; justify-content: center;">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewbox="0 0 24 24">
                                                            <path fill="none" fill-rule="evenodd" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 2.75c5.1 0 9.25 4.14 9.25 9.24 0 5.1-4.15 9.25-9.25 9.25 -5.11 0-9.26-4.15-9.26-9.25 0-5.11 4.14-9.25 9.25-9.25Z"></path>
                                                            <path fill="currentColor" d="M7.52 13.19c-.67 0-1.2-.54-1.2-1.2 0-.66.53-1.2 1.19-1.2 .66 0 1.19.53 1.19 1.19 0 .66-.54 1.19-1.2 1.19Z"></path>
                                                            <path fill="currentColor" d="M12 13.19c-.67 0-1.2-.54-1.2-1.2 0-.66.53-1.2 1.19-1.2 .66 0 1.19.53 1.19 1.19 0 .66-.54 1.19-1.2 1.19Z"></path>
                                                            <path fill="currentColor" d="M16.47 13.19c-.67 0-1.2-.54-1.2-1.2 0-.66.53-1.2 1.19-1.2 .66 0 1.19.53 1.19 1.19 0 .66-.54 1.19-1.2 1.19Z"></path>
                                                        </svg>
                                                    </button>
                                                </div>
                                            {% endif %}
                                            <button id="application-save-button" type="submit" class="custom-red-btn button" style="height: 38px;">{{ trans('Kaydet') }}</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>



        </div>

        {# Sağ Taraf Kısım #}
        <div class="col-12 col-md-4">

            {# Kurs Yayın Durumu #}
            <div class="col-12 form modal mt-4">
                <div class="modal-header">
                    <h5>{{ trans('Yayımla') }}</h5>
                </div>
                <div class="modal-body">
                    <div class="form-row">
                        <div class="form-control-wrapper">
                            <div class="form-label">
                                {{ form_label('status', 'Status') }}
                            </div>
                            <div class="form-input">
                                <select name="status" id="status" class="form-control" required>
                                    <option value="{{ constant('App\\Enums\\CourseStatus::CLOSED') }}" {% if item.status == constant('App\\Enums\\CourseStatus::CLOSED') %} selected="selected" {% endif %}>{{ trans('Taslak') }}</option>
                                    <option value="{{ constant('App\\Enums\\CourseStatus::PUBLISHED') }}" {% if item.status == constant('App\\Enums\\CourseStatus::PUBLISHED') %} selected="selected" {% endif %}>{{ trans('Yayında') }}</option>
                                </select>
                            </div>
                            <div class="form-description">
                                <p>{{ trans('Bu kursun yayınlanma durumunu belirtin') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {# Kurs Kategorileri #}
            <div class="col-12 form modal mt-4">
                <div class="modal-header">
                    <h5>{{ trans('Kategori') }}</h5>
                </div>
                <div class="modal-body py-2 pb-4">
                    <div class="form-row">
                        <div class="form-control-wrapper">
                            <div class="form-input">
                                {% for category in categories %}
                                    <div class="my-2">
                                        <label class="checkbox">
                                            <input type="checkbox"
                                                   name="categories[]"
                                                   value="{{ category.id }}"
                                                {% if category.id in item.categories.pluck('title', 'id').toArray()|keys %}
                                                    checked
                                                {% endif %}
                                            >
                                            <div class="checkbox-control">
                                                <span class="checkbox-tick"></span>
                                                <span class="checkbox-text">{{ category.title }}</span>
                                            </div>
                                        </label>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                        {# <div class="form-control-wrapper">
                            <div class="form-input">
                                <select name="category_id" id="user_group" class="form-control" required>
                                    <option value="" disabled selected>
                                        {{ trans('Kategori Seçiniz') }}
                                    </option>
                                    {% for category in categories %}
                                        <option value="{{ category.id }}" {{ item.category_id == category.id ? 'selected' : '' }}>{{ category.title }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>#}
                    </div>
                </div>
            </div>

            {# Kurs Tarihi #}
            {# <div class="col-12 form modal mt-4">
                <div class="modal-header">
                    <h5>{{ trans('Tarih') }}</h5>
                </div>
                <div class="modal-body py-2 pb-4">
                    <div class="form-row">
                        <div class="form-control-wrapper">
                            <div class="form-input">
                                {{ form_text('date', null, {'class': 'form-control datetimepicker', 'data-time': 'true', 'data-dateformat': 'Y-m-d H:i'}) }}
                            </div>

                        </div>
                    </div>
                </div>
            </div>#}

            {# Kurs Görselleri #}
            <div class="col-12 form modal mt-4">
                <div class="modal-header">
                    <h5>{{ trans('Medya / Görseller') }}</h5>
                </div>
                <div class="modal-body">
                    <p class="fw-500 fs-md mb-2">{{ trans('Kapak Fotoğrafı') }} <span class="fs-sm">(5:3)</span></p>
                    <input name="image" type="hidden" value="{{ item.image_id }}">
                    <div class="form-media-items form-media--single {{ item.image ? 'form-media-items--has-image' }}" ar="5:3">
                        {% if item.image %}
                            <div class="form-media-item" data-media-hash="{{ item.image.hash }}" data-image-url="{{ item.image.url }}" data-media-id="{{ item.image.id }}">
                                <div class="form-media-item-overlay ">
                                    <button type="button" class="form-media-item-remove button button--transparent">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                                <div class="form-media-item-content">
                                    <img src="{{ item.image.url }}">
                                </div>
                            </div>
                        {% endif %}
                        <button type="button" class="form-media-item-add">
                            <div class="form-media-item-content">
                                <span class="click-text">{{ trans('Yeni görsel ekle') }}</span>
                                <span class="drop-text">{{ trans('Görselleri buraya bırakın') }}</span>
                            </div>
                        </button>
                    </div>
                </div>
            </div>

        </div>


    </div>

</div>

{{ form_close() }}

    {# Kaynak destroy methoduna sahipse bunun için form oluştur #}
    {% if item and listing.actions.enabled and route_has('admin.' ~ listing.resource ~ '.destroy') %}
        {{ form_model(item, {'route': ['admin.' ~ listing.resource ~ '.destroy', item.id], 'method': 'DELETE', 'class': 'form-destroy-item'}) }}
        {{ form_close() }}
    {% endif %}

    <script src="{{ url('js/cloneData.js') }}"></script>

<script>

    let isApplicableCheckbox = document.querySelector('[name="is_applicable"]');
    if (isApplicableCheckbox) {

        document.body.classList.toggle('is-applicable', isApplicableCheckbox.checked);

        isApplicableCheckbox.addEventListener('change', function() {
            document.body.classList.toggle('is-applicable', this.checked);
        });
    }

    $(document).ready(function (){
        if($('.datetimepicker').length > 0){
            $('.datetimepicker').each((function(e,v) {

                var t = {
                    enableTime: $(this).data("time"),
                    dateFormat: "Y-m-d H:i",
                    minuteIncrement: 30,
                    time_24hr: true,
                    defaultHour: 0,
                    monthSelectorType: "static",
                    noCalendar : $(this).data("nocalendar"),
                    onChange: function (t, n, o) {
                        if ($(this).data("onchange")) {
                            var r = $(this).data("onchange");
                            __dtHandler[r](t, n, o)
                        }
                    },
                    onOpen: function (t, n, o) {
                        if ($(this).data("onopen")) {
                            var r = $(this).data("onopen");
                            __dtHandler[r](t, n, o)
                        }
                    }
                };

                $(this).data("mode") && (t.mode = $(this).data("mode")),
                $(this).data("dateformat") && (t.dateFormat = $(this).data("dateformat")),
                $(this).data("mindate") && (t.minDate = $(this).data("mindate")),
                    flatpickr($(this), t);
            }));
        }
    });


    $('#add-more').cloneData({

        // container to hold the dulicated form fields
        mainContainerId: 'main-container',

        // Which you want to clone
        cloneContainer: 'container-item',

        // CSS lcass of remove button
        removeButtonClass: 'remove-item', // Remove button for remove cloned HTML
        removeConfirm: true, // default true confirm before delete clone item
        removeConfirmMessage: '{{ trans('Silmek istediğinizden emin misiniz?') }}', // confirm delete message
        afterRender: function (){
            if($('.datetimepicker').length > 0){
                $('.datetimepicker').each((function(e,v) {

                    var t = {
                        enableTime: $(this).data("time"),
                        dateFormat: "Y-m-d H:i",
                        minuteIncrement: 30,
                        time_24hr: true,
                        defaultHour: 0,
                        monthSelectorType: "static",
                        noCalendar : $(this).data("nocalendar"),
                        onChange: function (t, n, o) {
                            if ($(this).data("onchange")) {
                                var r = $(this).data("onchange");
                                __dtHandler[r](t, n, o)
                            }
                        },
                        onOpen: function (t, n, o) {
                            if ($(this).data("onopen")) {
                                var r = $(this).data("onopen");
                                __dtHandler[r](t, n, o)
                            }
                        }
                    };

                    $(this).data("mode") && (t.mode = $(this).data("mode")),
                    $(this).data("dateformat") && (t.dateFormat = $(this).data("dateformat")),
                    $(this).data("mindate") && (t.minDate = $(this).data("mindate")),
                    flatpickr($(this), t);
                }));
            }
        }

    });

</script>


{% endblock %}
