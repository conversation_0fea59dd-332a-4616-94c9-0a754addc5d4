{% extends "admin/html.twig" %}
{% block body %}

    <style>
        .custom-add-new{
            cursor: pointer;
            outline: none;
            border: 1px solid white;
            padding: 0.75rem 2rem;
            display: flex;
            align-items: center;
            color: white;
            background-color: rgba(186, 39, 45, 1);
            border-radius: 8px;
            transition: all 0.3s ease-in-out;
        }

        .custom-add-new:hover{
            border: 1px solid rgba(186, 39, 45, 1);
            background-color: white;
            color: rgba(186, 39, 45, 1);
        }

        .custom-add-new span{
            font-family: var(--roboto);
            font-size: 12px;
            font-weight: 400;
            color: white;
            margin-left: 0.25rem;
        }
    </style>
    <div class="container mx-auto">
        <div class="row form-modal">
            <div class="row">
                <div class="col-12 mx-auto">
                    {% include "_messageBag.twig" %}
                </div>
            </div>

            {% if item %}
                {{ form_model(item, {'route': ['admin.' ~ listing.resource ~ '.update', item.id], 'method': 'PUT'}) }}
            {% else %}
                {{ form_model(null, {'route': ['admin.' ~ listing.resource ~ '.store']}) }}
            {% endif %}
            {# Başlık #}
            <div class="col-12">
                <div class="row">
                    <div class="col-12">
                        <div class="py-3">
                            <div class="modal-header">
                                <div class="d-flex align-items-center">
                                 <span class="iconify me-2" style="font-size: 26px; cursor: pointer;"  onclick="goBack()" data-icon="solar:round-arrow-left-outline" data-inline="false"></span>
                                    <h4>{{ listing.title ? listing.title ~ ' ' }}{{ item ? trans('Düzenle') : trans('Oluştur') }}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-12">

                <div class="row">

                    {# Sol taraf #}
                    <div class="col-12 col-md-8" spellcheck="false">

                        <div class="col-12 form modal mt-4" spellcheck="false">
                            <div class="modal-body">
                                <div class="form-row">
                                    <div class="form-control-wrapper">
                                        <div class="form-label">
                                            {{ form_label('banner_title', trans('Başlık')) }}
                                        </div>
                                        <div class="form-input">
                                            {{ form_text('banner_title', null, {'class': 'form-control' }) }}
                                        </div>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-control-wrapper">
                                        <div class="form-label">
                                            {{ form_label('banner_subtitle', trans('Kısa Açıklama')) }}
                                        </div>
                                        <div class="form-input">
                                            {{ form_text('banner_subtitle', null, {'class': 'form-control' }) }}
                                        </div>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-control-wrapper">
                                        <div class="form-label">
                                            {{ form_label('banner_url', trans('Yönlendirme')) }}
                                        </div>
                                        <div class="form-input">
                                            {{ form_text('banner_url', null, {'class': 'form-control' }) }}
                                        </div>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-control-wrapper">
                                        <div class="form-label">
                                            {{ form_label('banner_order', trans('Sıralama')) }}
                                        </div>
                                        <div class="form-input">
                                            {{ form_number('banner_order', null, {'class': 'form-control','min':1 }) }}
                                        </div>
                                    </div>
                                </div>

                                {# Güncelleme / Oluşturma Butonu  #}
                                <div class="col-12" spellcheck="false">
                                    <div class=" mt-4 py-0">
                                        <div class="form-row">
                                            <div class="form-control-wrapper text-right">
                                                {# Kaynak nesne aksiyonlarını destekliyorsa ve eğer varsayılan aksiyon seçili ise (silme) destekliyorsa seçenekler (aksiyonlar) menüsünü oluştur #}
                                                {% if item and listing.actions.enabled %}
                                                    <div class="actions">
                                                        <button type="button" data-options="{{ listing.actions.option }}" class="button button--big button--transparent">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewbox="0 0 24 24">
                                                                <path fill="none" fill-rule="evenodd" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 2.75c5.1 0 9.25 4.14 9.25 9.24 0 5.1-4.15 9.25-9.25 9.25 -5.11 0-9.26-4.15-9.26-9.25 0-5.11 4.14-9.25 9.25-9.25Z"></path>
                                                                <path fill="currentColor" d="M7.52 13.19c-.67 0-1.2-.54-1.2-1.2 0-.66.53-1.2 1.19-1.2 .66 0 1.19.53 1.19 1.19 0 .66-.54 1.19-1.2 1.19Z"></path>
                                                                <path fill="currentColor" d="M12 13.19c-.67 0-1.2-.54-1.2-1.2 0-.66.53-1.2 1.19-1.2 .66 0 1.19.53 1.19 1.19 0 .66-.54 1.19-1.2 1.19Z"></path>
                                                                <path fill="currentColor" d="M16.47 13.19c-.67 0-1.2-.54-1.2-1.2 0-.66.53-1.2 1.19-1.2 .66 0 1.19.53 1.19 1.19 0 .66-.54 1.19-1.2 1.19Z"></path>
                                                            </svg>
                                                        </button>
                                                    </div>
                                                {% endif %}
                                                <button id="application-save-button" type="submit" class="custom-red-btn button me-0">{{ trans('Kaydet') }}</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>

                    </div>

                    {# Sağ taraf #}
                    <div class="col-12 col-md-4 mt-4">
                        <div class="modal">
                            <div class="modal-header">
                                <h5>{{ trans('Yayımla') }}</h5>
                            </div>
                            <div class="modal-body">
                                <div class="form-row">
                                    <div class="form-control-wrapper">
                                        <div class="form-label">
                                            {{ form_label('banner_status', 'Status') }}
                                        </div>
                                        <div class="form-input">
                                            <select name="banner_status" id="banner_status" class="form-control">
                                                <option value="0" {% if item.banner_status == '0' %} selected="selected" {% endif %}>{{ trans('Taslak') }}</option>
                                                <option value="1" {% if item.banner_status == '1' %} selected="selected" {% endif %}>{{ trans('Yayında') }}</option>
                                            </select>
                                        </div>
                                        <div class="form-description">
                                            <p>{{ trans('Bu bannerın yayınlanma durumunu belirtin') }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>

                        <div class="modal mt-4">
                            <div class="modal-header">
                                <h5>{{ trans('Medya / Görseller') }}</h5>
                            </div>
                            <div class="modal-body">
                                <p class="fw-500 fs-md mb-2">{{ trans('Kapak Fotoğrafı') }}
                                    <span class="fs-sm">(5:3)</span></p>
                                <input name="banner_image" type="hidden" value="{{ item.banner_image_id }}">
                                <div class="form-media-items form-media--single {{ item.banner_image ? 'form-media-items--has-image' }}" ar="5:3">
                                    {% if item.banner_image %}
                                        <div class="form-media-item" data-media-hash="{{ item.banner_image.hash }}" data-image-url="{{ item.banner_image.url }}" data-media-id="{{ item.banner_image.id }}">
                                            <div class="form-media-item-overlay ">
                                                <button type="button" class="form-media-item-remove button button--transparent">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewbox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                    </svg>
                                                </button>
                                            </div>
                                            <div class="form-media-item-content">
                                                <img src="{{ item.banner_image.url }}">
                                            </div>
                                        </div>
                                    {% endif %}
                                    <button type="button" class="form-media-item-add">
                                        <div class="form-media-item-content">
                                            <span class="click-text">{{ trans('Yeni görsel ekle') }}</span>
                                            <span class="drop-text">{{ trans('Görselleri buraya bırakın') }}</span>
                                        </div>
                                    </button>
                                </div>
                            </div>
                        </div>

                    </div>

                </div>

            </div>
            {{ form_close() }}
            {# Kaynak destroy methoduna sahipse bunun için form oluştur #}
            {% if item and listing.actions.enabled and route_has('admin.' ~ listing.resource ~ '.destroy') %}
                {{ form_model(item, {'route': ['admin.' ~ listing.resource ~ '.destroy', item.id], 'method': 'DELETE', 'class': 'form-destroy-item'}) }}
                {{ form_close() }}
            {% endif %}

        </div>
        {# {% block footer %}{% endblock %} #}
    </div>

    {# {% if item and listing.actions.enabled and route_has('admin.' ~ listing.resource ~ '.destroy') %}
{{ form_model(item, {'route': ['admin.' ~ listing.resource ~ '.destroy', item.id], 'method': 'DELETE', 'class': 'form-destroy-item'}) }}
{{ form_close() }}
{% endif %} #}

{% endblock %}
