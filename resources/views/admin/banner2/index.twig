{% extends "admin/html.twig" %}
{% block body %}
    <style>
  @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap');
  @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700;900&display=swap');

  :root{
          --roboto: 'Roboto', sans-serif;
          --poppins: 'Poppins', sans-serif;
    }

        @media (max-width: 991px){
        .educators-table{
            overflow-y: auto !important;
        }
        .user-image{
          border-radius: 50%;
          width: 40px !important;
          height: 40px !important;
        }

        .custom-search-bar input{
          width: 100px !important;
        }
        .custom-add-new{
          margin-top: 0.75rem !important;
        }
    }


  h4{
    font-family: var(--poppins);
    font-size: 20px;
    font-weight: 500;
    letter-spacing: 0.3px;
    margin-top: 3rem;
  }
  .custom-search-bar{
    background-color: rgba(241, 241, 245, 1);
    border-radius: 1px;
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    margin-top: 0.75rem;
    max-width: 300px;
  }

  .custom-search-bar input{
    background-color: transparent;
    outline: none;
    border: none;
    margin-left: 0.5rem;
  }

  .custom-search-bar input::placeholder{
    font-family: var(--roboto);
    font-size: 12px;
    font-weight: 400;
    color: rgba(105, 105, 116, 1);
  }

        .user-image{
          border-radius: 50%;
          width: 60px;
          height: 60px;
        }

        .user-image img{
          border-radius: 50%;
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .admin-grid h6{
          font-family: var(--roboto);
          font-size: 15px;
          font-weight: 500;
          color: rgba(38, 70, 83, 1);
          text-align: center;
        }

        .admin-grid small{
          border-radius: 4px;
          padding: 4px 6px 4px 6px;
          font-weight: 300;
          font-family: var(--roboto);
          margin-block: 0.75rem;
          text-align: center;
        }

        .admin-grid button{
          border-radius: 3px;
          background-color: rgba(38, 45, 51, 1);
          color: white;
          font-family: var(--roboto);
          font-weight: 400;
          cursor: pointer;
          padding: 10px;
          text-align: center;
          transition: all 0.3s ease-in-out;
          border: 1px solid transparent;
        }

        .admin-grid button:hover{
          background-color: transparent;
          border: 1px solid rgba(38, 45, 51, 1);
          color: rgba(38, 45, 51, 1);
        }


    .custom-add-new{
        cursor: pointer;
        outline: none;
        border: 1px solid white;
        padding: 0.75rem 1rem;
        display: flex;
        align-items: center;
        background-color: rgba(186, 39, 45, 1);
        border-radius: 8px;
        transition: all 0.3s ease-in-out;
    }

    .custom-add-new:hover{
        border: 1px solid rgba(186, 39, 45, 1);
        background-color: white;
    }

    .custom-add-new:hover span, .custom-add-new:hover .iconify{
        color: rgba(186, 39, 45, 1);
    }


    .custom-add-new span{
        font-family: var(--roboto);
        font-size: 12px;
        font-weight: 400;
        color: white;
        text-decoration: underline;
        margin-left: 0.25rem;
    }

    .custom-add-new .iconify{
        font-size: 18px;
        color: white;
    }

    table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    font-size: 18px;
    text-align: left;
}

th, td {
    padding: 12px;
}

table tr td{
  vertical-align: middle;
}

th{
  color: rgba(146, 146, 157, 1);
  font-family: var(--roboto);
  font-size: 14px;
  font-weight: 500;
}

td{
  font-family: var(--roboto);
  font-size: 14px;
  font-weight: 500;
  color: rgba(38, 45, 51, 1);
}

.pagination{
  display: flex;
  justify-content: center;
  margin-block: 1rem;
}

.pagination-list .before-after{
  font-family: var(--roboto);
  font-size: 14px;

}

.pagination-list .page-numbers{
  margin-inline: 0.5rem;
  padding: 0.35rem 0.6rem;
  font-size: 14px;
  border-radius: 4px;
}

.pagination .page-numbers.active{
  background-color: rgba(186, 39, 45, 1) !important;
}

.custom-search-result{
  padding: 0.4rem 1rem;
    background-color: rgba(241, 241, 245, 1);
    border-radius: 1px;
    display: flex;
    align-items: center;
    margin-top: 0.75rem;
    max-width: 300px;
    margin-left: 1rem;
}
.custom-search-result span{
  font-family: var(--roboto);
  font-size: 14px;
  font-weight: 500;
  color: black;
  display: flex;
  align-items: center;
}

.custom-search-result .iconify{
  color: rgba(186, 39, 45, 1);
  cursor: pointer;
  font-size: 20px;
  margin-top: 0.1rem;
}

.custom-search-result-name{
  background-color: rgb(215, 215, 215);
  padding: 0.15rem 0.75rem;
  font-size: 13px;
}
</style>

  <div class="container">
    <h4> {{trans('Bannerler')}} </h4>
    <div class="d-flex align-items-center justify-content-between">
      <div class="d-flex align-items-center">
      </div>
          <a href="/admin/banner/create">
              <button class="custom-add-new">
                  <span class="iconify" data-icon="material-symbols:add-rounded" data-inline="false"></span>
                  <span> {{trans('Yeni Ekle')}} </span>
              </button>
          </a>
      </div>
      <div class="educators-table">
        <table>

          <thead>
              <tr>
                  <th> {{trans('ID')}} </th>
                  <th> {{trans('Başlık')}} </th>
                  <th> {{trans('Alt Başlık')}} </th>
                  <th> {{trans('Banner')}} </th>
                  <th> {{trans('Durumu')}} </th>
                  <th> {{trans('Sıralama')}} </th>
                  <th> {{trans('Eklenme Tarihi')}} </th>
                  <th> {{trans('İşlemler')}} </th>
              </tr>
          </thead>
          <tbody>
            {% for banner in banners %}
                <tr>
                  <td>  {{banner.id}} </td>
                  <td>  {{banner.banner_title}} </td>
                  <td>  {{banner.banner_subtitle}} </td>
                  <td> <img src="{{ banner.banner_image }}" style="height: 150px;"> </td>
                  <td>
                    {% if banner.banner_status == 1 %}
                      <span> Aktif </span>
                    {% else %}
                      <span> Pasif </span>
                    {% endif %}
                  </td>
                  <td> {{banner.banner_order }}</td>               
                  <td> {{banner.created_at }} </td>
                    <td>
                        <a href="/admin/banner/{{banner.id}}/edit">
                        <button class="btn btn-primary" style="background: transparent; border: none;">
                            <span class="iconify" data-icon="material-symbols:edit-square-outline" data-inline="false" style="font-size:16px"></span>
                        </button>
                        </a>
                        <form method="POST" action="/admin/banner/{{banner.id}}" style="display: inline-block;">
                            <input type="hidden" name="_method" value="DELETE">
                            <input type="hidden" name="_token" value="{{ csrf_token() }}">
                            <button type="submit" class="btn btn-danger" style="background: transparent; border: none;">
                                <span class="iconify" data-icon="material-symbols:delete-outline" data-inline="false" style="font-size:16px;color:rgba(186, 39, 45, 1)"></span>
                            </button>
                        </form>
                    </td>

                </tr>
            {% endfor %}
          </tbody>
      </table>
      </div>
  </div>
{% endblock %}