{% extends "admin/html.twig" %}
{% block body %}
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700;900&display=swap');

        :root{
            --roboto: 'Roboto', sans-serif;
            --poppins: 'Poppins', sans-serif;
        }

        @media (max-width: 991px){
            .educators-table{
                overflow-y: auto !important;
            }
            .user-image{
                border-radius: 50%;
                width: 40px !important;
                height: 40px !important;
            }

            .custom-search-bar input{
                width: 100px !important;
            }
            .custom-add-new{
                margin-top: 0.75rem !important;
            }
        }

        h4{
            font-family: var(--poppins);
            font-size: 20px;
            font-weight: 500;
            letter-spacing: 0.3px;
            margin-top: 3rem;
        }
        .custom-search-bar{
            background-color: rgba(241, 241, 245, 1);
            border-radius: 1px;
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            margin-top: 0.75rem;
            max-width: 300px;
        }

        .custom-search-bar input{
            background-color: transparent;
            outline: none;
            border: none;
            margin-left: 0.5rem;
        }

        .custom-search-bar input::placeholder{
            font-family: var(--roboto);
            font-size: 12px;
            font-weight: 400;
            color: rgba(105, 105, 116, 1);
        }
    </style>
    <div class="container">
        <h4>{{ trans('Tepe Görselini Düzenle') }}</h4>
        <div class="col-12 d-flex flex-column align-items-center">
            <div class="col-6">
                <img src="{{ banner.banner_image }}" style="width: 100%; height: auto; border-radius: 5px;" alt="Banner Image">
            </div>
            <div class="col-6 mt-4">
                <form action="#" method="POST" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="title">{{ trans('Başlık') }}</label>
                        <input type="text" class="form-control mt-1" id="title" name="title" value="{{ banner.banner_title }}">
                    </div>
                    <div class="form-group mt-3">
                        <label for="description">{{ trans('Alt Başlık') }}</label>
                        <input type="text" class="form-control mt-1" id="title" name="title" value="{{ banner.banner_subtitle }}">
                    </div>
                    <div class="form-group mt-3">
                        <label for="description">{{ trans('Yönlendirme Bağlantısı') }}</label>
                        <input type="text" class="form-control mt-1" id="title" name="title" value="{{ banner.banner_url }}">
                    </div>
                    <div class="form-group mt-3">
                        <label for="image">Görsel</label>
                        <input type="file" class="form-control" id="image" name="image">
                    </div>
                    <button id="application-save-button" type="submit" class="button button--big mt-3 d-flex ms-auto text-end mx-2">{{ trans('Güncelle') }}</button>
                </form>
            </div>
        </div>
    </div>
{% endblock %}