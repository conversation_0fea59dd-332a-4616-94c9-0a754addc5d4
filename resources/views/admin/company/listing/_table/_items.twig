{% for item in items %}

    {# Ma<PERSON>üstü  #}
    <div class="table-row" data-item-id="{{ item.id }}"
        {% for append in listing.appendables %}
            data-{{ append }}="{{ item[append] }}"
        {% endfor %}
    >

        {# Checkbox #}


        {% for header in listing.headers %}

            {% if header.mobile_only is not defined or header.mobile_only == false  %}

                {# Başlık / Metin #}
                {% if header.type == 'text' %}

                    <div class="table-cell{{ header.keep ? ' table-keep' }}{{ header.color ? ' color-' ~ header.color }}{{ header.alignment ? ' text-' ~ header.alignment }}{{ header.weight ? ' fw-' ~ header.weight }}{{ header.size ? ' fs-' ~ header.size }}">{{ header.translate ? trans((header.translate_prefix|default('')) ~ item|get(header.bind)) : item|get(header.bind) }}</div>
                {% elseif header.type == 'compare' %}

                    <div class="table-cell{{ header.keep ? ' table-keep' }}{{ header.color ? ' color-' ~ header.color }}{{ header.alignment ? ' text-' ~ header.alignment }}{{ header.weight ? ' fw-' ~ header.weight }}">
                        <div class="table-details">
                            <p class="fs-md fw-500">{{ item|get(header.current_bind)|raw }}</p>
                            {% if item|get(header.old_bind) %}
                                <p class="text-line fs-sm fw-400 color-grey">{{ item|get(header.old_bind)|raw }}</p>
                            {% endif %}
                        </div>
                    </div>
                {% elseif header.type == 'link' %}

                    <div class="table-cell{{ header.keep ? ' table-keep' }}{{ header.color ? ' color-' ~ header.color }}{{ header.alignment ? ' text-' ~ header.alignment }}{{ header.weight ? ' fw-' ~ header.weight }}"><a href="{{ item|get(header.bind) }}">{{ (item|get(header.text)) }}</a></div>

                {% elseif header.type == 'date' %}

                    <div class="table-cell{{ header.keep ? ' table-keep' }}{{ header.color ? ' color-' ~ header.color }}{{ header.alignment ? ' text-' ~ header.alignment }}{{ header.weight ? ' fw-' ~ header.weight }}" title="{{ item|get(header.bind)|date_iso(header.tooltip_format ? header.tooltip_format : 'LLLL') }}">{{ item|get(header.bind)|date_iso(header.format ? header.format : 'LLL') }}</div>

                {% elseif header.type == 'timestamps' %}

                    <div class="table-cell{{ header.keep ? ' table-keep' }}{{ header.alignment ? ' text-' ~ header.alignment }}">
                        <a class="table-item" href="{{ controller.getRouteLink(header, item) }}">
                            <div class="table-details">
                                <p class="fs-sm fw-400" title="Son Güncellenme Tarihi&#10;{{ item.updated_at|date_iso('LLLL') }}">{{ item.updated_at|date_iso }}</p>
                                <p class="fs-xs fw-400 color-grey" title="Oluşturulma Tarihi&#10;{{ item.created_at|date_iso('LLLL') }}">{{ item.created_at|date_iso }}</p>
                            </div>
                        </a>
                    </div>

                {% elseif header.type == 'image' %}

                    <div class="table-cell {{ header.keep ? ' table-keep' }}">
                        <a class="table-item" href="#">
                            <div class="table-preview"><img class="table-pic" src="{{ item|get(header.bind) }}"></div>
                        </a>
                    </div>

                {% elseif header.type == 'detail' %}

                    <div  class=" table-cell{{ header.keep ? ' table-keep' }}{{ header.alignment ? ' text-' ~ header.alignment }}">
                        <a class="table-item" {% if header.route %}href="{{ controller.getRouteLink(header, item) }}"{% elseif header.link_bind %}href="{{ item|get(header.link_bind) }}"{% endif %}>
                            {% if header.image_bind %}
                            <div class="table-preview{{ header.aspect_ratio ? ' table-preview--ar-' ~ header.aspect_ratio }}" style="background-image: url({{ item|get(header.image_bind) }})"></div>
                            {% endif %}
                            <div class="table-details">
                                {% if header.title_bind and item|get(header.title_bind) %}<div class="table-title">{{ item|get(header.title_bind) }}</div>{% endif %}
                                {% if header.info_bind is defined %}
                                    {% if header.info_bind is not iterable %}
                                        {% set header = header|merge({info_bind: [header.info_bind]}) %}
                                    {% endif %}
                                    {% for info_bind in header.info_bind %}
                                        {% if item|get(info_bind) %}
                                            <div class="table-info ">
                                                {% if "url" in info_bind %}
                                                    <span class="color-blue d-inline-flex align-items-center">
                                                        <span class="copy-link" data-link="{{ item|get(info_bind) }}">{{ get_url_path(item|get(info_bind))|shrink }}</span>
                                                        <button class="button button--small button--transparent ms-1" style="padding: 3px !important; height: auto !important;" onclick="return __copyLink(event);">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M14.7367 2.7619H8.08369C6.02469 2.7619 4.24969 4.4309 4.24969 6.4909V17.2039C4.24969 19.3799 5.90869 21.1149 8.08369 21.1149H16.0727C18.1327 21.1149 19.8017 19.2649 19.8017 17.2039V8.0379L14.7367 2.7619Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M14.474 2.75021V5.65921C14.474 7.07921 15.623 8.23121 17.042 8.23421C18.359 8.23721 19.706 8.23821 19.797 8.23221" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M14.284 15.5578H8.88699" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M12.2425 10.6056H8.88651" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg>
                                                        </button>
                                                    </span>
                                                {% elseif "button" in info_bind %}

                                               <a href={{item.feedback_filter_link}}>
                                                <button  class="button button--big px-4  add-new" >{{ item|get(info_bind)|raw }}</button>
                                               </a>

                                                {% elseif "feedback_point" in info_bind %}
                                                {{ item|get(info_bind)|raw }}
                                                {% else %}


                                                <span>{{ item|get(info_bind)|raw }}</span>{% endif %}
                                            </div>
                                        {% endif %}
                                    {% endfor %}
                                {% endif %}
                                {% if header.date_bind and item|get(header.date_bind) %}<div class="table-info">{{ item|get(header.date_bind)|date_iso(header.date_format ? header.date_format : 'LLLL') }}</div>{% endif %}
                            </div>
                        </a>
                    </div>

                {% elseif header.type == 'status' %}

                    <div class="table-cell{{ header.keep ? ' table-keep' }}">
                        <div class="table-color">
                            <div class="table-bg color-white bg-{{ controller.getStatusColor(item|get(header.bind)) }} px-2 py-1">
                                {{ controller.getStatusLabel(item|get(header.bind)) }}
                            </div>
                        </div>
                    </div>

                {% elseif header.type == 'accommodation' %}

                    <div class="table-cell{{ header.keep ? ' table-keep' }}">{{item.accommodation.name|get(header.bind) }}</div>

                {% elseif header.type == 'status_pill' %}

                    <div class="table-cell{{ header.keep ? ' table-keep' }}">
                        <div class="table-status caption bg-{{ controller.getStatusColor(item|get(header.bind)) }}">{{ controller.getStatusLabel(item|get(header.bind)) }}</div>
                    </div>

                {% else %}

                {% endif %}

            {% endif %}

        {% endfor %}

    </div>

    {# Mobil #}
    {% if listing.has_mobile_header %}
    <div class="table-body">

        {% for header in listing.headers %}
            {% if (header.mobile or header.mobile_only) and header.type == 'status' %}
                <div class="table-bg bg-{{ controller.getStatusColor(item|get(header.bind)) }}"></div>
            {% endif %}
        {% endfor %}

        <div class="table-line">
        {% for header in listing.headers %}

            {% if header.mobile or header.mobile_only %}
                {# Başlık / Metin #}
                {% if header.type == 'text' %}

                    <div class="table-col{{ header.color ? ' color-' ~ header.color }}{{ header.alignment ? ' text-' ~ header.alignment }}{{ header.weight ? ' fw-' ~ header.weight }}">{{ header.translate ? trans((header.translate_prefix|default('')) ~ item|get(header.bind)) : item|get(header.bind) }}</div>

                {% elseif header.type == 'status' %}

                    <div class="table-col fw-500 color-{{ controller.getStatusColor(item|get(header.bind)) }}">{{ controller.getStatusLabel(item|get(header.bind)) }}</div>

                {% elseif header.type == 'compare' %}

                    <div class="table-col{{ header.color ? ' color-' ~ header.color }}{{ header.alignment ? ' text-' ~ header.alignment }}{{ header.weight ? ' fw-' ~ header.weight }}">
                        <div class="table-details">
                            <p class="fs-md fw-500">{{ item|get(header.current_bind)|raw }}</p>
                            {% if item|get(header.old_bind) %}
                                <p class="text-line fs-sm fw-400 color-grey">{{ item|get(header.old_bind)|raw }}</p>
                            {% endif %}
                        </div>
                    </div>

                {% elseif header.type == 'detail' %}

                    <div class="table-col p-2">
                        <a class="table-item" {% if header.route %}href="{{ controller.getRouteLink(header, item) }}"{% elseif header.link_bind %}href="{{ item|get(header.link_bind) }}"{% endif %}>
                            {% if header.image_bind %}
                            <div class="table-preview"><img class="table-pic" src="{{ item|get(header.image_bind) }}"></div>
                            {% endif %}
                            <div class="table-details">
                                {% if header.title_bind and item|get(header.title_bind) %}<div class="table-title">{{ item|get(header.title_bind) }}</div>{% endif %}
                                {% if header.info_bind is defined %}
                                    {% if header.info_bind is not iterable %}
                                        {% set header = header|merge({info_bind: [header.info_bind]}) %}
                                    {% endif %}
                                    {% for info_bind in header.info_bind %}
                                        {% if item|get(info_bind) %}
                                            <div class="table-info">
                                                {% if "url" in info_bind %}
                                                    <span class="color-blue d-inline-flex align-items-center">
                                                        <span class="copy-link" data-link="{{ item|get(info_bind) }}"> {{ get_url_path(item|get(info_bind))|shrink }}</span>
                                                        <button class="button button--small button--transparent ms-1" style="padding: 3px !important; height: auto !important;" onclick="return __copyLink(event);">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M14.7367 2.7619H8.08369C6.02469 2.7619 4.24969 4.4309 4.24969 6.4909V17.2039C4.24969 19.3799 5.90869 21.1149 8.08369 21.1149H16.0727C18.1327 21.1149 19.8017 19.2649 19.8017 17.2039V8.0379L14.7367 2.7619Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M14.474 2.75021V5.65921C14.474 7.07921 15.623 8.23121 17.042 8.23421C18.359 8.23721 19.706 8.23821 19.797 8.23221" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M14.284 15.5578H8.88699" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M12.2425 10.6056H8.88651" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg>
                                                        </button>
                                                    </span>
                                                {% else %}<span>{{ item|get(info_bind) }}</span>{% endif %}
                                            </div>
                                        {% endif %}
                                    {% endfor %}
                                {% endif %}
                                {% if header.date_bind and item|get(header.date_bind) %}<div class="table-info">{{ item|get(header.date_bind)|date_iso(header.date_format ? header.date_format : 'LLLL') }}</div>{% endif %}
                            </div>
                        </a>
                    </div>

                {% else %}
                {% endif %}

            {% endif %}

        {% endfor %}
        </div>

    </div>
    {% endif %}

{% endfor %}
