{% extends "admin/html.twig" %}
{% block body %}

<style>
     @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

    :root{
        --poppins: 'Poppins', sans-serif;
    }

    .custom-red-btn{
        background-color: #BA272D;
        border-radius: 10px;
        color: white;
        padding: 0.75rem 1rem;
        font-family: var(--poppins);
        font-size: 12px;
        font-weight: 500;
        letter-spacing: 0.3px;
        border: none;
        outline: none;
    }
    .custom-gray-btn{
        background-color: #F1F1F5;
        border-radius: 10px;
        border: none;
        outline: none;
        color: #696974;
        padding: 0.75rem 1rem;
        font-family: var(--poppins);
        font-size: 12px;
        font-weight: 500;
        letter-spacing: 0.3px;
        cursor: pointer;
    }

    #application-custom-questions:empty:before {
        content: "{{ trans('<PERSON><PERSON>') }}";
        text-align: center;
        display: block;
        font-size: 0.75rem;
        font-weight: 500; }

    .custom-red-btn:hover{
        background-color: #F1F1F5;
        color: #BA272D;
        border: none;
        outline: none;
    }

    .custom-gray-btn:hover{
        background-color: #696974;
        border: none;
        outline: none;
        color: #F1F1F5;
    }
    .modal{
        margin: 0;
    }

    .col-md-8{
        padding-left: 2rem;
    }
    .col-md-4{
        padding-right: 2rem;
    }
</style>

    {{ form_model(null, {'route': ['admin.company.create.post'], 'method': 'POST','enctype':'multipart/form-data'}) }}

<div class="container mx-auto" id="translate">

    <div class="row form-modal">

        <div class="col-12 px-0 mx-auto">
            {% include "_messageBag.twig" %}
        </div>

        <div class="d-flex align-items-center">
            <span class="iconify me-2" style="font-size: 26px; cursor: pointer;"  onclick="goBack()" data-icon="solar:round-arrow-left-outline" data-inline="false"></span>
            <div>
                {% include 'admin/header.twig' with {'title': trans('Firma Oluştur')} %}
            </div>
            <div class="form mt-4 ms-auto" style="margin-right: 1rem;">
                <button id="application-save-button" type="submit" class="custom-red-btn button">{{ trans('Kaydet') }}</button>
            </div>
        </div>

        {# Sol Taraf #}
        <div class="col-12 col-md-8">

            {# Basit program bilgileri #}
            <div class="col-12 form modal mt-4" spellcheck="false">
                <div class="modal-header">
                    <h5>{{ trans('Firma Bilgileri') }}</h5>
                </div>
                <div class="modal-body">
                    <div class="form-row">
                        <div class="form-control-wrapper form-control-wrapper--is-required">
                            <div class="form-label">
                                {{ form_label('company_name', trans('Firma İsmi')) }}
                            </div>
                            <div class="form-input">
                                {{ form_text('company_name', null, {'class': 'form-control','required':true}) }}
                            </div>
                        </div>
                    </div>
                    {# <div class="form-row">
                        <div class="form-control-wrapper">
                            <div class="form-label">
                                {{ form_label('subtitle', trans('Adres')) }}
                            </div>
                            <div class="form-input">
                                {{ form_text('subtitle', null, {'class': 'form-control' }) }}
                            </div>
                        </div>
                    </div> #}
                    <div class="col-12 form mt-4 d-flex justify-content-between">
                        <div class="form-row col-6">
                            <div class="form-control-wrapper">
                                <div class="form-label">
                                    {{ form_label('phone_number', trans('Telefon Numarası')) }}
                                </div>
                                <div class="form-input">
                                    {{ form_text('phone_number', null, {'class': 'form-control','required':true}) }}
                                </div>
                            </div>
                        </div>
                        <div class="form-row col-6">
                            <div class="form-control-wrapper">
                                <div class="form-label">
                                    {{ form_label('contact_email', trans('Mail Adresi')) }}
                                </div>
                                <div class="form-input">
                                    {{ form_text('contact_email', company.contact_email, {'class': 'form-control','required':true }) }}
                                </div>
                            </div>
                        </div>
                    </div>
                    {# <div class="form-row">
                        <div class="form-control-wrapper">
                            <div class="form-label">
                                {{ form_label('theme_color', trans('Firma Teması')) }}
                            </div>
                            <div class="form-input">
                                <select name="theme_color" id="theme_color" class="form-control">
                                    <option selected disabled>{{ trans('Tema Seçin') }}</option>
                                    <option value="#264653">{{ trans('Standart') }}</option>
                                    <option value="#070033">{{ trans('Lacivert') }}</option>
                                    <option value="#cc8899">{{ trans('Mor') }}</option>
                                    <option value="#1c542d">{{ trans('Yeşil') }}</option>
                                    <option value="#722f37">{{ trans('Kahverengi') }}</option>
                                    <option value="#222233">{{ trans('Gri') }}</option>
                                </select>
                            </div>
                        </div>
                    </div>#}
                    {% if admin_user.super_admin == 1 %}
                    <div class="form-row">
                        <div class="form-control-wrapper">
                            <div class="form-label">
                                {{ form_label('package_type', trans('Firma Abonelik')) }}
                            </div>
                            <div class="form-input">
                                <select name="package_type" id="package_type" class="form-control" required>
                                    <option selected disabled>{{ trans('Paket Seçin') }}</option>
                                    <option value="1">Red - 1</option>
                                    <option value="2">Blue - 2</option>
                                    <option value="3">Yellow - 3</option>
                                    <option value="99999">Green - {{ trans('Limitsiz') }}</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-control-wrapper">
                            <div class="form-label">
                                {{ form_label('user_limit', trans('Kullanıcı Limiti')) }}
                            </div>
                            <div class="form-input">
                                <select name="user_limit" id="user_limit" class="form-control" required>
                                    <option selected disabled>{{ trans('Paket Seçin') }}</option>
                                    <option value="15">Bronze(10-15)</option>
                                    <option value="60">Silver(25-60)</option>
                                    <option value="-1">Gold(60+)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    {# <div class="form-row">
                        <div class="form-control-wrapper">
                            <div class="form-label">
                                {{ form_label('location_id', trans('Saat Dilimi')) }}
                            </div>
                            <div class="form-input">
                                <select name="time_zone" id="time_zone" class="form-control">
                                    <option selected disabled>{{ trans('Saat Dilimi Seçin') }}</option>
                                    {% for timezone in timezones %}
                                        <option value="{{ timezone }}" {{ company.getSettingValue("timezone") == timezone ? 'selected' : '' }}>{{ timezone }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>#}
                    <div class="form-row">
                        <div class="form-control-wrapper">
                            <div class="form-label">
                                {{ form_label('description', trans('Açıklama')) }}
                            </div>
                            <div class="form-input">
                                {{ form_textarea('description', null, {'class': 'editor tinymce d-none'}) }}
                                {# {% for companySetting in company %}
                                    {% if companySetting.setting_key == "package_type" %}
                                        {{ companySetting.setting_value }}
                                    {% endif %}
                                {% endfor %} #}
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-control-wrapper">
                            <div class="form-label">
                                {{ form_label('address', trans('Adres')) }}
                            </div>
                            <div class="form-input">
                                {{ form_textarea('address', company.address, {'class': 'form-control','required':true}) }}
                            </div>
                            {# <div class="form-description">
                                <p class="fs-sm"><span class="fw-500">{{ trans('Özet') }}</span>: {{ trans('Sosyal medya paylaşımları gibi önizleme yapılacak yerlerde gözükecek açıklamadır.') }}</p>
                            </div> #}
                        </div>
                    </div>

                    {# <div class="separator"></div>
                    <div class="form-row">
                        <div class="form-control-wrapper">
                            <label class="checkbox">
                                <input type="checkbox" name="is_applicable" value="true" {% if item.is_applicable is not defined or item.is_applicable == true %} checked="checked" {% endif %}>
                                <div class="checkbox-control align-items-center">
                                    <span class="checkbox-tick"></span>
                                    <span class="checkbox-text"><span class="fs-md fw-500">{{ trans('Program başvuru kabul edebilir mi?') }}</span></span>
                                </div>
                            </label>
                        </div>
                    </div> #}

                </div>
            </div>

        </div>

        {# Sağ Taraf Kısım #}
        <div class="col-12 col-md-4">

            {# Kamp Yayın Durumu #}
            {#<div class="col-12 form modal mt-4">
                 <div class="modal-header">
                    <h5>{{ trans('Yayımla') }}</h5>
                </div>
                <div class="modal-body">
                    <div class="form-row">
                        <div class="form-control-wrapper">
                            <div class="form-label">
                                {{ form_label('status', 'Status') }}
                            </div>
                            <div class="form-input">
                                <select name="status" id="status" class="form-control">
                                    <option value="draft" {% if item.status == 'draft' %} selected="selected" {% endif %}>{{ trans('Taslak') }}</option>
                                    <option value="completed" {% if item.status == 'completed' %} selected="selected" {% endif %}>{{ trans('Tamamlandı') }}</option>
                                    <option value="published" {% if item.status == 'published' %} selected="selected" {% endif %}>{{ trans('Yayında') }}</option>
                                    {# <option value="careers" {% if item.status == 'careers' %} selected="selected" {% endif %}>{{ trans('Kariyerler') }}</option>
                                </select>
                            </div>
                            <div class="form-description">
                                <p>{{ trans('Bu programın yayınlanma durumunu belirtin') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer py-2">
                    <p class="fs-xs">{{ trans('Yazınızı düzenlemeyi bitirdiyseniz buradan güncelleyebilirsiniz.') }}</p>
                </div> #}
                {# {% if item %}
                    <div class="modal-footer">
                        <div class="pb-3">
                            <p class="fw-500 fs-sm color-black">{{ trans('Başvuru Linki') }}</p>
                            {% if item.custom_application_link %}
                            <p class="fs-xs"><a href="{{ item.custom_application_link }}" target="_camp_{{ item.id }}_application_form" class="link">{{ item.custom_application_link }}</a></p>
                            {% else %}
                            <p class="fs-xs"><a href="{{ route('camp.applicant.form', { camp: item.slug }) }}" target="_camp_{{ item.id }}_application_form" class="link">{{ route('camp.applicant.form', { camp: item.slug }) }}</a></p>
                            {% endif %}
                            {% if item.status != 'published' %}
                                <p class="fw-500 fs-xs color-red">{{ trans('Yayında olmadığı için başvurular kapalı') }}</p>
                            {% elseif item.is_applicable == false %}
                                <p class="fw-500 fs-xs color-red">{{ trans('Başvuru kabul edebilir seçeneği kapalı') }}</p>
                            {% elseif item.expiry_at != null and item.expiry_at <= "now"|date_iso('YYYY-MM-DD HH:mm') %}
                                <p class="fw-500 fs-xs color-red">{{ trans('Son başvuru kabul tarihi geçmiş') }}</p>
                            {% else %}
                                <p class="fw-500 fs-xs color-green">{{ trans('Başvuru alabilir') }}</p>
                            {% endif %}
                        </div>
                    </div>
                    <div class="modal-footer pb-3 d-flex justify-content-between">
                        <div>
                            <p class="fs-xs"><a class="link" href="{{ route('admin.application.index', {'filter[camp.id]': item.id}) }}">{{ trans('Başvurularına git') }}</a></p>
                            <p class="fs-xs color-grey">{{ trans(total_applications_label) }}</p>
                        </div>
                        <p class="fs-xs"><a class="link" href="{{ route('admin.application.export', {'filter[camp.id]': item.id}) }}">{{ trans('Başvuruları dışa aktar') }}</a></p>
                    </div>
                    <div class="modal-footer pb-3">
                        <p class="fs-xs fw-500 pb-2">{{ trans('Google Sheets Aktarım Linki') }}</p>

                        <div class="form-input form-input--small d-flex align-items-center">
                            <input id="google_sheet_url" type="text" class="form-control" readonly="readonly" value="{{ item.google_sheet_url }}">
                            <button type="button" class="button ms-2" onclick="return __copyInput(this)" data-copy-id="google_sheet_url">Kopyala</button>
                        </div>

                    </div>
                {% endif %}
            </div>#}

            {# Kamp Kategorileri #}
            {# <div class="col-12 form modal mt-4">
                <div class="modal-header">
                    <h5>{{ trans('Kategori') }}</h5>
                </div>
                <div class="modal-body py-2">
                    <div class="form-row">
                        <div class="form-control-wrapper">
                            <div class="form-input">
                                {% for value, label in categories %}
                                <div class="my-2">
                                    <label class="checkbox">
                                        <input type="checkbox"
                                            name="categories[]"
                                            value="{{ value }}"
                                            {% if value in item.categories.pluck('title', 'id').toArray()|keys %}
                                                checked
                                            {% endif %}
                                            >
                                        <div class="checkbox-control">
                                            <span class="checkbox-tick"></span>
                                            <span class="checkbox-text">{{ trans(label) }}</span>
                                        </div>
                                    </label>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div> #}

            {# Eğitmenler #}
            {# <div class="col-12 form modal mt-4">
                <div class="modal-header">
                    <h5>{{ trans('Eğitmenler') }}</h5>
                </div>
                <div class="modal-body py-2">
                    <div class="form-row">
                        <div class="form-input">
                            <select class="form-control" name="educators[]" id="educator_selector" placeholder="trans('Eğitmen Seçin')" multiple>
                                {% for educator in item.educators %}
                                    <option value="{{ educator.id }}" selected>{{ trans(educator.title) }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>
            </div> #}

            {# Sorumlu #}
            {# {% if user.role == "moderator" %}
            <div class="col-12 form modal mt-4 d-none">
                <div class="modal-header">
                    <h5>{{ trans('Sorumlular') }}</h5>
                </div>
                <div class="modal-body py-2">
                    <div class="form-row">
                        <div class="form-input">
                            <select class="form-control" name="users[]" id="user_selector" multiple data-users={{ users }} >
                                {% for user in users %}
                                    <option
                                        value="{{ user.id }}"
                                        {% if user.id in camp_users %} selected {% endif %}
                                        >
                                        {{ user.name }}
                                        </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="col-12 form modal mt-4">
                <div class="modal-header">
                    <h5>{{ trans('Sorumlular') }}</h5>
                </div>
                <div class="modal-body py-2">
                    <div class="form-row">
                        <div class="form-input">
                            <select class="form-control" name="users[]" id="user_selector" multiple data-users={{ users }} >
                                {% for user in users %}
                                    <option
                                        value="{{ user.id }}"
                                        {% if user.id in camp_users %} selected {% endif %}
                                        >
                                        {{ user.name }}
                                        </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %} #}


            {# Kamp Görselleri #}
            <div class="col-12 form modal mt-4">
                <div class="modal-header">
                    <h5>{{ trans('Medya / Görseller') }}</h5>
                </div>
                <div class="modal-body">
                    <p class="fw-500 fs-md mb-2">{{ trans('Logo') }} <span class="fs-sm">(1:1)</span></p>
                    <input name="logo" type="hidden" value="{{ company.logo }}">
                    <div class=" form-media--single {{ company.logo ? 'form-media-items--has-image' }}" ar="1:1">
                        <div class="profile-container">
                            <label for="file-upload" class="upload-label"> {{trans('Görsel Yükle')}} </label>
                            <input type="file" name="logo" id="file-upload" accept="image/png, image/jpeg" style="display: none" value="{{ company.logo }}"/>
                            <img id="image" src="{% if company.logo %}https://istegelis.com{{ company.logo }}{%endif%}" {% if company.logo is empty %}class="d-none"{% endif%} alt=""/>
                        </div>


                    </div>
                </div>
                {# <div class="separator my-2"></div>
                <div class="modal-body">
                    <p class="fw-500 fs-md mb-2">{{ trans('Tepe Fotoğrafı') }} <span class="fs-sm">(6:1)</span></p>
                    <input name="header_image" type="hidden" value="{{ item.header_image_id }}">
                    <div class="form-media-items form-media--single {{ item.header_image ? 'form-media-items--has-image' }}" ar="6:1">
                        {% if item.header_image %}
                            <div class="form-media-item" data-media-hash="{{ item.header_image.hash }}" data-image-url="{{ item.header_image.url }}" data-media-id="{{ item.header_image.id }}">
                                <div class="form-media-item-overlay ">
                                    <button type="button" class="form-media-item-remove button button--transparent">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                                <div class="form-media-item-content">
                                    <img src="{{ item.header_image.url }}">
                                </div>
                            </div>
                        {% endif %}
                        <button type="button" class="form-media-item-add">
                            <div class="form-media-item-content">
                                <span class="click-text">{{ trans('Yeni görsel ekle') }}</span>
                                <span class="drop-text">{{ trans('Görselleri buraya bırakın') }}</span>
                            </div>
                        </button>
                    </div>
                </div> #}
            </div>

            {# Kamp Başlangıç - Bitiş Tarihi #}
            {# <div class="col-12 form modal mt-4">
                <div class="modal-header mb-2">
                    <h5>{{ trans('Program Tarihleri') }}</h5>
                </div>
                <div class="modal-body pb-2">
                    <div class="form-row">
                        <div class="form-control-wrapper">
                            <div class="form-label">
                                <label for="date_range">{{ trans('Başlangıç - Bitiş Tarihi') }}</label>
                            </div>
                            <div class="form-input">
                                <input id="date_range" type="text" name="date_range" value="{{ item.dateRange }}" class="form-control datetimepicker" data-mode="range" placeholder="{{ trans('Programın başlangıç ve bitiş tarih aralığı') }}" data-time="false" data-dateformat="Y-m-d">
                            </div>
                            <div class="form-description mb-0 text-right">
                                <a class="link fs-xs" href="#" onclick="document.getElementById('date_range')._flatpickr.clear(); return false;">{{ trans('Tarihi temizle') }}</a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer pb-3">
                    <p>{{ trans('Takvim üzerinde gözükecek tarih aralığıdır') }}</p>
                </div>
            </div>
            <div class="col-12 form modal mt-4">
                <div class="modal-header mb-2">
                    <h5>{{ trans('Program Saati') }}</h5>
                </div>
                <div class="modal-body pb-2">
                    <div class="form-row">
                        <div class="form-control-wrapper">
                            <div class="form-label">
                                <label for="date_range2">{{ trans('Başlangıç Saatini 00:00 formatında yazın') }}</label>
                            </div>
                            <div class="form-input">
                                <input name="date_time" type="text" class="form-control" value="{{ item.date_time }}" placeholder="{{ trans('Programın başlangıç saati') }}">
                            </div>
                            <div class="form-description mb-0 text-right">
                                <a class="link fs-xs" href="#" onclick="document.getElementById('date_range2')._flatpickr.clear(); return false;">{{ trans('Tarihi temizle') }}</a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer pb-3">
                    <p>{{ trans('Takvim üzerinde gözükecek tarih aralığıdır') }}</p>
                </div>
            </div> #}

        </div>


    </div>

</div>

{{ form_close() }}

    {# Kaynak destroy methoduna sahipse bunun için form oluştur #}
    {% if item and listing.actions.enabled and route_has('admin.' ~ listing.resource ~ '.destroy') %}
        {{ form_model(item, {'route': ['admin.' ~ listing.resource ~ '.destroy', item.id], 'method': 'DELETE', 'class': 'form-destroy-item'}) }}
        {{ form_close() }}
    {% endif %}
    <style>
        .profile-container {
            position: relative;
            border: 1px dashed #264653;
            width: 150px;
            height: 150px;
            border-radius: 4px;
            background-color: #f1f1f1;
        }

        .profile-container img:hover {
            cursor: pointer;
            opacity: 0.4;
        }

        .profile-container img {
            transition: all 0.3s ease-in-out;
            object-fit: cover;
        }


        .upload-label {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #000;
            font-weight: bold;
            display: flex;
            justify-content: center;
            width: 100%;
            height: 100%;
            align-items: center;
            cursor: pointer;
            transition: opacity 0.3s ease;
        }

        .profile-container:hover .upload-label {
            opacity: 1;
        }

        .profile-container #image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    </style>
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            const uploadLabel = document.querySelector(".upload-label");
            const fileInput = document.querySelector("#file-upload");
            const imagePreview = document.querySelector("#image");

            uploadLabel.addEventListener("click", function (e) {
                e.preventDefault(); // Default işlemi iptal ediyoruz
                fileInput.click();
            });

            fileInput.addEventListener("change", function () {
                const file = this.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function (e) {
                        imagePreview.src = e.target.result;
                    };
                    reader.readAsDataURL(file);
                    imagePreview.classList.remove('d-none');
                }
            });
        });
    </script>
<script>

    let isApplicableCheckbox = document.querySelector('[name="is_applicable"]');
    if (isApplicableCheckbox) {

        document.body.classList.toggle('is-applicable', isApplicableCheckbox.checked);

        isApplicableCheckbox.addEventListener('change', function() {
            document.body.classList.toggle('is-applicable', this.checked);
        });
    }

</script>


{% endblock %}
