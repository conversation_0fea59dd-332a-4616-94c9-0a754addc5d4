{% extends "admin/html.twig" %}
{% block body %}
    <div class = "container">

        <div class="row">

            {% if auth().user().role == 'admin' %}
                <div class="col-md-12">
                    <div class="modal modal-admin d-flex mt-3 flex-column" >
                        <div class="modal-header d-flex align-items-center" >
                            <img src="https://istegelis.com{{ company.logo }}" height="50" width="auto" class="company-logo" style="box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);border-radius: 10px;">
                            <span class="ms-2 fw-500 fs-md">{{ company.company_name }}</span>
                        </div>
                        <div class="row">
                            <div class="col-lg-4">
                                <div class="modal-body">
                                    <p class="fs-md fw-500"> {{ trans('Mail Adresi:') }}</p>
                                    <p class="mb-4>">{{ company.contact_email }}</p>
                                </div>
                            </div>
                            <div class="col-lg-4">
                                <div class="modal-body">
                                    <p class="fs-md fw-500"> {{ trans('Telefon Numarası:') }}</p>
                                    <p class="mb-4>">{{ company.phone_number }}</p>
                                </div>
                            </div>
                            <div class="col-lg-4">
                                <div class="modal-body">
                                    <p class="fs-md fw-500"> {{ trans('Adres:') }}</p>
                                    <p class="mb-4>">{{ company.address }}</p>
                                </div>
                            </div>
                            <div class="col-lg-4">
                                <div class="modal-body">
                                    <p class="fs-md fw-500">{{ trans('Abonelik Bilgileri') }}</p>
                                    <p class="mb-4>">
                                        {% if company.getSettingValue("package_type") == '2' %}Red{% elseif company.getSettingValue("package_type") == "3" %}Blue{% elseif company.getSettingValue("package_type")  == "4" %}Yellow{% elseif company.getSettingValue("package_type") == "99999" %}Green{% endif %}
                                    </p>
                                    {# <p class="mb-4>">{{ trans('subscription_user_count',{'user_count': company.getSettingValue('package_type') == '99999'? trans('Limitsiz') : company.getSettingValue('package_type')}) }}</p>#}
                                </div>
                            </div>
                            <div class="col-lg-4">
                                <div class="modal-body">
                                    <p class="fs-md fw-500">{{ trans('Kullanıcı Limiti') }}</p>
                                    <p class="mb-4>">
                                        {% if company.getSettingValue("user_limit") == "15" %}Bronze{% elseif company.getSettingValue("user_limit") == "60" %}Silver{% elseif company.getSettingValue("user_limit") == "-1" %}Gold{% endif %}
                                    </p>
                                    {# <p class="mb-4>">{{ trans('subscription_user_count',{'user_count': company.getSettingValue('package_type') == '99999'? trans('Limitsiz') : company.getSettingValue('package_type')}) }}</p>#}
                                </div>
                            </div>
                        </div>
                        <div class="text-right mt-4">
                            <a class="link fs-sm d-inline-flex align-items-center" href="{{ route('admin.company.edit') }}">
                                {% if auth().user().super_admin == '1' %}
                                <span class="me-1">{{ trans('Firma Bilgilerini Güncelle') }}</span>

                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewbox="0 0 24 24">
                                    <path fill="none" stroke="#2860F6" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.75 11.72h-15"></path>
                                    <path fill="none" stroke="#2860F6" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.7 5.7l6.05 6.02 -6.05 6.025"></path>
                                </svg>
                                {% endif %}
                            </a>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>

    </div>
{% endblock %}
