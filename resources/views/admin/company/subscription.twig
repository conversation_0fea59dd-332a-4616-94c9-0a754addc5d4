{% extends "admin/html.twig" %}
{% block body %}

<style>
     @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

    :root{
        --poppins: 'Poppins', sans-serif;
    }

    .card {
  width: 150px;
  height: 214px;
  overflow: visible;
  cursor: pointer;
  position: relative;
}

.card::before, .content {
  border-radius: 5px;
  box-shadow: 0px 0px 5px 1px #00000022;
  transition: transform 300ms, box-shadow 200ms;
}

.card::before {
  position: absolute;
  content: ' ';
  display: block;
  width: 100%;
  height: 100%;
  background-color: #FDD652;
  transform: rotateZ(5deg);
}

.description {
  width: 100%;
  text-align: center;
  margin-top: 20px;
}

.info {
  color: #00000066;
}

.price::before {
  content: ' ';
}

.price {
  font-weight: bold;
  color: #ee9933;
}

.description p {
  margin-bottom: 10px;
}

.card .content {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: white;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  transform: rotateZ(-5deg);
}

.content .img {
  width: 100px;
  height: fit-content;
}

.selected-card::before, .selected-card .content, .card:hover::before, .card:hover .content {
  transform: rotateZ(0deg);
}

.card:active::before, .card:active .content {
  box-shadow: none;
}

    .custom-red-btn{
        background-color: #BA272D;
        border-radius: 10px;
        color: white;
        padding: 0.75rem 1rem;
        font-family: var(--poppins);
        font-size: 12px;
        font-weight: 500;
        letter-spacing: 0.3px;
        border: none;
        outline: none;
    }
    .custom-gray-btn{
        background-color: #F1F1F5;
        border-radius: 10px;
        border: none;
        outline: none;
        color: #696974;
        padding: 0.75rem 1rem;
        font-family: var(--poppins);
        font-size: 12px;
        font-weight: 500;
        letter-spacing: 0.3px;
        cursor: pointer;
    }

    #application-custom-questions:empty:before { 
        content: "{{ trans('Hiç Özel Soru Yok') }}";
        text-align: center; 
        display: block; 
        font-size: 0.75rem; 
        font-weight: 500; }

    .custom-red-btn:hover{
        background-color: #F1F1F5;
        color: #BA272D;
        border: none;
        outline: none;
    }

    .custom-gray-btn:hover{
        background-color: #696974;
        border: none;
        outline: none;
        color: #F1F1F5;
    }
    .modal{
        margin: 0;
    }

    .col-md-8{
        padding-left: 2rem;
    }
    .col-md-4{
        padding-right: 2rem;
    }

    .radio-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    }

    .radio-container input[type="radio"] {
        display: none;
    }

    .radio-container label {
        cursor: pointer;
        padding: 5px 10px;
        background-color: #f0f0f0;
        border-radius: 5px;
        transition: background-color 0.3s;
    }

    .radio-container label:hover {
        background-color: #e0e0e0;
    }

    .radio-container input[type="radio"]:checked + label {
        background-color: #d0d0d0;
    }

    .card {
    cursor: pointer;
    transition: background-color 0.3s, box-shadow 0.3s;
}

    .card:hover {
        background-color: #f9f9f9;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .card input[type="radio"] {
        display: none;
    }
    
    {# .cart {
    max-width: 400px;
    margin: 0 auto;
} #}

.cart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.cart-badge {
    background-color: #007bff;
    color: white;
    border-radius: 50%;
    padding: 5px 10px;
}

.cart-list {
    list-style: none;
    padding: 0;
    margin-bottom: 20px;
}

.cart-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border: 1px solid #ddd;
    margin-bottom: 10px;
}

.cart-item-title {
    margin: 0;
    font-size: 16px;
}

.cart-item-description {
    color: #6c757d;
}

.cart-item-price {
    color: #6c757d;
}

.promo {
    background-color: #f8f9fa;
}

.promo-code {
    color: #28a745;
}

.promo-discount {
    color: #28a745;
}

.total {
    font-weight: bold;
}

.promo-form {
    padding: 10px;
    border: 1px solid #ddd;
}

.promo-input-group {
    display: flex;
}

.modal.form {
    padding-bottom: 10px;
}

.promo-input {
    flex: 1;
    padding: 5px;
    border: 1px solid #ddd;
    border-radius: 4px 0 0 4px;
}

.promo-button {
    padding: 5px 10px;
    border: 1px solid #ddd;
    border-left: none;
    border-radius: 0 4px 4px 0;
    background-color: #6c757d;
    color: white;
}

.selected-card {
    border: 5px solid #FDD652;
    border-radius: 5px;
    background-color: #f9f9f9;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.selected-card p, .selected-card title, .selected-card .info, .selected-card .price {
    font-weight: 900;
}

.selected-card .price {
    content: 'Seçildi';
}

.text-center {
    text-align: center;
    font-family: var(--poppins);
    font-weight: 500;
    font-size: 1.5rem;
}


</style>

    {{ form_model(item, {'route': ['admin.company.update'], 'method': 'PUT'}) }}

<div class="container mx-auto" id="translate">

    <div class="row form-modal">

        <div class="col-12 px-0 mx-auto">
            {% include "_messageBag.twig" %}
        </div>

        <div class="d-flex justify-content-between">
            <div >
                {% include 'admin/header.twig' with {'title': trans('Firma Abonelik Sayfası')} %}   
            </div>
            <div class="form mt-4" style="margin-right: 1rem;">
                <button id="application-save-button" type="submit" class="custom-red-btn button">{{ trans('Kaydet') }}</button>
            </div>
        </div>

        {# Sol Taraf #}
        <div class="col-12 col-md-8">

            {# Basit program bilgileri #}
            <div class="col-12 form modal mt-4" spellcheck="false">
                <div class="modal-header">
                    <h5>{{ trans('Fatura Bilgileri') }}</h5>
                </div>
                <div class="modal-body">
                    {# <div class="form-row disable">
                        <div class="form-control-wrapper form-control-wrapper--is-required">
                            <div class="form-label">
                                {{ form_label('company_name', trans('İsim Soyisim')) }}
                            </div>
                            <div class="form-input">
                                {{ form_text('company_name', company.company_name, {'class': 'form-control' }) }}
                            </div>
                        </div>
                    </div> #}
                    <div class="col-12 form mt-4 d-flex justify-content-between">
                        <div class="form-row col-6">
                            <div class="form-control-wrapper" style="pointer-events:none;">
                                <div class="form-label">
                                    {{ form_label('phone_number', trans('İsim')) }}
                                </div>
                                <div class="form-input">
                                    {{ form_text('phone_number', company.phone_number, {'class': 'form-control' }) }}
                                </div>
                            </div>
                        </div>
                        <div class="form-row col-6">
                            <div class="form-control-wrapper">
                                <div class="form-label" style="pointer-events:none;">
                                    {{ form_label('mail_adress', trans('Soyisim')) }}
                                </div>
                                <div class="form-input" style="pointer-events:none;">
                                    {{ form_text('mail_adress', company.contact_email, {'class': 'form-control' }) }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 form mt-4 d-flex justify-content-between">
                        <div class="form-row col-6">
                            <div class="form-control-wrapper" style="pointer-events:none;">
                                <div class="form-label">
                                    {{ form_label('phone_number', trans('Telefon Numarası')) }}
                                </div>
                                <div class="form-input">
                                    {{ form_text('phone_number', company.phone_number, {'class': 'form-control' }) }}
                                </div>
                            </div>
                        </div>
                        <div class="form-row col-6">
                            <div class="form-control-wrapper">
                                <div class="form-label" style="pointer-events:none;">
                                    {{ form_label('mail_adress', trans('Mail Adresi')) }}
                                </div>
                                <div class="form-input" style="pointer-events:none;">
                                    {{ form_text('mail_adress', company.contact_email, {'class': 'form-control' }) }}
                                </div>
                            </div>
                        </div>
                    </div>


                    {# <div class="container my-4">
                        <h2 class="text-center">{{ trans('Yükseltmek istediğiniz paketi seçin') }}</h2>
                        <div class="row my-4">
                            {% for package in packages %}
                                <div class="col-md-3 mb-4">
                                    <div class="card package-card" id="card{{ loop.index }}"
                                                data-id="{{ package.id }}"
                                                data-title="{{ package.title }}"
                                                data-users="{{ package.limit_usage }}"
                                                data-price="{{ package.price == 0 ? 'Free for 3 months' : '$' ~ package.price ~ ' / month' }}"
                                                data-package-id="{{ package.id }}">
                                        <div class="content">
                                            <div class="description">
                                                <p class="title">
                                                    <strong>{{ package.title }}</strong>
                                                </p>
                                                <p class="info">
                                                    {{ package.limit_usage }} Users
                                                </p>
                                                <input type="radio" id="user{{ loop.index }}" name="subscription" value="{{ package.id }}">
                                                <p class="price">
                                                    {% if company.package_type == package.id %}
                                                        {{ trans('Mevcut Paket') }}
                                                    {% else %}
                                                        {% if package.price == 0 %}
                                                            Free for 3 months
                                                        {% else %}
                                                            ${{ package.price }} / month
                                                        {% endif %}
                                                    {% endif %}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% if loop.index is divisible by(4) %}
                                    </div><div class="row">
                                {% endif %}
                            {% endfor %}
                        </div>
                </div> #}




                    
                        {# <div class="card my-5" id="card2">
                            <div class="content">
                                <div class="description">
                                <p class="title">
                                    <strong>Package 2</strong>
                                </p>
                                <p class="info">
                                    5-15 Users
                                </p>
                                <input type="radio" id="10user" name="subscription" value="monthly">
                                <p class="price">
                                    45 $ / month
                                </p>
                                </div>
                            </div>
                        </div>
                    
                        <div class="card my-5" id="card3">
                            <div class="content">
                                <div class="description">
                                <p class="title">
                                    <strong>Package 3</strong>
                                </p>
                                <p class="info">
                                    15-50 Users
                                </p>
                                <input type="radio" id="15user" name="subscription" value="monthly">
                                <p class="price">
                                    135 $ / month
                                </p>
                                </div>
                            </div>
                        </div>
                    
                        <div class="card my-5" id="card4">
                        
                            <div class="content">
                                <div class="description">
                                <p class="title">
                                    <strong>Package 4</strong>
                                </p>
                                <p class="info">
                                    50-100 Users
                                </p>
                                <input type="radio" id="20user" name="subscription" value="monthly">
                                <p class="price">
                                    240 $ / month
                                </p>
                                </div>
                            </div>
                        </div> #}
            {# </div> #}





                    {# <div class="form-row">
                        <div class="form-control-wrapper">
                            <div class="form-label">
                                {{ form_label('description', trans('Açıklama')) }}
                            </div>
                            <div class="form-input">
                                {{ form_textarea('description', company.description, {'class': 'editor tinymce d-none'}) }}
                                {# {% for companySetting in company %}
                                    {% if companySetting.setting_key == "package_type" %}
                                        {{ companySetting.setting_value }}
                                    {% endif %}
                                {% endfor %} 
                            </div>
                        </div>
                    </div> #}
                    {# <div class="form-row">
                        <div class="form-control-wrapper">
                            <div class="form-label">
                                {{ form_label('address', trans('Adres')) }}
                            </div>
                            <div class="form-input">
                                {{ form_textarea('address', company.address, {'class': 'form-control'}) }}
                            </div>
                            <div class="form-description">
                                <p class="fs-sm"><span class="fw-500">{{ trans('Özet') }}</span>: {{ trans('Sosyal medya paylaşımları gibi önizleme yapılacak yerlerde gözükecek açıklamadır.') }}</p>
                            </div>
                        </div>
                    </div> #}

                    {# <div class="separator"></div>
                    <div class="form-row">
                        <div class="form-control-wrapper">
                            <label class="checkbox">
                                <input type="checkbox" name="is_applicable" value="true" {% if item.is_applicable is not defined or item.is_applicable == true %} checked="checked" {% endif %}>
                                <div class="checkbox-control align-items-center">
                                    <span class="checkbox-tick"></span>
                                    <span class="checkbox-text"><span class="fs-md fw-500">{{ trans('Program başvuru kabul edebilir mi?') }}</span></span>
                                </div>
                            </label>
                        </div>
                    </div> #}

                </div>
            </div>

        </div>

        {# Sağ Taraf Kısım #}
        <div class="col-12 col-md-4">

            {# Kamp Yayın Durumu #}
            {#<div class="col-12 form modal mt-4">
                 <div class="modal-header">
                    <h5>{{ trans('Yayımla') }}</h5>
                </div>
                <div class="modal-body">
                    <div class="form-row">
                        <div class="form-control-wrapper">
                            <div class="form-label">
                                {{ form_label('status', 'Status') }}
                            </div>
                            <div class="form-input">
                                <select name="status" id="status" class="form-control">
                                    <option value="draft" {% if item.status == 'draft' %} selected="selected" {% endif %}>{{ trans('Taslak') }}</option>
                                    <option value="completed" {% if item.status == 'completed' %} selected="selected" {% endif %}>{{ trans('Tamamlandı') }}</option>
                                    <option value="published" {% if item.status == 'published' %} selected="selected" {% endif %}>{{ trans('Yayında') }}</option>
                                    {# <option value="careers" {% if item.status == 'careers' %} selected="selected" {% endif %}>{{ trans('Kariyerler') }}</option>
                                </select>
                            </div>
                            <div class="form-description">
                                <p>{{ trans('Bu programın yayınlanma durumunu belirtin') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer py-2">
                    <p class="fs-xs">{{ trans('Yazınızı düzenlemeyi bitirdiyseniz buradan güncelleyebilirsiniz.') }}</p>
                </div> #}
                {# {% if item %}
                    <div class="modal-footer">
                        <div class="pb-3">
                            <p class="fw-500 fs-sm color-black">{{ trans('Başvuru Linki') }}</p>
                            {% if item.custom_application_link %}
                            <p class="fs-xs"><a href="{{ item.custom_application_link }}" target="_camp_{{ item.id }}_application_form" class="link">{{ item.custom_application_link }}</a></p>
                            {% else %}
                            <p class="fs-xs"><a href="{{ route('camp.applicant.form', { camp: item.slug }) }}" target="_camp_{{ item.id }}_application_form" class="link">{{ route('camp.applicant.form', { camp: item.slug }) }}</a></p>
                            {% endif %}
                            {% if item.status != 'published' %}
                                <p class="fw-500 fs-xs color-red">{{ trans('Yayında olmadığı için başvurular kapalı') }}</p>
                            {% elseif item.is_applicable == false %}
                                <p class="fw-500 fs-xs color-red">{{ trans('Başvuru kabul edebilir seçeneği kapalı') }}</p>
                            {% elseif item.expiry_at != null and item.expiry_at <= "now"|date_iso('YYYY-MM-DD HH:mm') %}
                                <p class="fw-500 fs-xs color-red">{{ trans('Son başvuru kabul tarihi geçmiş') }}</p>
                            {% else %}
                                <p class="fw-500 fs-xs color-green">{{ trans('Başvuru alabilir') }}</p>
                            {% endif %}
                        </div>
                    </div>
                    <div class="modal-footer pb-3 d-flex justify-content-between">
                        <div>
                            <p class="fs-xs"><a class="link" href="{{ route('admin.application.index', {'filter[camp.id]': item.id}) }}">{{ trans('Başvurularına git') }}</a></p>
                            <p class="fs-xs color-grey">{{ trans(total_applications_label) }}</p>
                        </div>
                        <p class="fs-xs"><a class="link" href="{{ route('admin.application.export', {'filter[camp.id]': item.id}) }}">{{ trans('Başvuruları dışa aktar') }}</a></p>
                    </div>
                    <div class="modal-footer pb-3">
                        <p class="fs-xs fw-500 pb-2">{{ trans('Google Sheets Aktarım Linki') }}</p>

                        <div class="form-input form-input--small d-flex align-items-center">
                            <input id="google_sheet_url" type="text" class="form-control" readonly="readonly" value="{{ item.google_sheet_url }}">
                            <button type="button" class="button ms-2" onclick="return __copyInput(this)" data-copy-id="google_sheet_url">Kopyala</button>
                        </div>

                    </div>
                {% endif %} 
            </div>#}

            {# Kamp Kategorileri #}
            {# <div class="col-12 form modal mt-4">
                <div class="modal-header">
                    <h5>{{ trans('Kategori') }}</h5>
                </div>
                <div class="modal-body py-2">
                    <div class="form-row">
                        <div class="form-control-wrapper">
                            <div class="form-input">
                                {% for value, label in categories %}
                                <div class="my-2">
                                    <label class="checkbox">
                                        <input type="checkbox"
                                            name="categories[]"
                                            value="{{ value }}"
                                            {% if value in item.categories.pluck('title', 'id').toArray()|keys %}
                                                checked
                                            {% endif %}
                                            >
                                        <div class="checkbox-control">
                                            <span class="checkbox-tick"></span>
                                            <span class="checkbox-text">{{ trans(label) }}</span>
                                        </div>
                                    </label>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div> #}

            {# Eğitmenler #}
            {# <div class="col-12 form modal mt-4">
                <div class="modal-header">
                    <h5>{{ trans('Eğitmenler') }}</h5>
                </div>
                <div class="modal-body py-2">
                    <div class="form-row">
                        <div class="form-input">
                            <select class="form-control" name="educators[]" id="educator_selector" placeholder="trans('Eğitmen Seçin')" multiple>
                                {% for educator in item.educators %}
                                    <option value="{{ educator.id }}" selected>{{ trans(educator.title) }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>
            </div> #}

            {# Sorumlu #}
            {# {% if user.role == "moderator" %}
            <div class="col-12 form modal mt-4 d-none">
                <div class="modal-header">
                    <h5>{{ trans('Sorumlular') }}</h5>
                </div>
                <div class="modal-body py-2">
                    <div class="form-row">
                        <div class="form-input">
                            <select class="form-control" name="users[]" id="user_selector" multiple data-users={{ users }} >
                                {% for user in users %}
                                    <option
                                        value="{{ user.id }}"
                                        {% if user.id in camp_users %} selected {% endif %}
                                        >
                                        {{ user.name }}
                                        </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="col-12 form modal mt-4">
                <div class="modal-header">
                    <h5>{{ trans('Sorumlular') }}</h5>
                </div>
                <div class="modal-body py-2">
                    <div class="form-row">
                        <div class="form-input">
                            <select class="form-control" name="users[]" id="user_selector" multiple data-users={{ users }} >
                                {% for user in users %}
                                    <option
                                        value="{{ user.id }}"
                                        {% if user.id in camp_users %} selected {% endif %}
                                        >
                                        {{ user.name }}
                                        </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %} #}

            
            {# Kamp Görselleri #}
            <div class="col-12 form modal mt-4" id="cart" style="display: none;">
                <div class="cart" >
                    <h4 class="cart-header">
                        <span>{{ trans('Sepet') }}</span>
                        {# <span class="cart-badge">3</span> #}
                    </h4>
                    <ul class="cart-list">
                        <li style="display:none" class="cart-item">
                            <div>
                                <h6 class="cart-item-title" >{{ trans('Paket Number') }}</h6>
                            </div>
                            <span class="cart-item-price" id="id-span"></span>
                        </li>
                        <li class="cart-item">
                            <div>
                                <h6 class="cart-item-title" >{{ trans('Paket Adı') }}</h6>
                            </div>
                            <span class="cart-item-price" id="name-span"></span>
                        </li>
                        <li class="cart-item">
                            <div>
                                <h6 class="cart-item-title">{{ trans('Kullanıcı Limiti') }}</h6>
                            </div>
                            <span class="cart-item-price" id="user-span"></span>
                        </li>
                        <li class="cart-item">
                            <div>
                                <h6 class="cart-item-title">{{ trans('Fiyat') }}</h6>
                            </div>
                            <span class="cart-item-price" id="price-span"></span>
                        </li>
                        {# <li class="cart-item promo">
                            <div class="promo-code">
                                <h6 class="promo-title">{{ trans('Kupon Kodu') }}</h6>
                                {# <small>EXAMPLECODE</small>
                            </div>
                            <span class="promo-discount" id="promo-span"></span>
                        </li>  #}
                        <li class="cart-item total">
                            <span>{{ trans('Ödenecek Tutar') }}</span>
                            <strong id="total-span"></strong>
                        </li>
                    </ul>
                    {# <form class="promo-form">
                        <div class="promo-input-group">
                            <input type="text" class="promo-input" placeholder="Promo code">
                            <button type="submit" class="promo-button">Redeem</button>
                        </div>
                    </form> #}
                    <!-- Payment Button -->
                    {# <div class="mt-4">
                        <button type="button" class="custom-red-btn button" onclick="redirectToPayment()">Proceed to Payment</button>
                    </div> #}
                </div>  
            </div>

            {# Kamp Başlangıç - Bitiş Tarihi #}
            {# <div class="col-12 form modal mt-4">
                <div class="modal-header mb-2">
                    <h5>{{ trans('Program Tarihleri') }}</h5>
                </div>
                <div class="modal-body pb-2">
                    <div class="form-row">
                        <div class="form-control-wrapper">
                            <div class="form-label">
                                <label for="date_range">{{ trans('Başlangıç - Bitiş Tarihi') }}</label>
                            </div>
                            <div class="form-input">
                                <input id="date_range" type="text" name="date_range" value="{{ item.dateRange }}" class="form-control datetimepicker" data-mode="range" placeholder="{{ trans('Programın başlangıç ve bitiş tarih aralığı') }}" data-time="false" data-dateformat="Y-m-d">
                            </div>
                            <div class="form-description mb-0 text-right">
                                <a class="link fs-xs" href="#" onclick="document.getElementById('date_range')._flatpickr.clear(); return false;">{{ trans('Tarihi temizle') }}</a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer pb-3">
                    <p>{{ trans('Takvim üzerinde gözükecek tarih aralığıdır') }}</p>
                </div>
            </div>
            <div class="col-12 form modal mt-4">
                <div class="modal-header mb-2">
                    <h5>{{ trans('Program Saati') }}</h5>
                </div>
                <div class="modal-body pb-2">
                    <div class="form-row">
                        <div class="form-control-wrapper">
                            <div class="form-label">
                                <label for="date_range2">{{ trans('Başlangıç Saatini 00:00 formatında yazın') }}</label>
                            </div>
                            <div class="form-input">
                                <input name="date_time" type="text" class="form-control" value="{{ item.date_time }}" placeholder="{{ trans('Programın başlangıç saati') }}">
                            </div>
                            <div class="form-description mb-0 text-right">
                                <a class="link fs-xs" href="#" onclick="document.getElementById('date_range2')._flatpickr.clear(); return false;">{{ trans('Tarihi temizle') }}</a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer pb-3">
                    <p>{{ trans('Takvim üzerinde gözükecek tarih aralığıdır') }}</p>
                </div>
            </div> #}

        </div>


    </div>

</div>

{{ form_close() }}

    {# Kaynak destroy methoduna sahipse bunun için form oluştur #}
    {% if item and listing.actions.enabled and route_has('admin.' ~ listing.resource ~ '.destroy') %}
        {{ form_model(item, {'route': ['admin.' ~ listing.resource ~ '.destroy', item.id], 'method': 'DELETE', 'class': 'form-destroy-item'}) }}
        {{ form_close() }}
    {% endif %}

    <script>

    function redirectToPayment() {
        var selectedCard = document.querySelector('.selected-card');
        // id
        var package_id = selectedCard.getAttribute('data-id');
        window.location.href = '{{ route('admin.company.payment') }}?package_id=' + package_id;
    }

   document.addEventListener('DOMContentLoaded', function() {
    var cards = document.getElementsByClassName('card');
    var selectedPackageType = '{{ company.package_type }}';

    function removeSelectedClass() {
        for (var i = 0; i < cards.length; i++) {
            cards[i].classList.remove('selected-card');
        }
    }

    const cart = document.getElementById('cart');
    const idSpan = document.getElementById('id-span');
    const nameSpan = document.getElementById('name-span');
    const userSpan = document.getElementById('user-span');
    const priceSpan = document.getElementById('price-span');
    const totalSpan = document.getElementById('total-span');

    function updateCartDetails(id, title, users, price) {
        idSpan.innerHTML = `${id}`;
        nameSpan.innerHTML = `${title}`;
        userSpan.innerHTML = `${users}`;
        priceSpan.innerHTML = `${price}`;
        totalSpan.innerHTML = `${price}`; // Assuming total is the same as price for simplicity
        cart.style.display = 'block';
    }

    // Pre-select the card based on selectedPackageType
    for (var i = 0; i < cards.length; i++) {
        var card = cards[i];
        if (card.getAttribute('data-package-id') === selectedPackageType) {
            removeSelectedClass();
            card.classList.add('selected-card');
            // Update the cart details for the pre-selected card
            updateCartDetails(
                card.getAttribute('data-id'),
                card.getAttribute('data-title'),
                card.getAttribute('data-users'),
                card.getAttribute('data-price')
            );
        }

        // Add click event listener to each card
        card.addEventListener('click', function() {
            // Get data from the clicked card
            let id = this.getAttribute('data-id');
            let title = this.getAttribute('data-title');
            console.log(title);
            let users = this.getAttribute('data-users');
            let price = this.getAttribute('data-price');

            // Update the cart details
            updateCartDetails(id, title, users, price);

            // Highlight the selected card
            removeSelectedClass();
            this.classList.add('selected-card');
        });
    }
});
    </script>




<script>

    let isApplicableCheckbox = document.querySelector('[name="is_applicable"]');
    if (isApplicableCheckbox) {

        document.body.classList.toggle('is-applicable', isApplicableCheckbox.checked);

        isApplicableCheckbox.addEventListener('change', function() {
            document.body.classList.toggle('is-applicable', this.checked);
        });
    }

</script>


{% endblock %}
