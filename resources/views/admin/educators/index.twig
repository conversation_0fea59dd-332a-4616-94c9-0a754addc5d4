{% extends "admin/html.twig" %}

{% block body %}

<style>
  @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap');
  @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700;900&display=swap');

  :root{
          --roboto: 'Roboto', sans-serif;
          --poppins: 'Poppins', sans-serif;
    }

        @media (max-width: 991px){
        .educators-table{
            overflow-y: auto !important;
        }
        .user-image{
          border-radius: 50%;
          width: 40px !important;
          height: 40px !important;
        }

        .custom-search-bar input{
          width: 100px !important;
        }
        .custom-add-new{
          margin-top: 0.75rem !important;
        }
    }


  h4{
    font-family: var(--poppins);
    font-size: 20px;
    font-weight: 500;
    letter-spacing: 0.3px;

  }
  .custom-search-bar{
    background-color: rgba(241, 241, 245, 1);
    border-radius: 1px;
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    margin-top: 0.75rem;
    max-width: 300px;
  }

  .custom-search-bar input{
    background-color: transparent;
    outline: none;
    border: none;
    margin-left: 0.5rem;
  }

  .custom-search-bar input::placeholder{
    font-family: var(--roboto);
    font-size: 12px;
    font-weight: 400;
    color: rgba(105, 105, 116, 1);
  }

        .user-image{
          border-radius: 50%;
          width: 60px;
          height: 60px;
        }

        .user-image img{
          border-radius: 50%;
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .admin-grid h6{
          font-family: var(--roboto);
          font-size: 15px;
          font-weight: 500;
          color: rgba(38, 70, 83, 1);
          text-align: center;
        }

        .admin-grid small{
          border-radius: 4px;
          padding: 4px 6px 4px 6px;
          font-weight: 300;
          font-family: var(--roboto);
          margin-block: 0.75rem;
          text-align: center;
        }

        .admin-grid button{
          border-radius: 3px;
          background-color: rgba(38, 45, 51, 1);
          color: white;
          font-family: var(--roboto);
          font-weight: 400;
          cursor: pointer;
          padding: 10px;
          text-align: center;
          transition: all 0.3s ease-in-out;
          border: 1px solid transparent;
        }

        .admin-grid button:hover{
          background-color: transparent;
          border: 1px solid rgba(38, 45, 51, 1);
          color: rgba(38, 45, 51, 1);
        }


    .custom-add-new{
        cursor: pointer;
        outline: none;
        border: 1px solid white;
        padding: 0.75rem 1rem;
        display: flex;
        align-items: center;
        background-color: rgba(186, 39, 45, 1);
        border-radius: 8px;
        transition: all 0.3s ease-in-out;
    }

    .custom-add-new:hover{
        border: 1px solid rgba(186, 39, 45, 1);
        background-color: white;
    }

    .custom-add-new:hover span, .custom-add-new:hover .iconify{
        color: rgba(186, 39, 45, 1);
    }


    .custom-add-new span{
        font-family: var(--roboto);
        font-size: 12px;
        font-weight: 400;
        color: white;
        text-decoration: underline;
        margin-left: 0.25rem;
    }

    .custom-add-new .iconify{
        font-size: 18px;
        color: white;
    }

    table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    font-size: 18px;
    text-align: left;
}

th, td {
    padding: 12px;
}

table tr td{
  vertical-align: middle;
}

th{
  color: rgba(146, 146, 157, 1);
  font-family: var(--roboto);
  font-size: 14px;
  font-weight: 500;
}

td{
  font-family: var(--roboto);
  font-size: 14px;
  font-weight: 500;
  color: rgba(38, 45, 51, 1);
}

.pagination{
  display: flex;
  justify-content: center;
  margin-block: 1rem;
}

.pagination-list .before-after{
  font-family: var(--roboto);
  font-size: 14px;

}

.pagination-list .page-numbers{
  margin-inline: 0.5rem;
  padding: 0.35rem 0.6rem;
  font-size: 14px;
  border-radius: 4px;
}

.pagination .page-numbers.active{
  background-color: rgba(186, 39, 45, 1) !important;
}

.custom-search-result{
  padding: 0.4rem 1rem;
    background-color: rgba(241, 241, 245, 1);
    border-radius: 1px;
    display: flex;
    align-items: center;
    margin-top: 0.75rem;
    max-width: 300px;
    margin-left: 1rem;
}
.custom-search-result span{
  font-family: var(--roboto);
  font-size: 14px;
  font-weight: 500;
  color: black;
  display: flex;
  align-items: center;
}

.custom-search-result .iconify{
  color: rgba(186, 39, 45, 1);
  cursor: pointer;
  font-size: 20px;
  margin-top: 0.1rem;
}

.custom-search-result-name{
  background-color: rgb(215, 215, 215);
  padding: 0.15rem 0.75rem;
  font-size: 13px;
}
</style>

  <div class="container modal px-4 mt-4">
    <h4> {{trans('Eğitmenler')}} </h4>
    <div class="d-flex align-items-center justify-content-between">
      <div class="d-flex align-items-center">
       <form method="GET" action="/admin/educators">
         <div class="custom-search-bar">
           <div class="d-flex align-items-center">
             <span class="iconify" data-icon="ph:magnifying-glass-thin" data-inline="false"></span>
             <input placeholder=" {{trans('Ara')}} " name="q" type="text">
           </div>
         </div>

        </form>
        {% if app.request.query.has('q') %}
        <div class="custom-search-result">
         <span>{{trans('Aradığınız Kişi:')}}
           <div class="custom-search-result-name ms-2 d-flex align-items-center">{{ app.request.query.get('q') }}
          <a href="{{ route('admin.educators.index') }}">
           <span class="iconify ms-1" data-icon="basil:cross-outline" data-inline="false">
          </a>
           </span>
         </div>
         </span>
        </div>
        {% endif %}
      </div>
      <div class="d-flex">
          {% if admin_user.super_admin == 1 %}
              <a class="button button--outline me-2" style="height: auto;padding: 0.75rem 1rem;border-radius: 8px;" href="{{ route('admin.instructors.export') }}">
                  <span class="iconify me-2" style="font-size: 20px;" data-icon="ph:microsoft-excel-logo-thin" data-inline="false"></span>
                  {{trans('Excel Dışa Aktar')}}
              </a>
          {% endif %}
          <a href="/admin/user/create">
              <button class="custom-add-new">
                  <span class="iconify" data-icon="material-symbols:add-rounded" data-inline="false"></span>
                  <span> {{trans('Yeni Oluştur')}} </span>
              </button>
          </a>
      </div>
      </div>
      <div class="educators-table">
        <table>

          <thead>
              <tr>
                  <th class="text-center"><span class="iconify" data-icon="bi:people" data-inline="false"></span></th>
                  <th> {{trans('Eğitmen Adı')}} </th>
                  <th> {{trans('E-Posta Adresi')}} </th>
                  <th> {{trans('Link')}} </th>
                  <th> {{trans('Kategori')}} </th>
                  <th> {{trans('Katılma Tarihi')}} </th>
              </tr>
          </thead>
          <tbody>
            {% for educator in educators %}
                <tr>
                  <td class="user-image">
                    {% if educator.image_id %}
                    <img src="https://admin.istegelis.com{{ educator.image.url }}" alt="">
                    {% else %}
                    <img src="https://www.canterburysociety.org.uk/wp-content/uploads/2020/04/person-placeholder.jpg" alt="">
                    {% endif %}
                  </td>
                  <td>
                    <a href="/admin/educator/{{educator.id}}/edit">
                      {{educator.title}}
                    </a>
                  </td>
                  <td>  {{educator.email}} </td>
                  <td>

                  </td>
                  <td>
                    {% if educator.category %}
                    <small>{{ trans(admin.category.title) }}</small>
                    {% else %}
                        <small> {{trans('Genel')}} </small>
                    {% endif %}
                  </td>
                  <td> {{educator.formatted_created_at }} </td>
                </tr>
            {% endfor %}
          </tbody>
      </table>
      </div>
      {% if not app.request.query.has('q') %}
      <div class="pagination">
        {% if educators.lastPage() > 1 %}
        <ul style="display:flex; align-items: center;" class="pagination-list">
            {% if educators.currentPage() > 1 %}
            <li class="before-after"><a href="{{ educators.previousPageUrl() }}"> {{trans('Önceki')}} </a></li>
            {% endif %}

            {% for page in 1..educators.lastPage() %}
            <li class="page-numbers {{ page == educators.currentPage() ? 'active' : '' }}">
                <a href="{{ educators.url(page) }}">{{ page }}</a>
            </li>
            {% endfor %}

            {% if educators.currentPage() < educators.lastPage() %}
            <li class="before-after"><a href="{{ educators.nextPageUrl() }}"> {{trans('Sonraki')}} </a></li>
            {% endif %}
        </ul>
        {% endif %}
    </div>
    {% endif %}
  </div>

  {% endblock %}
