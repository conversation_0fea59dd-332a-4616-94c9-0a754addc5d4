{% extends "admin/html.twig" %}

{% block body %}


<style>

@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700;900&display=swap');

    :root{
          --white: #fafafa;
          --red: #ba272d;
          --softblack: #434551;
          --roboto: 'Roboto', sans-serif ;
    }

        .modal-action-group--push-right{
            margin-right: 0 !important;
        }


            @media (max-width:460px){
        .modal-header{
            display:flex !important;
            flex-direction:column !important;
            width:100% !important;
        }
        .modal-header .button--small{
            margin:0 !important;
            margin-top: 1rem !important;
        }
        .modal-header h4{
            font-size: 20px !important;
        }
        .table-checkbox{
        width:0;
        display:none;
    }
    .table-cell{
        padding:0;
    }
    .table-item{
        margin-top: 1rem;
    }
    .table-preview{
        width: 100%;
        height: 10vh;
                margin-top:1rem;
    }
    .table-item{
        display:inline;
    }
    .table-keep{
        width:100%;
        display:inline !important;
    }
    .table-preview+.table-details{
        padding:0 !important;
    }
    }

     @media (max-width:330px){
    .sidebar-content, .sidebar{
        width: auto;
    }

    .sidebar-item{
        overflow:hidden;
        width:1.3rem;
        height:1.3rem;
        margin-block:1rem;
    }

    .main-content{
        padding:0;
        margin:0;
        margin-block: 1rem;

    }
    .first-col, .second-col{
            width: 100% !important;
            justify-content:start !important;
        }
                .table-checkbox{
        width:0;
        display:none;
    }
    .table-cell{
        padding:0;
    }
    .table-item{
        margin-top: 1rem;
    }
    .table-preview{
        width:100%;
        height: 10vh;
    }

    }


    @media (min-width:330.02px) and (max-width: 499.98px){

    .sidebar-content, .sidebar{
        width: auto;
    }

    .sidebar-item{
        overflow:hidden;
        width:1.6rem;
        height:1.6rem;
        margin-block:1rem;
    }

    .main-content{
        padding:0;
        margin:0;
        margin-top: 1rem;

    }

   }
   @media (min-width:430.02px) and (max-width: 500px){
    .sidebar-item{
        overflow:hidden;
        width:1.5rem;
        height:1.5rem;
        margin-block:1rem;
    }
    .sidebar-content, .sidebar{
        max-width: 80% ;
    }
        .main-content{
        padding:0;
        margin:0;
        margin-top: 1rem;

    }
   }
   @media (min-width: 501px) and (max-width: 576px){

    .sidebar-content, .sidebar{
        width: auto;
    }

    .sidebar-item{
        overflow:hidden;
    }

    .main-content{
        padding:0;
        margin:0;
        margin-block: 1rem;

    }
   }

    @media (max-width: 576px){
        .first-col, .second-col{
            width: 50%;
            display:flex;
            justify-content:center;
        }
    }

    @media (min-width:576.02px) and (max-width:767px){
    .main-content{
        padding:0;
        margin:0;
        margin-block: 1rem;
        width:100%;
    }
    .row>*{
        padding-left:0 !important;
    }
     .sidebar-item{
        width:2.5rem;
        height:2.5rem;
        margin-block:0.75rem;
        margin-left: 0.5rem;
    }
    .sidebar-content, .sidebar{
        width: 85%;
    }
    .modal-actions{
            display:flex;
            flex-direction:column;
            align-items:start;
        }
        .modal-action-col{
            width:auto;
        }
        .arrows, .fs-sm{
            width:100%;
        }
    }


    @media (min-width:576.02px) and (max-width:1275px){
        .modal-actions{
            display:flex !important;
            flex-direction:column;
            justify-content:center !important;
            align-items:center !important;
        }
        .first-row, .second-row{
            margin-block:1rem;
        }



    }

    @media (min-width:576.02px) and (max-width: 767px){

.row{
    padding:0;
    margin:0;
}


    }

    @media (min-width:400px) and (max-width: 767px){
        .my-row{
    display:flex;
    flex-direction:row;
}
    }

    @media(min-width:1750px){
        .main-content{
        padding:0;
        margin:0;
        margin-block: 1rem;
        width:100%;
        }
    }


    .add-new {
        background: var(--white) !important;
        border: solid 1px var(--softblack);
        border-radius: 8px;
        color: var(--red) !important;
       padding: 1.5rem !important;
                font-family: 'Roboto', sans-serif !important;
        font-weight:bold !important;
        font-size:14px !important;
    }

    .add-new:hover{
        background: var(--red) !important;
        color: var(--white) !important;
        transition: all .3s ease-in-out;
    }

    html{
        font-family: var(--roboto) !important;
        font-weight: bold !important;
    }


    .table-details{
        padding : 1rem;
    }
</style>

    <div class="container mx-auto">
        <div class="row form-modal">
            <div class="row">
                <div class="col-12 mx-auto">
                    {% include "_messageBag.twig" %}
                </div>
            </div>

            {% if item %}
                {{ form_model(item, {'route': ['admin.' ~ listing.resource ~ '.update', item.id], 'method': 'PUT'}) }}
            {% else %}
                {{ form_model(null, {'route': ['admin.' ~ listing.resource ~ '.store']}) }}
            {% endif %}
            {# Başlık #}
            <div class="col-12">
                <div class="row">
                    <div class="col-12">
                        <div class="modal py-3">
                            <div class="modal-header">
                                <div class="d-flex align-items-md-center justify-content-between flex-col flex-md-row">
                                    <h4>{{ listing.title ? trans(listing.title) ~ ' ' }}{{ item ? trans('Güncelle') : trans('Oluştur') }}</h4>
                                    <div class="d-flex flex-col flex-md-row align-items-start align-items-md-center  justify-content-between my-row">
                                        {% if item and listing.actions.enabled %}
                                            <div class="actions">
                                                <button type="button" data-options="{{ listing.actions.option }}" class="button button--big button--transparent"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="none" fill-rule="evenodd" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 2.75c5.1 0 9.25 4.14 9.25 9.24 0 5.1-4.15 9.25-9.25 9.25 -5.11 0-9.26-4.15-9.26-9.25 0-5.11 4.14-9.25 9.25-9.25Z"></path><path fill="currentColor" d="M7.52 13.19c-.67 0-1.2-.54-1.2-1.2 0-.66.53-1.2 1.19-1.2 .66 0 1.19.53 1.19 1.19 0 .66-.54 1.19-1.2 1.19Z"></path><path fill="currentColor" d="M12 13.19c-.67 0-1.2-.54-1.2-1.2 0-.66.53-1.2 1.19-1.2 .66 0 1.19.53 1.19 1.19 0 .66-.54 1.19-1.2 1.19Z"></path><path fill="currentColor" d="M16.47 13.19c-.67 0-1.2-.54-1.2-1.2 0-.66.53-1.2 1.19-1.2 .66 0 1.19.53 1.19 1.19 0 .66-.54 1.19-1.2 1.19Z"></path></svg></button>
                                            </div>
                                        {% endif %}
                                        <button type="submit" class="button button--big px-4 ms-2 add-new">{{ item ? trans('Güncelle') : trans('Oluştur') }}</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-12">

                <div class="row">

                    {# Sol taraf #}
                    <div class="col-12 col-md-8" spellcheck="false">

                        <div class="row">

                            <div class="col-12">

                                <div class="col-12 form modal mt-4" spellcheck="false">
                                    <div class="modal-body">
                                        <div class="form-row">
                                            <div class="form-control-wrapper">
                                                <div class="form-label">
                                                    {{ form_label('title', trans('Ad Soyad')) }}
                                                </div>
                                                <div class="form-input">
                                                    {{ form_text('title', null, {'class': 'form-control' }) }}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-row">
                                            <div class="form-control-wrapper">
                                                <div class="form-label">
                                                    {{ form_label('content', trans('Açıklama')) }}
                                                </div>
                                                <div class="form-input">
                                                    {{ form_textarea('content', null, {'class': 'editor tinymce d-none'}) }}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-row">
                                            <div class="form-control-wrapper">
                                                <div class="form-label">
                                                    {{ form_label('excerpt', trans('Özet')) }}
                                                </div>
                                                <div class="form-input">
                                                    {{ form_textarea('excerpt', null, {'class': 'form-control'}) }}
                                                </div>
                                                <div class="form-description">
                                                    <p class="fs-sm">
                                                        <span class="fw-500">{{ trans('Özet') }}</span>: {{ trans('Sosyal medya paylaşımları gibi önizleme yapılacak yerlerde gözükecek açıklamadır.') }}</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-row">
                                            <div class="form-control-wrapper">
                                                <div class="form-label">
                                                    {{ form_label('remote_url', trans('Eğitmen Linki')) }}
                                                </div>
                                                <div class="form-input">
                                                    {{ form_text('remote_url', null, {'class': 'form-control' }) }}
                                                </div>
                                                <div class="form-description">
                                                    <p class="fs-sm">{{ trans('Eğitmenin bilgilerinin olduğu uzak bağlantı linki') }}</p>
                                                </div>
                                            </div>
                                        </div>
                                        {% if courseList | length > 0 %}
                                            <div class="form-label">
                                                {{ form_label('', trans('Eğitmen Kursları')) }}
                                            </div>
                                            <div class="table">
                                                <div class="table-header">
                                                    <div class="table-row table-row-head">
                                                        <div class="table-cell table-keep">{{ trans('Kurs Adı') }}</div>
                                                    </div>
                                                </div>
                                                {% for course in courseList %}
                                                    <div class="table-items">
                                                        <div class="table-row" data-item-id="{{ course.id }}">
                                                            <div class="table-cell table-keep py-2">
                                                                <a class="table-item" href="{{ route('admin.course.edit', course.id) }}">
                                                                    <div class="table-preview table-preview--ar-5:3" style="background-image: url({{ course.image.url }})"></div>
                                                                    <div class="table-details">
                                                                        <div class="table-title">{{ course.title }}</div>

                                                                        {% if course.course_sessions|length > 0 %}
                                                                            <div class="table-subtitle">
                                                                                Başlangıç Tarihi: {{ course.course_sessions[0].date|date("d.m.Y") }}
                                                                            </div>
                                                                        {% endif %}
                                                                    </div>
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>

                            </div>

                        </div>

                    </div>

                    {# Sağ taraf #}
                    <div class="col-12 col-md-4 mt-4">
                        <div class="modal">
                            <div class="modal-header">
                                <h5> {{trans('Medya / Görseller')}} </h5>
                            </div>
                            <div class="modal-body">
                                <input name="image" type="hidden" value="{{ item.image_id }}">
                                <div class="form-media-items form-media--single {{ item.image ? 'form-media-items--has-image' }}" ar="1:1">
                                    {% if item.image %}
                                        <div class="form-media-item" data-media-hash="{{ item.image.hash }}" data-image-url="{{ item.image.url }}" data-media-id="{{ item.image.id }}">
                                            <div class="form-media-item-overlay ">
                                                <button type="button" class="form-media-item-remove button button--transparent">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewbox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                    </svg>
                                                </button>
                                            </div>
                                            <div class="form-media-item-content">
                                                <img src="{{ item.image.url }}">
                                            </div>
                                        </div>
                                    {% endif %}
                                    <button type="button" class="form-media-item-add">
                                        <div class="form-media-item-content">
                                            <span class="click-text"> {{trans('Yeni görsel ekle')}} </span>
                                            <span class="drop-text"> {{trans('Görselleri buraya bırakın')}} </span>
                                        </div>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="modal">
                            <div class="modal-header">
                                <h5> {{trans('Eğitmen Notu')}} </h5>
                            </div>
                            <div class="modal-body">
                                <div class="form-input">
                                    {{ form_textarea('note', null, {'class': 'form-control'}) }}
                                </div>
                                <div class="form-description">
                                    <p class="fs-sm">
                                        <span class="fw-500">{{ trans('Özet') }}</span>: {{ trans('Bu not yalnızca süper admin tarafından görüntülenir.') }}</p>
                                </div>
                            </div>
                        </div>


                    </div>

                </div>

            </div>
            {{ form_close() }}
        </div>
        {# {% block footer %}{% endblock %} #}
    </div>

    {% if item and listing.actions.enabled and route_has('admin.' ~ listing.resource ~ '.destroy') %}
        {{ form_model(item, {'route': ['admin.' ~ listing.resource ~ '.destroy', item.id], 'method': 'DELETE', 'class': 'form-destroy-item'}) }}
        {{ form_close() }}
    {% endif %}

{% endblock %}
