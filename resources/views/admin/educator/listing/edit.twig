{% extends "admin/html.twig" %}
{% block body %}

<div class="container mx-auto">
    <div class="row form-modal">
        <div class="col-12 col-md-7 px-0 mx-auto">
            {% include "_messageBag.twig" %}
        </div>
        <div class="col-12 col-md-7 form modal mx-auto" spellcheck="false">
            <div class="modal-header">
                <h4>{{ listing.title ? listing.title ~ ' ' }}{{ item ? 'Düzenle' : 'Oluştur' }}</h4>
            </div>
            <div class="modal-body">
                {% if item %}
                {{ form_model(item, {'route': ['admin.' ~ listing.resource ~ '.update', item.id], 'method': 'PUT'}) }}
                {% else %}
                {{ form_model(null, {'route': ['admin.' ~ listing.resource ~ '.store']}) }}
                {% endif %}

                {% block form %}

                {% endblock %}

                <div class="form-row">
                    <div class="form-control-wrapper text-right">
                    {# Kaynak nesne aksiyonlarını destekliyorsa ve eğer varsayılan aksiyon seçili ise (silme) destekliyorsa seçenekler (aksiyonlar) menüsünü oluştur #}
                    {% if item and listing.actions.enabled %}
                        <div class="actions">
                            <button type="button" data-options="{{ listing.actions.option }}" class="button button--big button--transparent"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="none" fill-rule="evenodd" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 2.75c5.1 0 9.25 4.14 9.25 9.24 0 5.1-4.15 9.25-9.25 9.25 -5.11 0-9.26-4.15-9.26-9.25 0-5.11 4.14-9.25 9.25-9.25Z"></path><path fill="currentColor" d="M7.52 13.19c-.67 0-1.2-.54-1.2-1.2 0-.66.53-1.2 1.19-1.2 .66 0 1.19.53 1.19 1.19 0 .66-.54 1.19-1.2 1.19Z"></path><path fill="currentColor" d="M12 13.19c-.67 0-1.2-.54-1.2-1.2 0-.66.53-1.2 1.19-1.2 .66 0 1.19.53 1.19 1.19 0 .66-.54 1.19-1.2 1.19Z"></path><path fill="currentColor" d="M16.47 13.19c-.67 0-1.2-.54-1.2-1.2 0-.66.53-1.2 1.19-1.2 .66 0 1.19.53 1.19 1.19 0 .66-.54 1.19-1.2 1.19Z"></path></svg></button>
                        </div>
                    {% endif %}
                        <button type="submit" class="button button--big">{{ item ? 'Güncelle' : 'Oluştur' }}</button>
                    </div>
                </div>

                {{ form_close() }}
            </div>
        </div>
    </div>
    {% block footer %}{% endblock %}
</div>

{# Kaynak destroy methoduna sahipse bunun için form oluştur #}
{% if item and listing.actions.enabled and route_has('admin.' ~ listing.resource ~ '.destroy') %}
{{ form_model(item, {'route': ['admin.' ~ listing.resource ~ '.destroy', item.id], 'method': 'DELETE', 'class': 'form-destroy-item'}) }}
{{ form_close() }}
{% endif %}

{% endblock %}
