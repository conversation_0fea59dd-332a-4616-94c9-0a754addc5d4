<?php

return [

    /*
    |--------------------------------------------------------------------------
    | API Hata Mesajı Kodları
    |--------------------------------------------------------------------------
    */

    /**
     * Auth
     */
    120 => 'Geçersiz veya yanlış SMS Onay Kodu.',
    121 => 'Bu telefon numarasına ait kayıt bulunamadı.',
    122 => 'Eksik veya hatalı telefon numarası.',
    124 => 'Bu telefon numarasına zaten kayıtlı.',
    125 => 'Bu telefon numarasına SMS gönderilemez.',


    /**
     * Profile
     */
    140 => '<PERSON>sim boş olamaz.',
    142 => 'Telefon numarası SMS almak için uygun değil. +90 (5--) --- ---- formatında bir telefon numarası giriniz.',
    143 => 'Bu telefon numarası zaten kullanımda.',
    144 => 'E-Posta adresi geçerli değil.',
    145 => 'Bu E-Posta adresi zaten kullanımda.',
    146 => 'Bu hesap zaten silinmiş.',
    147 => 'Bu hesap kullanıcı isteği üzerine silinmiştir, detaylar için iletişime geçin.',

    /**
     * Doktor auth
     */
    160 => 'Bu hesabınız Doktor olarak kayıtlıdır.',

    /**
     * Cart
     */
    204 => 'Ürün bulunamadı.',

    /**
     * Address
     */
    214 => 'Adres bulunamadı.',

    /**
     * Product
     */
    220 => 'Ürün bulunamadı veya yayından kaldırıldı.',

    /**
     * Campaign
     */
    230 => 'Kampanya bulunamadı.',
    231 => 'Kampanya süresi dolmuş.',

    /**
     * Brand
     */
     241 => 'Marka bulunamadı.',

    /**
     * Order
     */
    300 => 'Sipariş bulunamadı.',
    301 => 'Geçersiz durum.',

    /**
     * Checkout
     */
    310 => 'Sepetinizde ürün bulunmamakta.',
    320 => 'Desteklenmeyen ödeme yöntemi.',
    321 => 'Desteklenmeyen kargo yöntemi.',
    322 => 'Adres belirtmediniz veya adres size ait değil.',
    330 => 'Bu siparişe artık ödeme yapılamaz.',

    /**
     * Checkout Authorization
     */
    430 => 'İşlem imzası eksik.',
    431 => 'İşlem imzası geçersiz.',
    432 => 'Geçersiz siparişe işlem.',
    433 => 'İşlem zaman aşımına uğramış, sayfayı kapatarak tekrar deneyin.',

    /**
     * Currency
     */
    1200 => 'Böyle bir para birimi bulunamadı.',

    /**
     * Data Processing
     */
    422 => 'Verilen veriler geçersiz veya eksik.',

    /**
     * API Token
     */
    9999 => 'Token geçersiz.',
];
