<?php

namespace App\Scopes;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;
use Illuminate\Support\Facades\Auth;

class CompanyScope implements Scope
{
    public function apply(Builder $builder, Model $model)
    {
        if (Auth::check()) {
            $companyId = Auth::user()->company_id;
            if(Auth::user()->super_admin == 1) {
                return;
            }
            $builder->where($model->getTable() . '.company_id', $companyId);
        }
    }
}
