<?php
namespace App\Enums;

class MailTemplateType
{
    // const ACCOUNT_CREATED = 'account_created';
    // const PASSWORD_RESET = 'password_reset';
    // const CLASS_REGISTERED = 'class_registered';
    // const MENTOR_MESSAGE = 'mentor_message';
    // const USER_MESSAGE = 'user_message';
    // const PRIVATE_ROOM_CREATED = 'private_room_created';
    const ONE_DAY_REMINDER = 'one_day_reminder';
    const FIRST_REMINDER = 'first_reminder';
    const LAST_REMINDER = 'last_reminder';
    const USER_CREATE_SUCCESS = 'user_create_success';
    const COMPANY_CREATE_SUCCESS = 'company_create_success';
    const USER_FEEDBACK = 'user_feedback';

    private static $labels = [
        // self::ACCOUNT_CREATED => 'Hesap Oluşturuldu',
        // self::PASSWORD_RESET => '<PERSON><PERSON><PERSON><PERSON>',
        // self::CLASS_REGISTERED => 'Canlı Derse Kayıt Alındı',
        // self::MENTOR_MESSAGE => 'Mentora Mesaj İletildi',
        // self::USER_MESSAGE => 'Kullanıcı Mesaj İletildi',
        // self::PRIVATE_ROOM_CREATED => 'Özel Oda Oluşturuldu',
        self::ONE_DAY_REMINDER => '1 Gün Kala Hatırlatma',
        self::FIRST_REMINDER => 'İlk Hatırlatma',
        self::LAST_REMINDER => 'Son Hatırlatma',
        self::USER_CREATE_SUCCESS => 'Kullanıcı Oluşturuldu',
        self::COMPANY_CREATE_SUCCESS => 'Şirket Oluşturuldu',
        self::USER_FEEDBACK => 'Kullanıcı Geri Bildirimi',
    ];

    public static function label(string $value): string
    {
        return self::$labels[$value] ?? '';
    }

    public static function labels(): array
    {
        return self::$labels;
    }
}
