<?php
namespace App\Enums;

class CourseStatus
{
    const DRAFT = -1;
    const PUBLISHED = 1;
    const CLOSED = 0;

    private static $labels = [
        self::DRAFT => '<PERSON>lindi',
        self::PUBLISHED => 'Yayında',
        self::CLOSED => 'Taslak',
    ];

    public static function label(string $value): string
    {
        return self::$labels[$value] ?? '';
    }

    public static function labels(): array
    {
        return self::$labels;
    }
}
