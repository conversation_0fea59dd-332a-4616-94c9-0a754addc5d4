<?php

namespace App\Services\SMS;

use App\Services\SMS\Contracts\Deliverer;
use Illuminate\Support\Facades\Http;
use \Illuminate\Contracts\Container\Container;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Log;

class VerimorDriver extends Deliverer {
    
    protected $endpoint = 'sms.verimor.com.tr/v2';

    protected $payload = [];

    public function __construct(Container $container) {
   	$this->container = $container;
        $this->config = $container->make('config');
    }

    public function send() {
        $this->buildPayload();
        
        $url = 'http://' . $this->endpoint . '/send.json';
        $response = Http::post($url, $this->payload);
        
        try {
            $response->throw();
        } catch (RequestException $e) {
            return false;
        }

        return true;
    }

    private function buildPayload() {
        $config = $this->config->get('services.verimor');

        $this->payload = [
            'username'    => $config['username'],
            'password'    => $config['password'],
            'source_addr' => $this->from,
            'datacoding'  => '1',
        ];

        if ($this->at) {
            $this->payload['send_at'] = $this->at;
        }

        $this->payload['messages'] = $this->body;
    }

    /**
     * @see https://github.com/verimor/SMS-API/blob/master/user_guide.md
     * Craft message body according to Verimor
     *
     * @return void
     */
    public function getBodyAttribute() {

        $to = implode(',', $this->to);

        $messages = [];

        foreach ($this->message_list as $message) {
            $messages[] = [
                'msg'  => $message,
                'dest' => $to
            ];
        }

        return $messages;
    }

}
