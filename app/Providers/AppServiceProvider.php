<?php

namespace App\Providers;

use App\Models\Category;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\URL;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {

        if (env('CODESPACE_NAME')) {
            \URL::forceScheme('https');

            if (env('APP_URL')) URL::forceRootUrl(env('APP_URL'));
        }

        $this->defineWhereLike();
        $this->composeMainMenu();
        $this->composeLocale();
        $this->composeAdminEdits();
        $this->getCompanyList();
        $this->getAdminUser();

    }


    public function defineWhereLike() {

        Builder::macro('whereLike', function ($attributes, string $searchTerm) {

            /**
             * @var Illuminate\Database\Eloquent\Builder
             */
            $builder = $this;

            $searchTerm = str_replace(
                ['\\', '%', '_'],
                ['\\\\', '\%', '\_'],
                $searchTerm);

            $builder->where(function (Builder $query) use ($attributes, $searchTerm, $builder) {

                foreach ($attributes as $attribute) {
                    $query->when(
                        str_contains($attribute, '.'),
                        function (Builder $query) use ($attribute, $searchTerm) {
                            [$relationName, $relationAttribute] = explode('.', $attribute);

                            $query->orWhereHas($relationName, function (Builder $query) use ($relationAttribute, $searchTerm) {
                                $query->where($relationAttribute, 'LIKE', "%{$searchTerm}%");
                            });
                        },
                        function (Builder $query) use ($attribute, $searchTerm) {

                            if (str_contains($attribute, 'CONCAT')) {

                                $searchTerm = preg_replace('/\s+/', '%', $searchTerm);
                                $query->orWhere($query->raw($attribute), 'LIKE', "%{$searchTerm}%");

                            } else if (str_contains($attribute, 'date') || str_contains($attribute, '_at')) {

                                $string_time = tr_strtotime($searchTerm);

                                if ($string_time) {

                                    // If no hour/time is specified clear it for better date searching
                                    $string_time = preg_replace('/( 00:00:00)$/', '', $string_time);

                                    $query->orWhere($attribute, 'LIKE', "{$string_time}%");
                                }

                            } else if ($attribute == 'status') {

                                $keys = array_keys(Arr::where(Lang::get('status'), function($value, $key) use ($searchTerm) {
                                    return mb_strpos(tr_strtolower($value), tr_strtolower($searchTerm)) !== false;
                                }));

                                $query->orWhereIn($attribute, $keys);

                            } else {
                                $query->orWhere($attribute, 'LIKE', "%{$searchTerm}%");
                            }
                        }
                    );
                }
            });

            return $this;
        });
    }

    public function composeLocale() {
        // dd(session('locale'));
        // dd(cookie('locale'));
        // app()->setLocale(session()->get('locale', 'tr'));

        View::composer('*', function ($view) {
            $view->with('locale', app()->getLocale());
        });
    }

    public function composeAdminEdits() {

        View::composer([
            'admin.camp.*',
            'admin.educator.*',
        ], function($view) {

            $view->with('categories', Category::where('lang', Lang::getLocale())->pluck('title', 'id'),);

        });

    }

    public function composeMainMenu() {

        View::composer('home.*', function($view) {

            $view->with('main_menu', $this->getMainMenu());

        });

    }

    public function getMainMenu() {

        $locale = Lang::getLocale();

        $method = 'getMainMenuFor' . Str::studly($locale);

        return $this->$method();
    }


    private function getCompanyList()
    {
        view()->composer('admin.sidebar', function ($view) {
            $companyList = DB::table('company_details')->get();
            $view->with('chooseCompanyList', $companyList);
        });
    }
    private function getAdminUser()
    {
        view()->composer('*', function ($view) {
            $view->with('admin_user', Auth::user());
        });
    }

}
