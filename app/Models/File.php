<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class File extends Model
{
    protected $fillable = [
        'user_id',
        'hash',
        'url',
        'title',
        'original_name',
        'original_path',
        'mime',
        'ip',
        'session_id'
    ];

    public function getExtensionAttribute() {
        return pathinfo($this->url, PATHINFO_EXTENSION);
    }

    protected static function booted()
    {
        static::creating(function ($file) {
            // Save a part of session ID for verification
            $file->session_id = substr(session()->getId(), 0, 8);
        });

        static::deleting(function($file) {
            if (!is_null($file->url) && !empty($file->url)) {
                \Illuminate\Support\Facades\File::delete(public_path($file->url));
                \Illuminate\Support\Facades\File::delete(public_path($file->original_path));
            }

            // ProductPicture::where('file_id', $file->id)->delete();
            // CategoryPicture::where('file_id', $file->id)->delete();
        });
    }
}
