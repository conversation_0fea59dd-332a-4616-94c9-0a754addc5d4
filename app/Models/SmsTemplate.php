<?php

namespace App\Models;

use App\Scopes\CompanyScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class SmsTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'content',
        'description',
        'type',
        'company_id'
    ];

    public static function booted()
    {
        //static::addGlobalScope(new CompanyScope);
        static::addGlobalScope('company_id_null', function (\Illuminate\Database\Eloquent\Builder $builder) {
            $builder->whereNull('company_id');
        });
    }

    public function getExcerptAttribute() {
        return Str::words($this->content, 10);
    }
}
