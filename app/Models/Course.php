<?php

namespace App\Models;

use App\Enums\CourseStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class Course extends Model
{
    use HasFactory;

    protected $fillable = [
        'auth_id',
        'category_id',
        'title',
        'description',
        'image_id',
        'slug',
        'educator_id',
        'status',
    ];

    public static function booted()
    {
        //static::addGlobalScope(new CompanyScope);
    }

    public function users()
    {
        return $this->belongsToMany(User::class, 'course_enrolments', 'course_id', 'user_id');
    }

    public function sessions()
    {
        return $this->hasMany(CourseSession::class, 'course_id')->where('status', "!=",CourseStatus::DRAFT);
    }
    public function educator()
    {
        return $this->belongsTo(Educator::class);
    }

    public function categories()
    {
        return $this->belongsToMany(Category::class, 'course_categories', 'course_id', 'category_id');
    }

    public function getImageAttribute() {
        return File::find($this->image_id);
    }

    public function getImageUrlAttribute() {
        return $this->image->url ?? null;
    }

    public function getImageAbsoluteUrlAttribute() {
        return $this->image ? url($this->image->url) : null;
    }
    
    public function course_sessions()
    {
        return $this->hasMany(\App\Models\CourseSession::class, 'course_id');
    }


    protected static function boot()
    {
        parent::boot();

        static::creating(function ($item) {
            $item->slug = Str::slug($item->title);
            $item->auth_id = Auth::user()->id;
        });

        static::saving(function ($item) {
            $item->slug = Str::slug($item->title);
            if(empty($item->auth_id)) $item->auth_id = Auth::user()->id;
        });
    }


}
