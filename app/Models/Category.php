<?php

namespace App\Models;

use App\Scopes\CompanyScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'lang',
        'background',
        'color',
        //'company_id'
    ];


    public static function booted()
    {
        //static::addGlobalScope(new CompanyScope);

        static::saving(function($item) {
            $item->slug = \Illuminate\Support\Str::of($item->title)->slug();
        });
    }

    public function courses()
    {
        return $this->belongsToMany(Course::class, 'course_categories', 'category_id', 'course_id');
    }

}
