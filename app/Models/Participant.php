<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class Participant extends Model
{
    use HasFactory;

    protected $fillable = [
        'id',
        'account_id',
        'webinar_id',
        'user_id' ,
        'entered',
        'left'
    ];

    protected static function booted()
    {
        static::addGlobalScope('company', function (Builder $builder) {
            $builder->whereHas('user', function ($query) {
                $query->where('company_id', Auth::user()->company_id);
            });
        });
    }

    public function user()
    {
        return $this->belongsTo(User::class,'user_id');
    }
}
