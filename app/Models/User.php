<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Hash;
use Lara<PERSON>\Sanctum\HasApiTokens;
use <PERSON><PERSON>\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'username',
        'email',
        'password',
        'phone',
        //'image_id',
        'image_url',
        'role',
        'status',
        'level',
        'company_id',
        'beta',
        'super_admin'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Keep a cache of defaults for same request
     *
     * @var array|null
     */
    private $defaults_cache = null;
    private $career_defaults_cache = null;
    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];


    public $useEmailAsName = true; // Varsayılan olarak true (yani name boşsa email gösterir)

    public function getNameAttribute($value)
    {
        // Şu anki route adını al
        $currentRoute = request()->route()->getName();

        // Eğer belirli bir route ise, flag'i göz ardı et ve sadece name alanını döndür
        if ($currentRoute === 'admin.user.edit') {
            return $value; // Kullanıcı düzenleme sayfasında sadece name alanı kullanılacak
        }

        // Eğer flag true ve name boşsa email döndür, değilse name döndür
        return ($this->useEmailAsName && empty($value)) ? $this->email : $value;
    }

    public function setPasswordAttribute($value) {
        if (!is_null($value) && !empty($value)) {
            $this->attributes['password'] = Hash::make($value);
        }
    }

    /*public function getImageAttribute() {
        return File::find($this->image_id);
    }

    public function getImageUrlAttribute() {
        return $this->image->url ?? null;
    }*/

    public function courses()
    {
        return $this->belongsToMany(Course::class, 'course_enrolments', 'user_id', 'course_id');
    }
    public function course_sessions()
    {
        return $this->belongsToMany(CourseSession::class, 'course_enrolments', 'user_id', 'session_id');
    }

    //returns default answers
    public function defaults() {
        return $this->hasOne(UserDefault::class);
    }

    public function educator(){
        return $this->hasOne(Educator::class,'user_id');
    }

    protected static function booted()
    {

        static::addGlobalScope('educator', function ($builder) {
            $builder->with('educator');
        });

        //static ::addGlobalScope(new CompanyScope);

    }

    public function getEducatorTypeAttribute(){
        return $this->educator->educator_type ?? null;
    }

    public function getApplicationDefaultsAttribute() {

        if (is_null($this->defaults_cache)) {
            $defaults = $this->defaults;

            $this->defaults_cache = [
                'name'            => $this->name,
                'email'           => $this->email,
                'phone'           => $this->phone,
                'gender'          => Arr::get($defaults, 'gender'),
                'dob'             => Arr::get($defaults, 'dob'),
                'idno'            => Arr::get($defaults, 'idno'),
                'allergies'       => Arr::get($defaults, 'allergies'),
                'food_preference' => Arr::get($defaults, 'food_preference'),
                'school_id'       => Arr::get($defaults, 'school_id'),
                'school_name'     => Arr::get($defaults, 'school_name'),
                'school_type'     => Arr::get($defaults, 'school_type'),
                'school_branch'   => Arr::get($defaults, 'school_branch'),
                'heard'           => Arr::get($defaults, 'heard'),
                'heard_other'     => Arr::get($defaults, 'heard_other'),
            ];
        }

        return $this->defaults_cache;
    }

    //returns career default answers
    public function def() {
        return $this->hasOne(UserCareerDefault::class);
    }

    public function getCareerDefaultsAttribute() {

        if (is_null($this->career_defaults_cache)) {
            $career_defaults = $this->def;

            $this->career_defaults_cache = [
                'linkedin'        => Arr::get($career_defaults, 'linkedin'),
                'twitter'         => Arr::get($career_defaults, 'twitter'),
                'high_school'     => Arr::get($career_defaults, 'high_school'),
                'university'      => Arr::get($career_defaults, 'university'),
                'major'           => Arr::get($career_defaults, 'major'),
                'experiences'     => Arr::get($career_defaults, 'experiences'),
                'interested_fields' => Arr::get($career_defaults, 'interested_fields'),
            ];
        }
        return $this->career_defaults_cache;
    }

    public function getInitialsAttribute() {

        $name = $this->name;
        $name = explode(' ', $name);

        $initials = '';

        if (count($name) > 1) {
            $initials = mb_substr($name[0], 0, 1) . mb_substr($name[1], 0, 1);
        } else {
            $initials = mb_substr($name[0], 0, 2);
        }

        return tr_strtoupper($initials);
    }

    public function getIsAdminAttribute() {
        return $this->role == 'admin';
    }


    public function categories() {
        return $this->belongsToMany(Category::class, 'educator_category');
    }
    public function company()
    {
        return $this->belongsTo(CompanyDetail::class, 'company_id', 'company_id');
    }
}
