<?php

namespace App\Models;

use App\Scopes\CompanyScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Lang;


class Educator extends Model
{
    use HasFactory;

    protected $fillable = [
        'id',
        'user_id',
        'title',
        'content',
        'excerpt',
        'lang',
        'slug',
        'image_id',
        'remote_url',
        'educator_type',
        'note',
        'created_at',
        'updated_at',
    ];

    public static function booted() {
        //static::addGlobalScope(new CompanyScope);
        static::saving(function($item) {
            $item->slug = \Illuminate\Support\Str::of($item->title)->slug();
        });
    }


    public function getImageAttribute() {
        return File::find($this->image_id);
    }

    public function getImageUrlAttribute() {
        return $this->image->url ?? null;
    }
    public function getImageAbsoluteUrlAttribute() {
        return $this->image ? url($this->image->url) : null;
    }

    public function categories() {
        return $this->belongsToMany(Category::class, 'educator_category');
    }

    public function getCategoriesExportAttribute() {
        $text = '';
        $categories = $this->categories();
        if($categories->count()>0) {
            foreach($categories->get() as $category){
                $text .= $category['title'] . ', ';
            }
        }
        return $text;
    }

    public function camps() {
        return $this->belongsToMany(Camp::class, 'camp_educator')->where('status', '!=', 'draft');
    }

    public function getUrlAttribute() {
        return route('home.'.  Lang::getLocale() .'.educator.show', ['slug' => $this->slug]);
    }

    public function user() {
        return $this->belongsTo(User::class);
    }
}
