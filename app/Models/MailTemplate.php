<?php

namespace App\Models;

use App\Enums\MailTemplateType;
use App\Scopes\CompanyScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class MailTemplate extends Model
{
    use HasFactory;

    public $timestamps = false;

    protected $fillable = [
        'name',
        'title',
        'subject',
        'content',
        'description',
        'fields',
        'type',
        'camp_id',
        'created_by',
        'company_id',
    ];

    public static function booted()
    {
        //static::addGlobalScope(new CompanyScope);
        static::addGlobalScope('company_id_null', function (\Illuminate\Database\Eloquent\Builder $builder) {
            $builder->whereNull('company_id');
        });
    }

    protected $casts = [
        'template' => MailTemplateType::class,
    ];

    public function getExcerptAttribute() {
        return Str::words($this->content, 10);
    }
}
