<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Scopes\CompanyScope;

class CompanyDetail extends Model
{
    protected $table = 'company_details';
    protected $primaryKey = 'company_id';
    public $incrementing = true;
    public $timestamps = false;

    protected $fillable = [
        'company_name',
        'company_slug',
        'address',
        'contact_email',
        'phone_number',
        'logo'
    ];

    public function users()
    {
        return $this->hasMany(User::class, 'company_id', 'company_id');
    }

    public function settings()
    {
        return $this->hasMany(CompanySetting::class, 'company_id', 'company_id');
    }

    public function options()
    {
        return $this->hasMany(CompanyOption::class, 'company_id', 'company_id');
    }

    public function getSettingValue($setting_key)
    {
        $setting = CompanySetting::select('setting_value')
            ->where('company_id', $this->company_id)
            ->where('setting_key', $setting_key)
            ->first();

        if ($setting) {
            return $setting->setting_value;
        }

        return null; // veya isteğinize göre başka bir değer döndürebilirsiniz
    }

    public function getOptionValue($option_key)
    {
        $option = CompanyOption::select('value')
            ->where('company_id', $this->company_id)
            ->where('option', $option_key)
            ->first();

        if ($option) {
            return $option->value;
        }

        return null; // veya isteğinize göre başka bir değer döndürebilirsiniz
    }

    public static function booted()
    {
        static::addGlobalScope(new CompanyScope);
    }
    public function company()
    {
        return $this->belongsTo(Company_details::class, 'company_id');
    }

}
