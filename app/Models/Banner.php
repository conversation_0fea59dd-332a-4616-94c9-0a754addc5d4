<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Banner extends Model
{
    use HasFactory;

    protected $fillable = [
        'banner_title',
        'banner_subtitle',
        'banner_url',
        'banner_image_id',
        'banner_status',
        'banner_order',
        'created_at',
        'updated_at',
    ];

    public function getBannerImageAttribute() {
        return File::find($this->banner_image_id);
    }

    public function getBannerImageUrlAttribute() {
        return $this->banner_image->url ?? null;
    }

    public function getBannerImageAbsoluteUrlAttribute() {
        return $this->banner_image ? url($this->banner_image->url) : null;
    }
}
