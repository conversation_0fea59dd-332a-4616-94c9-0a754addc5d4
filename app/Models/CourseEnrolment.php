<?php

namespace App\Models;

use App\Scopes\CompanyScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class CourseEnrolment extends Model
{
    use HasFactory;

    protected $fillable = [
        'course_id',
        'user_id',
        'date',
        'enrolment_hash'
    ];

    public static function booted()
    {
        //static::addGlobalScope(new CompanyScope);
    }

    public function course()
    {
        return $this->belongsTo(Course::class, 'course_id', 'id');
    }

    public function courseSession()
    {
        return $this->belongsTo(CourseSession::class, 'session_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

}
