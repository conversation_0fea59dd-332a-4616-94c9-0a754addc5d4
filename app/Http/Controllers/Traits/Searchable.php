<?php

namespace App\Http\Controllers\Traits;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Lang;

trait Searchable
{

    /**
     * Determine whether is any searchable columns are preset
     *
     * @return boolean
     */
    public function isSearchable() {
        return isset($this->searchable_fields) && count($this->searchable_fields) > 0;
    }

    /**
     * Determine whether is table sortable
     *
     * @return boolean
     */
    public function isSortable() {
        return isset($this->sorting_options) && count($this->sorting_options) > 0;
    }

    /**
     * Determine whether is table filterable
     *
     * @return boolean
     */
    public function isFilterable() {
        return isset($this->filter_options) && count($this->filter_options) > 0;
    }

    /**
     * Get row count for current table
     *
     * @return array
     */
    public function countData() {
        return [
            'total' => $this->rows_total,
            'found' => $this->rows_found
        ];
    }

    public function getSearchableFields() {

        if (isset($this->searchable_fields)) {
            return $this->searchable_fields;
        }

        $listing_headers = $this->listing_headers ?? [];

        return array_map(
            function($arr) {
                return $arr['bind'];
            },
            array_filter(
                $listing_headers,
                function($arr) {
                    return $arr['type'] == 'text';
                })
        );

    }

    /**
     * Get the resource name of controller
     * Defaults to class name by excluding "Controller" keyword
     *
     * Route::get('admin/(resource_name)/{id}/edit')...
     * route('admin.(route_prefix).edit', [(resource_name) => $item->id])
     *
     * @return string
     */
    public function getResourceName() {
        if (!$this->resource) {
            $this->resource = Str::camel(Str::replaceLast('Controller', '', class_basename($this)));
        }

        return $this->resource;
    }

    /**
     * Get the route prefix of controller
     * Defaults to resource name
     *
     * Route::get('admin/(resource_name)')->name('admin.(route_prefix).index')
     * Route::get('admin/(resource_name)/{id}/edit')->name('admin.(route_prefix).edit')
     *
     * @return string
     */
    public function getRoutePrefix() {
        if (!$this->route_prefix) {
            $this->route_prefix = $this->getResourceName();
        }

        return $this->route_prefix;
    }

    /**
     * Get the view path folder prefix of controller
     * Defaults to resource name
     *
     * admin/
     *   (view_prefix)/
     *     index.twig
     *     edit.twig
     *
     * @return string
     */
    public function getViewPrefix() {
        if (!$this->view_prefix) {
            $this->view_prefix = $this->getResourceName();
        }

        return $this->view_prefix;
    }

    /**
     * Get the base model to be used for CRUD operations
     *
     * @return Illuminate\Database\Eloquent\Model
     */
    public function getModel() {
        if (is_string($this->model)) {
            $this->model = app($this->model);
        }

        if (is_null($this->model)) {
            $model_class = 'App\\Models\\' . Str::studly(Str::replaceLast('Controller', '', class_basename($this)));
            $this->model = app($model_class);
        }

        return $this->model;
    }

    public function getFilterData() {
        return array_filter($this->opt('filter', []), function($column) {
            return array_key_exists($column, $this->filter_options);
        }, ARRAY_FILTER_USE_KEY);
    }

    public function getFilterBinds() {
        foreach ($this->filter_options as $column => $data) {

            if (!isset($this->filter_options[$column]['item_list']) && isset($this->filter_options[$column]['items'])) {
                $method = $this->filter_options[$column]['items'];
                $this->filter_options[$column]['item_list'] = $this->$method();
            }
        }

        return $this->filter_options;
    }

    public function getSearchableData() {
        return [
                'resource' => $this->getResourceName(),
                'title'    => $this->title ?? '',
                'headers'  => $this->listing_headers,
                'count'    => $this->countData(),
                'sort' => [
                    'enabled' => $this->isSortable(),
                    'current' => request('sort'),
                    'options' => $this->sorting_options,
                ],
                'search' => [
                    'enabled' => $this->isSearchable(),
                    'current' => request('q'),
                ],
                'filter' => [
                    'enabled' => $this->isFilterable(),
                    'current' => $this->getFilterData(),
                    'binds'   => $this->getFilterBinds(),
                ],
                'pagination' => $this->getPaginationData(),
                'actions'    => [
                    'enabled' => $this->canShowActions(),
                    'option' => $this->action,
                ],
                'export' => [
                    'enabled' => $this->canExport(),
                ],
                'appendables' => $this->getAppendableRows(),
                'item_tags' => $this->getItemTags(),
                'has_mobile_header' => $this->hasMobileHeader(),
            ];
    }

    public function hasMobileHeader() {
        foreach ($this->listing_headers as $header => $options) {
            if (Arr::get($options, 'mobile_only') || Arr::get($options, 'mobile')) {
                return true;
            }
        }

        return false;
    }

    public function getRequestOptions() {
        return [];
    }

    public function setRequestOptions($data = []) {

        $data = array_merge($data, $this->getRequestOptions());

        // Sorting Options
        $sorting = Arr::get($data, 'sort', '');

        if ((is_null($sorting) || empty($sorting)) && !empty($this->sorting_options)) {
            $sorting = array_keys($this->sorting_options)[0];
        }

        $sort = explode(' ', $sorting, 2);

        $this->request_options['sort'] = [
            'column' => isset($sort[0]) && !empty($sort[0]) ? $sort[0] : 'created_at',
            'order'  => isset($sort[1]) && strtolower($sort[1][0]) == 'a' ? 'ASC' : 'DESC',
        ];

        // Search Query Options
        $this->request_options['search'] = [
            'query' => Arr::get($data, 'q')
        ];

        // Filter Options
        $filter_keys = Arr::get($data, 'filter', []);
        if (is_null($filter_keys) || is_string($filter_keys)) {
            $filter_keys = [];
        }
        $filters = array_map(function($columns) {
            return explode(',', $columns);
        }, $filter_keys);
        $this->request_options['filter'] = $filters;

        // Pagination Options
        $this->request_options['pagination'] = [
            'per_page' => min(500, max(25, ((int) Arr::get($data, 'per_page', 25)))),
            'page'     => max(1, ((int) Arr::get($data, 'page', 1))),
            'disable'  => Arr::get($data, 'per_page') == 'all'
        ];
    }

    public function canShowActions() {
        if ($this->action == 'item-actions' && Route::has('admin.' . $this->getRoutePrefix() . '.destroy')) {
            return true;
        }

        return !!$this->action;
    }

    public function opt($key, $default = null) {
        return Arr::get($this->request_options, $key, $default);
    }

    public function languageQuery($query) {
        if ($this->has_language) {
            return $query->where('lang', Lang::getLocale());
        }

        return $query;
    }

    public function customQuery($query) {
        return $query;
    }

    /**
     * Modify query with requested options
     *
     * @param Builder $query
     * @return Builder
     */
    public function buildSearchableQuery($query = null) {

        if (!$query) {
            $query = $this->getModel()->query();
        }

        $query = $this->customQuery($this->languageQuery($query));

        $this->rows_total = $query->count();

        if (is_null($this->request_options)) {
            $this->setRequestOptions(request()->query());
        }

        $query = $this->filterQuery($query);

        $query = $this->searchQuery($query);

        $query = $this->sortQuery($query);

        $this->rows_found = count($query->get());

        $query = $this->paginateQuery($query);

        return $query;
        // $a = str_replace(array('?'), array('\'%s\''), $query->toSql());
        // $a = vsprintf($a, $query->getBindings());
        // dd($a);
    }

    /**
     * Search in query
     *
     * @param Builder $query
     * @return Builder
     */
    public function searchQuery($query) {

        $term = $this->opt('search.query');

        if ($this->isSearchable() && !is_null($term) && !empty($term)) {
            $query->whereLike($this->getSearchableFields(), $term);
        }

        return $query;
    }

    /**
     * Filter in query
     *
     * @param Builder $query
     * @return Builder
     */
    public function filterQuery($query) {

        $columns = $this->opt('filter', []);

        foreach ($columns as $column => $values) {
            $query->when(
                str_contains($column, '.'),
                function (Builder $query) use ($column, $values) {
                    [$relationName, $relationAttribute] = explode('.', $column);

                    $query->whereHas($relationName, function (Builder $query) use ($relationAttribute, $values) {
                        $query->whereIn($relationAttribute, $values);
                    });

                },
                function (Builder $query) use ($column, $values) {
                    $query->whereIn($column, $values);
                }
            );

        }

        return $query;
    }

    /**
     * Sort the query results
     *
     * @param Builder $query
     * @return Builder
     */
    public function sortQuery($query) {

        $relation = $this->opt('sort.relation');
        $column = $this->opt('sort.column', 'created_at');
        $order = $this->opt('sort.order', 'DESC');

        if ($this->isSortable() && !str_contains($column, '.')) {
            if($relation){
                $query->orderBy($relation.'.'.$column, $order);
            }else {
                $query->orderBy($column, $order);
            }
        }

        return $query;
    }

    /**
     * Paginate the query
     *
     * @param Builder $query
     * @return Builder
     */
    public function paginateQuery($query) {

        if ($this->opt('pagination.disable', false)) {
            return $query;
        }

        $this->per_page = $this->opt('pagination.per_page', 25);

        $this->max_page = (int) floor(ceil($this->rows_found / $this->per_page));

        $requested_page = $this->opt('pagination.page', 1);

        $this->current_page = min(max(1, $this->max_page), max(1, $requested_page));

        $page_to_load = max(0, ($this->current_page - 1));

        $offset = $page_to_load * $this->per_page;

        $query->skip($offset)->take($this->per_page);

        $this->previous_page = $this->current_page > 1 ? $this->current_page - 1 : false;
        $this->next_page = $page_to_load + 1 < $this->max_page ? $this->current_page + 1: false;

        $showing_from = (($this->current_page - 1) * $this->per_page) + 1;
        $showing_to = min($this->per_page * $this->current_page, $this->rows_found);

        $this->showing_between = [
            'from' => $showing_from,
            'to' => $showing_to
        ];

        return $query;
    }

    public function canExport() {
        return Route::has('admin.' . $this->getRoutePrefix() . '.export') && method_exists($this, 'export');
    }

    public function getPaginationData() {
        if (!isset($this->current_page)) {
            return [];
        }

        return [
            'current' => $this->current_page,
            'max' => max(1, $this->max_page),
            'min' => 1,
            'previous' => $this->previous_page,
            'next' => $this->next_page,
            'per_page' => $this->per_page,
            'showing' => $this->showing_between,
        ];
    }

    public function getAppendableRows() {
        return $this->appendable_row_columns;
    }

    public function getItemTags() {
        $tags = [];

        if ($this->title) {
            $tags['title'] = $this->title;
        }

        $tags['found'] = $this->rows_found;
        $tags['exportable'] = $this->canExport() ? 1 : 0;


        if ($search = $this->opt('search.query')) {
            $tags['query'] = $search;
        }

        if ($filters = $this->opt('filter')) {
            foreach ($filters as $column => $values) {
                $tags['filter:' . $column] = implode(',', $values);
            }
        }

        return $tags;
    }

}
