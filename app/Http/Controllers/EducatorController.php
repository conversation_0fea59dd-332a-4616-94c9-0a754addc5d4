<?php

namespace App\Http\Controllers;

use App\Exports\InstructorMultiSheetExport;
use App\Exports\InstructorsExport;
use App\Exports\UserMultiSheetExport;
use App\Http\Controllers\Traits\CanExport;
use App\Models\Applicant;
use App\Models\Camp;
use App\Models\Course;
use App\Models\User;
use App\Models\Category;
use App\Models\Educator;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use App\Models\UserLog;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Lang;
use Maatwebsite\Excel\Facades\Excel;
use Carbon\Carbon;

class EducatorController extends SearchableController {

    use CanExport;

    public $title = 'Eğitmen';

    public $has_language = true;

    public $listing_headers = [
        [
            'type'         => 'detail',
            'label'        => 'Eğitmen',
            'title_bind'   => 'title',
            'image_bind'   => 'image_url',
            'info_bind'    => [
                'url',
            ],
            'route'        => 'edit',
            'aspect_ratio' => '1:1',
            'keep'         => true,
        ],
        [
            'type' => 'timestamps',
            'label' => 'Tarih',
            'alignment' => 'right',
        ]
    ];


    public $searchable_fields = [
        'title',
        'content',
        'excerpt'
    ];

    public $sorting_options = [
        'title asc' => 'Alfabetik (A-Z)',
        'title desc' => 'Alfabetik (Z-A)',
        'created_at desc' => 'Oluşturulma (Yeni > Eski)',
        'created_at asc' => 'Oluşturulma (Eski > Yeni)',
    ];

    public $filter_options = [
        'categories.category_id' => [
            'label' => 'Uzmanlık Alanı',
            'items' => 'categories',
        ],
    ];

    public $export_options = [
        'columns' => [
            'title',
            'categories_export'
        ],
        'names' => [
            'title'                      => 'İsim Soyisim',
            'categories_export'          => 'Uzmanlık Alanı',
        ]
    ];

    public function generateExportFileName() {
        $_filter = request('filter');

        if ($_filter && Arr::get($_filter, 'categories.category_id')) {
            $category = Category::find($_filter['categories.category_id']);
            return Str::of($category->title)->slug()->substr(0, 50) . '_egitmenler_' . date('Y-m-d-H-i-s');
        }

        return null;
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'title'      => 'required|string|max:255',
            'content'    => 'sometimes',
            'image'      => 'sometimes',
            'remote_url' => 'sometimes',
            'note'       => 'sometimes',
        ]);


        $validated['image_id'] = request('image');
        $validated['lang'] = Lang::getLocale();

        $educator = Educator::create($validated);

        $educator->categories()->sync(request('categories', []));

        $this->created_log([
            'id'    => $educator->id,
            'title' => $educator->title,
        ]);

        return redirect()->route('admin.educator.edit', $educator->id)->with('success', 'Eğitmen başarıyla oluşturuldu.');
    }

    public function edit($id) {
        $item = Educator::findOrFail($id);
        $now = Carbon::now('Europe/Istanbul')->format('Y-m-d H:i:s');
    
        $courseList = Course::where('educator_id', $id)
            ->where('status', '!=', '-1')
            ->whereHas('course_sessions', function ($query) use ($now) {
                $query->whereRaw("CONCAT(date, ' ', start_time) > ?", [$now]);
            })
            ->with(['course_sessions' => function ($query) use ($now) {
                $query->whereRaw("CONCAT(date, ' ', start_time) > ?", [$now])
                      ->orderByRaw("CONCAT(date, ' ', start_time) ASC");
            }])
            ->get();
    
        return view(
            $this->getViewPath('edit'),
            $this->getViewData(),
            array_merge(
                ['courseList' => $courseList],
                ['item' => $item],
                ['meta_tags' => ['title' => $this->title]],
                $this->editData($item)
            )
        );
    }

    public function update(Request $request, $id)
    {
        $authUser = Auth::user();
        $validated = $request->validate([
            'title'      => 'required|string|max:255',
            'content'    => 'sometimes',
            'excerpt'    => 'sometimes',
            'image'      => 'sometimes',
            'remote_url' => 'sometimes',
            'note'       => 'sometimes',
        ]);

        $validated['image_id'] = request('image');

        $educator = Educator::findOrFail($id);

        $educator->update($validated);
        $educator->categories()->sync(request('categories', []));

        $this->updated_log([
            'id'    => $educator->id,
            'title' => $educator->title,
        ]);

        return redirect()->route('admin.educator.edit', $educator->id)->with('success', 'Eğitmen bilgileri başarıyla güncellendi.');
    }

    public function apiList() {
        return Educator::where('lang', Lang::getLocale())->get(['id', 'title'])->toArray();
    }

    public function categories() {
        return array_column(Category::orderBy('created_at', 'DESC')->get()->toArray(), 'title', 'id');
    }

    public function destroying($item) {
        $this->deleted_log([
            'id'    => $item->id,
            'title' => $item->title,
        ]);
    }

    public function educators(Request $request) {
        $authUser = Auth::user();
        if ($request->input('q')){
            $q = $request->input('q');
            $educators = Educator::where('title', 'LIKE', '%'.$q.'%')->get();
        } else {
            $educators = Educator::paginate(20);
        }
        $today = new \DateTime();

        foreach ($educators as $educator) {
            $createdAt = new \DateTime($educator->created_at);
            $educator->formatted_created_at = $createdAt->format('d.m.Y');

            $educator_user = User::where('id', $educator->user_id)->first();
            $educator->email = $educator_user->email ?? 'E-Posta Bulunamadı';
        }

        $userLogs = UserLog::with(['user:id,name'])
        ->where('visited_url', 'NOT LIKE', '%api/%')
        ->where('visited_url', 'NOT LIKE', '%save-chat-message%')
        ->orderBy('created_at', 'desc')
        ->paginate(100);

        return view('admin.educators.index', [
            'educators' => $educators,
            'userLogs' => $userLogs
        ]);
    }
    public function index(){
        return redirect()->route('admin.educators.index');
    }

    public function instructorsExport()
    {
        return Excel::download(new InstructorMultiSheetExport(), 'instructors-' . now()->format('Y-m-d') . '.xlsx');
    }
}
