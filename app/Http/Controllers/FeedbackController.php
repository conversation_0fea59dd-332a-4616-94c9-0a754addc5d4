<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\Feedback;
use App\Models\Camp;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Arr;
use Carbon\Carbon;
use App\Rules\TCKimlik;
use App\Models\School;
use Illuminate\Support\Facades\Auth;


class FeedbackController extends SearchableController {

    public $title = 'Feedback';

    public $listing_headers = [
        [
            'type'       => 'detail',
            'label'      => 'Kurs Adı',
            'title_bind' => 'title',
            'image_bind' => 'image_url',
            'route'      => 'show',
            'keep'       => true,
        ]
    ];

    public function index()
    {
        $items = $this->postFetchSort($this->fetchSort($this->buildSearchableQuery(Course::query())->get()));


        return view('admin.feedback.index', [
            'items' => $items,
            'controller' => $this,
            'listing' => $this->getSearchableData(),
        ]);
    }

    public function show($id)
    {
        $this->title = "Geri Bildirim Kayıtları";
        $this->listing_headers = [
            [
                'type'       => 'detail',
                'label'      => trans('Kullanıcı'),
                'title_bind' => 'enrolment.user.name',
                'aspect_ratio' => '5:3',
                'route'      => 'edit',
                'keep'         => true,
            ],
            [
                'type'       => 'detail',
                'label'      => trans('Kurs Tarihi'),
                'title_bind' => 'enrolment.courseSession.date',
                'info_bind' => [
                    'start_end_time'
                ],
                'aspect_ratio' => '5:3',
                'keep'         => true,
            ],
            [
                'type'       => 'detail',
                'label'      => trans('Tarih'),
                'title_bind' => 'created_at',
                'aspect_ratio' => '5:3',
                'keep'         => true,
                'alignment'  => 'right',
            ]
        ];


        //$query = Course::query();
        ///$query->where('date', '>', Carbon::now());

        $items = $this->postFetchSort($this->fetchSort($this->buildSearchableQuery(Feedback::query()
            ->whereHas('enrolment', function ($query) use ($id) {
            $query->where('course_enrolments.course_id',$id);
        })->when(Auth::user()->role !== 'super_admin', function ($query) {
                $query->whereHas('enrolment.user', function ($query) {
                    $query->where('users.company_id', Auth::user()->company_id);
                });
            })
        )->get()));


        return view('admin.feedback.index', [
            'items' => $items,
            'controller' => $this,
            'listing' => $this->getSearchableData(),
        ]);


    }

    public function edit($id)
    {
        $feedback = Feedback::findOrFail($id);
        if (!$feedback){
            return redirect()->route('feedback.index')->with('error', 'Geri bildirim bulunamadı.');
        }

        $feedback->content = json_decode($feedback->content, true);

        return view('admin.feedback.edit', [
            'feedback' => $feedback,
        ]);

    }

}
