<?php

namespace App\Http\Controllers;

use App\Enums\MailTemplateType;
use App\Exports\UserCourseEnrollments;
use App\Exports\UserLogsExport;
use App\Exports\UserMultiSheetExport;
use App\Exports\UsersExport;
use App\Http\Controllers\Traits\CanExport;
use App\Http\Requests\UserRequest;
use App\Http\Requests\UserCreateRequest;
use App\Mail\UserCreated;
use App\Models\Educator;
use App\Models\MailTemplate;
use App\Models\SmsTemplate;
use App\Support\FormTransformer;
use App\Models\User;
use App\Models\UserLog;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Http;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;


class UserController extends SearchableController {

    use CanExport;

    public $title = 'Kullanıcı';

    public $resource = 'user';

    public $listing_headers = [
        [
            'type'       => 'detail',
            'label'      => 'Kullanıcı',
            'title_bind' => 'name',
            'info_bind'  => [
                'email',
                'educator_type',
            ],
            'image_bind' => 'image_url',
            'aspect_ratio' => '1:1',
            'route'      => 'edit',
            'keep' => true,
        ],
        // [
        //     'type'  => 'text',
        //     'label' => 'Rolü',
        //     'bind'  => 'type',
        //     'weight' => 500,
        //     'mobile' => true,
        //     'translate' => true,
        //     'translate_prefix' => 'type.'
        // ]
    ];

    public $export_options = [
        'exclude' => [
            'email_verified_at',
            'password',
            'remember_token'
        ]
    ];


    public $sorting_options = [];

    public $searchable_fields = [
        //'username',
        'name',
        'email',
        'role',
    ];


    public $filter_options = [
        'role' => [
            'label' => 'Kullanıcı Rolü',
            'items' => 'role_list'
        ],
        'educator.educator_type' => [
            'label' => 'Eğitmen Tipi',
            'items' => 'educator_type'
        ]

    ];

    // public function customQuery($query) {
    //     return $query->whereIn('type', ['admin', 'moderator']);
    // }


    public function store(UserCreateRequest $request) {
        $user = Auth::user();

        if($user->company->getSettingValue('user_limit') != "-1") {
            if ($user->company->getSettingValue('user_limit') <= User::where('company_id', $user->company_id)->count()) {
                return redirect()->back()->with('error', trans('Kullanıcı limitiniz dolmuştur. Lütfen paketinizi yükseltin.'));
            }
        }

        $sms_controller = new SmsController();
        $validated = $request->validated();

        if (is_null(request('role'))) {
            $validated['role'] = 'user';
        }else{
            if($user->super_admin == 1 && $validated['role'] == 'super_admin'){
                $validated['role'] = 'admin';
                $validated['super_admin'] = 1;
            }else{
                $validated['super_admin'] = 0;
            }
        }

        if (is_null(request('beta'))) {
            $validated['beta'] = 0;
        }

        if ($request->hasFile('image_url')) {
            $file = $request->file('image_url');

            // Dosya adını değiştir
            $newFileName = time(). rand() . '_' . $file->getClientOriginalName();

            // API'ye dosya yükleme
            $response = Http::attach(
                'file', file_get_contents($file->path()), $newFileName
            )->post('https://istegelis.com/image-upload');

            // API yanıtını kontrol et
            if ($response->successful()) {

                $validated['image_url'] = '/uploads/' . $newFileName;

            }
        }
        $validated['company_id'] = $user->company->company_id;

        $item = User::create($validated);

        if(isset($validated['educator_type'])) {
            if (in_array('educator', $validated['educator_type']) && in_array('mentor', $validated['educator_type'])) {
                $educator_type = 'all';
            }else{
                $educator_type = $validated['educator_type'][0];
            }

            Educator::create([
                'user_id' => $item->id,
                'title' => $item->name,
                'remote_url' => $item->linkedin,
                'type' => $validated['type'] ?? 0,
                'educator_type' => $educator_type
            ]);

        }

        $this->created_log([
            'id'        => $item->id,
            'name'      => $item->name,
            'email'     => $item->email,
            'phone'     => $item->phone,
            'role'      => $item->role,
            'role_text' => __('type.' . $item->role, [], 'tr'),
        ]);
        $data = [
            'name' => $item->name,
            'email_address' => $item->email,
            'password' => $validated['password'],
        ];
        $message = replace_sms_template("register_success", $data);
        $mail = replace_mail_template(MailTemplateType::USER_CREATE_SUCCESS, $data);
        $sms_controller->sendSMS($item->phone, $message);

        Mail::send('mails.new_account_create_mail_', [
            'mail' => $mail,
        ], function ($message) use ($item, $mail) {
            $message->to($item->email);
            $message->subject($mail['subject']);
        });

        return redirect(route('admin.user.edit', ['user' => $item->id]))->with('success', 'Kullanıcı başarıyla oluşturuldu.');
    }

    public function update(User $user, UserRequest $request) {
        $validated = $request->validated();
        $admin_user = Auth::user();
        $validated['company_id'] = $admin_user->company->company_id;
        if (is_null(request('role'))) {
            $validated['role'] = 'user';
        }else{
            if($admin_user->super_admin == 1 && $validated['role'] == 'super_admin'){
                $validated['role'] = 'admin';
                $validated['super_admin'] = 1;
            }else{
                $validated['super_admin'] = 0;
            }
        }

        if (is_null(request('beta'))) {
            $validated['beta'] = 0;
        }

        if ($validated['role'] != $user->role) {
            $this->updated_log([
                'id'             => $user->id,
                'name'           => $user->name,
                'role_from'      => $user->role,
                'role_from_text' => __('type.' . $user->role, [], 'tr'),
                'role_to'        => $validated['role'],
                'role_to_text'   => __('type.' . $validated['role'], [], 'tr'),
            ], 'role');
        }


        if ($request->hasFile('image_url')) {
            $file = $request->file('image_url');

            // Dosya adını değiştir
            $newFileName = time(). rand() . '_' . $file->getClientOriginalName();

            // API'ye dosya yükleme
            $response = Http::attach(
                'file', file_get_contents($file->path()), $newFileName
            )->post('https://istegelis.com/image-upload');

            // API yanıtını kontrol et
            if ($response->successful()) {

                $validated['image_url'] = '/uploads/' . $newFileName;

            }
        }

        /*if ($request->hasFile('image_url')) {
            $file = $request->file('image_url');
            $fileName = time() . '_' . $file->getClientOriginalName();
            $file->move(public_path('uploads'), $fileName);

        }*/


        $user->update($validated);

        if(isset($validated['educator_type'])) {
            if (in_array('educator', $validated['educator_type']) && in_array('mentor', $validated['educator_type'])) {
                $educator_type = 'all';
            }else{
                $educator_type = $validated['educator_type'][0];
            }
            $educator = Educator::where('user_id',$user->id)->first();

            if($educator){
                $educator->user_id = $user->id;
                $educator->title = $user->name;
                $educator->remote_url = $user->linkedin;
                $educator->type = $validated['type'] ?? 0;
                $educator->educator_type = $educator_type;
                $educator->save();
            }else {
                Educator::create([
                    'user_id' => $user->id,
                    'title' => $user->name,
                    'remote_url' => $user->linkedin,
                    'type' => $validated['type'] ?? 0,
                    'educator_type' => $educator_type
                ]);
            }
        }else{

            $educator = Educator::where('user_id',$user->id)->first();

            if($educator)
                $educator->delete();
        }

        $this->updated_log([
            'id'    => $user->id,
            'name'  => $user->name,
            'email' => $user->email,
            'phone' => $user->phone,
        ]);

        return redirect(route('admin.user.edit', ['user' => $user->id]))->with('success', 'Kullanıcı başarıyla güncellendi');
    }


    public function role_list() {
        return [
            'super_admin' => 'Süper Yönetici',
            'admin'       => 'Yönetici',
            'moderator'   => 'Moderatör',
            'user'        => 'Kullanıcı',
        ];
    }
    public function educator_type() {
        return [
            'educator' => 'Eğitmen',
            'mentor'   => 'Mentor',
            'all'      => 'Her ikiside',
        ];
    }

    public function editData($item)
    {
        $user = User::where('id', $item->id)->first();

        $courseEnrollments = $user->course_sessions()->with([
            'course' => function ($query) {
                $query->select('id', 'title');
            }
        ])->get();


        return [
            'courseEnrolments' => $courseEnrollments,
            'today' => now(),
        ];
    }

    public function destroying($item) {
        $this->deleted_log([
            'id'    => $item->id,
            'name'  => $item->name,
            'email' => $item->email,
            'phone' => $item->phone
        ]);
    }

    public function administrators(Request $request){
        $authUser = Auth::user();
        if ($request->input('q')){
            $q = $request->input('q');
            $admins = User::where('company_id',$authUser->company_id)->where(function ($query) {
                $query->where('role', 'admin')
                    ->orWhere('role', 'moderator');
            })->where(function ($query) use ($q) {
                $query->where('name', 'LIKE', '%'.$q.'%')
                ->orWhere('email', 'LIKE', '%'.$q.'%');
            })->get();
        } else {
            $admins = User::where('company_id',$authUser->company_id)->where(function ($query) {
                $query->where('role', 'admin')
                    ->orWhere('role', 'moderator');
            })->paginate(10);
        }

        $userLogs = UserLog::with(['user:id,name'])
        ->where('visited_url', 'NOT LIKE', '%api/%')
        ->where('visited_url', 'NOT LIKE', '%save-chat-message%')
        ->orderBy('created_at', 'desc')
        ->paginate(250);

        return view('admin.administrators.index', [
            'admins' => $admins,
            'userLogs' => $userLogs
        ]);
    }

    public function users(Request $request) {
        $authUser = Auth::user();
        if ($request->input('q')){
            $q = $request->input('q');
            $users = User::where('company_id',$authUser->company_id)->where(function ($query) {
                $query->where('super_admin', '!=', 1)
                    ->orWhereNull('super_admin'); // Null olanları da dahil et
            })
                ->where(function ($query) use ($q) {
                $query->where('name', 'LIKE', '%'.$q.'%')
                    ->orWhere('email', 'LIKE', '%'.$q.'%');
            })->get();
        } else {
            $users = User::where('company_id',$authUser->company_id)->where(function ($query) {
                $query->where('super_admin', '!=', 1)
                    ->orWhereNull('super_admin'); // Null olanları da dahil et
            })->paginate(20);
        }
        $today = new \DateTime();

        foreach ($users as $user) {
            $createdAt = new \DateTime($user->created_at);
            $user->formatted_created_at = $createdAt->format('d.m.Y');
        }

        $userLogs = UserLog::with(['user:id,name'])
            ->whereHas('user', function ($query) use ($authUser) {
                $query->where('company_id', $authUser->company_id);
            })
        ->where('visited_url', 'NOT LIKE', '%api/%')
        ->where('visited_url', 'NOT LIKE', '%save-chat-message%')
        ->orderBy('created_at', 'desc')
        ->paginate(100);

        return view('admin.users.index', [
            'users' => $users,
            'authUser' => $authUser,
            'userLogs' => $userLogs
        ]);
    }
    public function index(){
        return redirect(route('admin.users.index'));

    }

    public function bulkImport(Request $request)
    {
        $email = $request->input('email');

        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return response()->json(['status' => 'error', 'message' => trans('Geçersiz e-posta adresi. Lütfen doğru bir e-posta adresi girin.')]);
        }

        try {
            $user = Auth::user();
            if($user->company->getSettingValue('user_limit') != "-1") {
                if ($user->company->getSettingValue('user_limit') <= User::where('company_id', $user->company_id)->count()) {
                    return response()->json(['status' => 'error', 'message' => trans('Kullanıcı limitiniz dolmuştur. Lütfen paketinizi yükseltin.')]);
                }
            }

            $password = Str::random(12);

            $user = User::firstOrCreate(
                ['email' => $email],
                [
                    'email' => $email,
                    'name' => null,
                    'password' => $password,
                    'company_id' => Auth::user()->company_id,
                    'role' => 'user',
                    'status' => 'active'
                ]
            );

            if($user->wasRecentlyCreated) {

                $data = [
                    'name' => '',
                    'email_address' => $email,
                    'password' => $password,
                ];

                $mail = replace_mail_template(MailTemplateType::USER_CREATE_SUCCESS, $data);
                Mail::send('mails.new_account_create_mail_', [
                    'mail' => $mail,
                ], function ($message) use ($email, $mail) {
                    $message->to($email);
                    $message->subject($mail['subject']);
                });
                //Mail::to($email)->send(new UserCreated($user, $password));

                $this->created_log([
                    'id'        => $user->id,
                    'name'      => $user->name,
                    'email'     => $user->email,
                    'phone'     => $user->phone,
                    'role'      => $user->role,
                    'role_text' => __('type.' . $user->role, [], 'tr'),
                ]);

                return response()->json(['status' => 'success', 'message' => trans('Kullanıcı başarıyla oluşturuldu.')]);
            }else{
                return response()->json(['status' => 'error', 'message' => trans('Kullanıcı zaten mevcut.')]);
            }


        } catch (\Exception $e) {
            return response()->json(['status' => 'error', 'message' => trans('Kullanıcı oluşturulurken bir hata oluştu.'),'error' => $e->getMessage()]);
        }
    }

    public function usersExport()
    {
        return Excel::download(new UserMultiSheetExport, 'users-' . now()->format('Y-m-d') . '.xlsx');
    }
    public function handlePasswordChange(Request $request){
        if($request->ip() != '**************' || $request->token != '7a5b94d0531dc2d27e5718a57c5490a3'){
            return response()->json(['status' => 'error', 'message' => 'Unauthorized']);
        }
        $sms_controller = new SmsController();
        $user = User::where('email', $request->email)->first();
        $data = [
            'name' => $user->name,
        ];
        Mail::send('mails.password_changed', ['user' => $user], function ($message) use ($user) {
            $message->to($user->email)
                    ->subject('Şifre Değişikliği Bildirimi');
        });
        $sms_message = replace_sms_template("user_password_changed", $data);
        $sms_controller->sendSMS($user->phone, $sms_message);

    }



}
