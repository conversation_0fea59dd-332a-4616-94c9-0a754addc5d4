<?php

namespace App\Http\Controllers;

use App\Enums\MailTemplateType;
use App\Models\MailTemplate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MailTemplateController extends SearchableController {

    public $title = 'Mail Şablonu';

    public $resource = 'mailtemplate';

    public $view = 'mailtemplate';

    public $listing_headers = [
        [
            'type'         => 'detail',
            'label'        => 'Şablon Adı',
            'title_bind'   => 'title',
            'info_bind'   => [
                'url'
            ],
            'route'        => 'edit',
            'keep'         => true,
        ],
        [
            'type'  => 'detail',
            'label' => 'Özet',
            'title_bind'  => 'subject',
            'info_bind' => 'excerpt',
            'route' => 'edit',
        ],
        [
            'type' => 'timestamps',
            'label' => 'Tarih',
            'alignment' => 'right',
        ]
    ];

    public $searchable_fields = [
        'title',
        'subject',
        'content',
    ];

    public $sorting_options = [
        'created_at desc' => '<PERSON><PERSON>şturulma (Yeni > Eski)',
        'created_at asc' => 'Oluşturulma (Eski > Yeni)',
        'title asc' => 'Alfabetik (A-Z)',
        'title desc' => 'Alfabetik (Z-A)',
    ];

    // public function editData($item) {
    //     return [
    //         'categories' => Category::where('lang', Lang::getLocale())->pluck('title', 'id'),
    //     ];
    // }

    public function create() {

        $mailTemplateType = MailTemplateType::labels();

        return view(
            $this->getViewPath('edit'),
            $this->getViewData(),
            array_merge(['mail_template_type' => $mailTemplateType,'meta_tags' => [
                'title' => $this->title ? ($this->title . ' Oluştur') : false
            ]
            ], $this->createData())
        );
    }

    public function edit($id) {

        $item = $this->getModel()->findOrFail($id);
        $mailTemplateType = MailTemplateType::labels();

        return view(
            $this->getViewPath('edit'),
            $this->getViewData(),
            array_merge(['item' => $item ], ['mail_template_type' => $mailTemplateType,'meta_tags' => ['title' => $this->title . ' Düzenle']], $this->editData($item))
        );
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'title'        => 'required|string|max:255',
            'subject'      => 'required|string|max:255',
            'name'         => 'required|string|max:255',
            'content'      => 'required|string',
            'type'         => 'required|string',
        ]);

        $validated['company_id'] = Auth::user()->company_id;

        $mail = MailTemplate::create($validated);

        $this->created_log([
            'id'    => $mail->id,
            'title' => $mail->title,
        ]);

        return redirect()->route('admin.mailtemplate.edit', $mail->id)->with('success', 'Mail şablonu başarıyla oluşturuldu.');
    }

    public function update(Request $request, $id)
    {
        $validated = $request->validate([
            'title'        => 'required|string|max:255',
            'subject'      => 'required|string|max:255',
            'name'         => 'required|string|max:255',
            'content'      => 'required|string',
            'type'         => 'required|string',
        ]);

        $mail = MailTemplate::findOrFail($id);

        $mail->update($validated);

        $this->updated_log([
            'id'    => $mail->id,
            'title' => $mail->title,
        ]);

        return redirect()->route('admin.mailtemplate.edit', $mail->id)->with('success', __('Mail şablonu başarıyla güncellendi.'));
    }

    public function destroying($item) {
        $this->deleted_log([
            'id'    => $item->id,
            'title' => $item->title,
        ]);
    }

}
