<?php

namespace App\Http\Controllers;

use App\Enums\CourseStatus;
use App\Mail\CourseRegistrationMail;
use App\Mail\UserCreated;
use App\Models\ApplicationAnswer;
use App\Models\Camp;
use App\Models\Category;
use App\Models\CompanySetting;
use App\Models\Course;
use App\Models\CourseEnrolment;
use App\Models\CourseSession;
use App\Models\Educator;
use App\Models\UserCamp;
use App\Models\User;
use App\Models\Location;
use App\Models\School;
use App\Rules\TCKimlik;
use Carbon\Carbon;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class CourseController extends SearchableController
{

    public $title = 'Kurslar';

    public $has_language = false;

    public $listing_headers = [
        [
            'type'       => 'detail',
            'label'      => 'Kurs Adı',
            'title_bind' => 'title',
            'image_bind' => 'image_url',
            'aspect_ratio' => '5:3',
            'route' => 'edit',
            'keep'       => true,
        ]

    ];

    public $searchable_fields = [
        'title',
    ];

    public $sorting_options = [
        'created_at desc' => 'Oluşturulma (Yeni > Eski)',
        'created_at asc' => 'Oluşturulma (Eski > Yeni)',
        'title asc' => 'Alfabetik (A-Z)',
        'title desc' => 'Alfabetik (Z-A)',
    ];


    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return RedirectResponse
     */


    public function index()
    {
        $response = $this->checkSuperAdmin();
        if ($response) {
            return $response;
        }
        $items = $this->postFetchSort($this->fetchSort($this->buildSearchableQuery(Course::query()->where('status', CourseStatus::PUBLISHED))->get()));

        return view('admin.course.index', [
            'items' => $items,
            'controller' => $this,
            'listing' => $this->getSearchableData(),
        ]);


    }
    public function show()
    {
        $this->title = "Kurs Kayıtları";
        $this->listing_headers = [
            [
                'type'       => 'detail',
                'label'      => 'Kurs Adı',
                'title_bind' => 'course.title',
                'image_bind'   => 'image_url',
                'aspect_ratio' => '5:3',
                'route' => 'course.enrolment.detail',
                'keep'         => true,
            ],
            [
                'type'       => 'text',
                'label'      => 'Kurs Açıklaması',
                'bind'      => 'course.description',
                'color'     => 'grey',
                'keep'       => true,
            ],
            [
                'type'       => 'detail',
                'label'      => 'Tarih',
                'title_bind' => 'date',
                'info_bind'  => [
                 'time_range'
                ],
                'route'      => 'course.enrolment.detail',
                'alignment'  => 'right',
                'keep'       => true,
            ]
        ];

        $this->sorting_options = [
        'date desc' => 'Kurs Tarihi (Yeni > Eski)',
        'date asc' => 'Kurs Tarihi (Eski > Yeni)',
        'course.title asc' => 'Alfabetik (A-Z)',
        'course.title desc' => 'Alfabetik (Z-A)',
    ];

    $this->searchable_fields = [
        'course.title',
    ];

    $expired_date = request()->input('expired_date');

        //$query = Course::query();
        ///$query->where('date', '>', Carbon::now());

        $items = $this->postFetchSort($this->fetchSort($this->buildSearchableQuery(CourseSession::where(DB::raw("CONCAT(date, ' ', start_time)"), $expired_date == 1 ? '<' :'>', now()->format('Y-m-d H:i:s'))->where('status',CourseStatus::PUBLISHED)->with(['course' => function($query) {
            $query->select('id', 'title','description'); // Sadece 'id' ve 'title' alanlarını seç
        }]))->get()));


        return view('admin.course.index', [
            'items' => $items,
            'controller' => $this,
            'listing' => $this->getSearchableData(),
        ]);


    }

    public function create()
    {
        $response = $this->checkSuperAdmin();
        if ($response) {
            return $response;
        }
        $categories = Category::all();
        $educatorList = Educator::pluck('title', 'id')->toArray();
        return view('admin.course.edit', [
            'categories' => $categories,
            'controller' => $this,
            'educatorList' => $educatorList,
            'listing' => $this->getSearchableData(),
        ]);

    }
    public function edit($id)
    {
        $response = $this->checkSuperAdmin();
        if ($response) {
            return $response;
        }
        $user = Auth::user();
        $item = Course::find($id);
        $categories = Category::all();
        $educatorList = Educator::pluck('title', 'id')->toArray();
        return view('admin.course.edit', [
            'categories' => $categories,
            'user' => $user,
            'item' => $item,
            'educatorList' => $educatorList,
            'controller' => $this,
            'listing' => $this->getSearchableData(),
        ]);
    }

    public function store(Request $request)
    {
        $response = $this->checkSuperAdmin();
        if ($response) {
            return $response;
        }
        $validated = $request->validate([
            'title'        => 'required|string|max:255',
            'status'       => 'required|integer',
            'description'  => 'sometimes',
            'image'        => 'sometimes',
            'categories'   => 'sometimes|array|min:1',
            'course_sessions' => 'required|array',
            'course_sessions.*.id' => 'nullable|exists:course_sessions,id',
            'course_sessions.*.zoom_link' => 'required|string|max:255',
            'course_sessions.*.quota' => 'required|integer|min:1',
            'course_sessions.*.date' => 'required|date',
            'course_sessions.*.start_time' => 'required|date_format:H:i',
            'course_sessions.*.end_time' => 'required|date_format:H:i',
            'course_sessions.*.status' => 'required|integer',
            //'date'         => 'nullable|date',
            //'link'         => 'required|string|max:255',
            //'quota'        => 'required|integer',
            'educator_id'  => 'nullable',
        ]);


        $validated['image_id']  = request('image');
        //$validated['status'] = CourseStatus::PUBLISHED;
        $course = Course::create($validated);

        $this->courseSessionCreate($validated['course_sessions'], $course->id);
        $course->categories()->sync(request('categories', []));
        $this->created_log([
            'id'    => $course->id,
            'title' => $course->title,
        ]);


        return redirect()->route('admin.course.edit', $course->id)->with('success', __('Kurs başarıyla oluşturuldu.'));
    }


    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param  int  $id
     * @return Response
     */
    public function update(Request $request, $id)
    {
        $response = $this->checkSuperAdmin();
        if ($response) {
            return $response;
        }


        $validated = $request->validate([
            'title'        => 'required|string|max:255',
            'status'       => 'required|integer',
            'description'  => 'sometimes',
            'image'        => 'sometimes',
            'categories'   => 'sometimes|array|min:1',
            'course_sessions' => 'required|array',
            'course_sessions.*.id' => 'nullable|exists:course_sessions,id',
            'course_sessions.*.zoom_link' => 'required|string|max:255',
            'course_sessions.*.quota' => 'required|integer|min:1',
            'course_sessions.*.date' => 'required|date',
            'course_sessions.*.start_time' => 'required|date_format:H:i',
            'course_sessions.*.end_time' => 'required|date_format:H:i',
            'course_sessions.*.status' => 'required|integer',
            //'date'         => 'nullable|date',
            //'link'         => 'required|string|max:255',
            //'quota'        => 'required|integer',
            'educator_id'  => 'nullable',
        ]);


        $validated['image_id']  = request('image');


        $course = Course::findOrFail($id);


        $course->update($validated);

        $this->courseSessionUpdate($validated['course_sessions'], $id);
        $course->categories()->sync(request('categories', []));
        $this->updated_log([
            'id'    => $course->id,
            'title' => $course->title,
        ]);

        // remove all usercamps with camp id of camp->idate

        return redirect()->route('admin.course.edit', $course->id)->with('success', __('Kurs başarıyla güncellendi.'));
    }


    public function destroy($id) {
        $item = $this->getModel()->findOrFail($id);

        $this->destroying($item);

        $title = $item->title ?? $this->title;

        $item->status = CourseStatus::DRAFT;
        $item->save();

        $this->destroyed();

        return redirect(route($this->getRoutePath('index')))->with('success', $title . ' başarıyla silindi!');
    }


    private function courseSessionCreate($course_sessions, $course_id)
    {

        foreach ($course_sessions as $sessionData) {

            CourseSession::create([
                'course_id' => $course_id,
                'zoom_link' => $sessionData['zoom_link'],
                'quota' => $sessionData['quota'],
                'date' => $sessionData['date'],
                'start_time' => $sessionData['start_time'].':00',
                'end_time' => $sessionData['end_time'].':00',
                'status' => $sessionData['status']
            ]);

        }

    }

    private function courseSessionUpdate($course_sessions, $course_id)
    {

        $sessionIdsFromRequest = collect($course_sessions)->pluck('id')->filter()->all();

        $currentSessionIds = CourseSession::where('course_id', $course_id)->pluck('id')->all();

        $sessionsToDelete = array_diff($currentSessionIds, $sessionIdsFromRequest);

        $draftSessions =  CourseSession::whereIn('id', $sessionsToDelete)->get();

        foreach ($draftSessions as $draftSession) {
            $draftSession->status = CourseStatus::DRAFT;
            $draftSession->save();
        }


        foreach ($course_sessions as $sessionData) {

            $date = $sessionData['date'];
            $formattedDate = \DateTime::createFromFormat('d-m-Y', $date)->format('Y-m-d');
            $sessionData['date'] = $formattedDate;

            if (isset($sessionData['id'])) {
                $session = CourseSession::find($sessionData['id']);
                $session->update([
                    'zoom_link' => $sessionData['zoom_link'],
                    'quota' => $sessionData['quota'],
                    'date' => $sessionData['date'],
                    'start_time' => $sessionData['start_time'].':00',
                    'end_time' => $sessionData['end_time'].':00',
                    'status' => $sessionData['status']
                ]);
            } else {
                CourseSession::create([
                    'course_id' => $course_id,
                    'zoom_link' => $sessionData['zoom_link'],
                    'quota' => $sessionData['quota'],
                    'date' => $sessionData['date'],
                    'start_time' => $sessionData['start_time'].':00',
                    'end_time' => $sessionData['end_time'].':00',
                    'status' => $sessionData['status']
                ]);
            }
        }


    }

    public function enrolmentDetail($id,$session_id)
    {

        $course_session = CourseSession::find($session_id);
        if(!$course_session) {
            return redirect()->route('admin.course.enrolment')->with('error', __('Kurs bulunamadı.'));
        }

        $course_date = Carbon::parse($course_session->date.' '.$course_session->start_time);

        /*if ($course_date < Carbon::now()) {
            return redirect()->route('admin.course.enrolment')->with('error', __('Kurs tarihi geçmiş.'));
        }*/
        $course = Course::find($id);
        $enrolledUserList = $course_session->users()->select('users.id', 'users.name', 'users.email')->where('company_id',Auth::user()->company_id)->get()->toArray();
        $enrolledEmails = array_column($enrolledUserList, 'email');
        //$emailsForTextarea = implode("\n", $enrolledEmails);
        //$selectedEmails = json_encode($enrolledEmails);

        $userList = User::where('company_id',Auth::user()->company_id)->get();
        return view('admin.course.enrolment', [
            'item' => $course,
            'enrolledUserList' => $enrolledUserList,
            'enrolledEmails' => $enrolledEmails,
            'userList' => $userList,
            'course_session' => $course_session,
            //'selectedEmails' => $selectedEmails,
        ]);


    }
    public function enrolmentSave($id,$session_id,Request $request)
    {

        $course_session = CourseSession::find($session_id);
        if (!$course_session) {
            return redirect()->route('admin.course.enrolment')->with('error', __('Kurs bulunamadı.'));
        }

        $emailAddresses = explode(PHP_EOL, $request->input('email_list'));
        $emailAddresses = array_filter(array_map('trim', $emailAddresses));

        foreach ($emailAddresses as $email) {

            $user = User::where('email', $email)->first();
            if ($user) {
                continue;
            }
            $this->userBulkImport($email);
        }

        $existingUsers = User::whereIn('email', $emailAddresses)->pluck('email')->toArray();

        $packageTypeLimit = Auth::user()->company->getSettingValue('package_type'); // Paket limitini alıyoruz
        // Kursun ait olduğu ay
        $courseDate = Carbon::parse($course_session->date.' '.$course_session->start_time);

        $notEnrolment = [];
        foreach ($existingUsers as $email) {
            $user = User::where('email', $email)->where('company_id', Auth::user()->company_id)->first();

            if ($user) {
                // Kullanıcının o ay için kayıtlı olduğu kurs sayısını al
                $enrolledCoursesCount = $user->course_sessions()
                    ->whereMonth('course_sessions.date', $courseDate->month)
                    ->whereYear('course_sessions.date', $courseDate->year)
                    ->count();

                // Kullanıcının kayıtlı olduğu kurs sayısını kontrol et
                if ($enrolledCoursesCount >= $packageTypeLimit && ($user->super_admin != 1 && $user->role != 'admin')) {
                    $notEnrolment[] = ['id' => $user->id,'name' => $user->name,'email' => $user->email];
                    // Paket limitini aşan kullanıcıları atla
                    continue;
                }

                // Eğer kullanıcı zaten bu kursa kayıtlı değilse, ekle
                if (!$course_session->users()->where('user_id', $user->id)->exists()) {
                    $course_session->users()->attach($user->id, [
                        'date' => now(),
                        'course_id' => $course_session->course_id,
                        'enrolment_hash' => Str::random(12),
                    ]);

                    Mail::to($user->email)->send(new CourseRegistrationMail($user->name, $course_session->course->title, $course_session->date, $course_session->start_time, "https://istegelis.com/zoom/".$course_session->course->slug));
                }
            }
        }

        if($request->input('isAjax') == "true"){
            return response()->json(['status' => 'success', 'message' => 'Kullanıcılar başarı ile kayıt edildi.']);
        }else {

            if (count($notEnrolment) > 0) {
                return redirect()->route('admin.course.enrolment.detail', ['id' => $course_session->course_id,'session_id' => $course_session->id])
                    ->with(['status' => 'error', 'message' => __('Kullanıcılar kayıt edilemedi.'), 'notEnrolment' => $notEnrolment]);
            } else {
                return redirect()->route('admin.course.enrolment.detail', ['id' => $course_session->course_id,'session_id' => $course_session->id])
                    ->with('success', __('Kullanıcılar başarı ile kayıt edildi.'));
            }
        }

/*
        $course = Course::find($id);
        if(!$course) {
            return redirect()->route('admin.course.enrolment')->with('error', __('Kurs bulunamadı.'));
        }

        $emailAddresses = explode(PHP_EOL, $request->input('email_list'));

        $emailAddresses = array_filter(array_map('trim', $emailAddresses));

        $existingUsers = User::whereIn('email', $emailAddresses)->pluck('email')->toArray();

        /*$enrolledUsers = $course->users()->pluck('email')->toArray();

        $emailsToDelete = array_diff($enrolledUsers, $emailAddresses);

        if (!empty($emailsToDelete)) {
            $usersToDelete = User::whereIn('email', $emailsToDelete)->pluck('id')->toArray();

            $course->users()->detach($usersToDelete);
        }*


        foreach ($existingUsers as $email) {
            $user = User::where('email', $email)->where('company_id',Auth::user()->company_id)->first();

            if ($user && !$course->users()->where('user_id', $user->id)->exists()) {
                $course->users()->attach($user->id, ['date' => now()]);
            }
        }
        //$nonExistingEmails = array_diff($emailAddresses, $existingUsers);


        return redirect()->route('admin.course.enrolment.detail',$course->id)->with('success', __('Kullanıcılar başarı ile kayıt edildi.'));
*/

    }

    public function userEnrolledAjax($user_id,$course_id,$session_id)
    {

        $course_session = CourseSession::find($session_id);
        if (!$course_session) {
            return response()->json( __('Kurs bulunamadı.'), 404);
        }

        $courseDate = Carbon::parse($course_session->date.' '.$course_session->start_time);

        $user = User::where('id', $user_id)->where('company_id', Auth::user()->company_id)->first();

        if ($user) {
            // Kullanıcının o ay için kayıtlı olduğu kurs sayısını al
            $enrolledCourses = $user->course_sessions()->with('course:id,title')->select('course_sessions.id', 'course_sessions.date', 'course_sessions.start_time', 'course_sessions.end_time', 'course_sessions.course_id')
                ->whereMonth('course_sessions.date', $courseDate->month)
                ->whereYear('course_sessions.date', $courseDate->year)
                ->get()
                ->map(function ($session) {
                    return [
                        'session_id' => $session->id,
                        'course_id' => $session->course->id,
                        'course_title' => $session->course->title,
                        'session_date' => $session->date,
                        'start_time' => $session->start_time,
                        'end_time' => $session->end_time,
                    ];
                });
        }

        return response()->json($enrolledCourses);

    }

    public function enrolmentUserDelete($course_id,$session_id,$user_id)
    {
        $course_session = CourseSession::find($session_id);
        if(!$course_session) {
            return ['status' => false, 'message' =>  __('Kurs bulunamadı.')];
        }

        $course_date = Carbon::parse($course_session->date.' '.$course_session->start_time);

        if ($course_date < Carbon::now()) {
            return ['status' => false, 'message' =>  __('Kurs tarihi geçmiş.')];
        }

        $enrolment = CourseEnrolment::where('course_id',$course_id)->where('session_id',$session_id)->where('user_id',$user_id)->first();
        if($enrolment) {
            $enrolment->delete();
            return ['status' => true, 'message' => __('Kullanıcı kurs kaydı başarı ile silindi.')];
        }else{
            return ['status' => false, 'message' => __('Kullanıcı bulunamadı.')];
        }

    }

    private function userBulkImport($email)
    {
        try {
            $user = Auth::user();
            if($user->company->getSettingValue('user_limit') != "-1") {
                if ($user->company->getSettingValue('user_limit') <= User::where('company_id', $user->company_id)->count()) {
                    return response()->json(['status' => 'error', 'message' => trans('Kullanıcı limitiniz dolmuştur. Lütfen paketinizi yükseltin.')]);
                }
            }

            $password = Str::random(12);

            $user = User::firstOrCreate(
                ['email' => $email],
                [
                    'email' => $email,
                    'name' => null,
                    'password' => $password,
                    'company_id' => Auth::user()->company_id,
                    'role' => 'user',
                    'status' => 'active'
                ]
            );

            if($user->wasRecentlyCreated) {

                Mail::to($email)->send(new UserCreated($user, $password));

                $this->created_log([
                    'id'        => $user->id,
                    'name'      => $user->name,
                    'email'     => $user->email,
                    'phone'     => $user->phone,
                    'role'      => $user->role,
                    'role_text' => __('type.' . $user->role, [], 'tr'),
                ]);

                return response()->json(['status' => 'success', 'message' => trans('Kullanıcı başarıyla oluşturuldu.')]);
            }else{
                return response()->json(['status' => 'error', 'message' => trans('Kullanıcı zaten mevcut.')]);
            }


        } catch (\Exception $e) {
            return response()->json(['status' => 'error', 'message' => trans('Kullanıcı oluşturulurken bir hata oluştu.'),'error' => $e->getMessage()]);
        }
    }

    public function checkSuperAdmin()
    {
        if (Auth::check() && Auth::user()->super_admin !== 1) {
            return redirect()->route('admin.dashboard.index')->with('error', __('Yetkisiz erişim.'));
        }
    }

    public function copy($id)
    {
        $response = $this->checkSuperAdmin();
        if ($response) {
            return $response;
        }
        
        $originalCourse = Course::findOrFail($id);
        
        // Kursun kopyasını oluştur
        $newCourse = $originalCourse->replicate();
        $newCourse->title = $originalCourse->title . ' (Kopya)';
        $newCourse->save();
        
        // Kategorileri kopyala
        $newCourse->categories()->sync($originalCourse->categories()->pluck('categories.id')->toArray());
        
        // Kurs oturumlarını kopyala
        foreach ($originalCourse->sessions as $session) {
            $newSession = $session->replicate();
            $newSession->course_id = $newCourse->id;
            $newSession->save();
        }
        
        $this->created_log([
            'id'    => $newCourse->id,
            'title' => $newCourse->title,
        ]);
        
        return redirect()->route('admin.course.edit', $newCourse->id)->with('success', __('Kurs başarıyla kopyalandı.'));
    }

}
