<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Traits\Searchable;
use App\Models\ActivityLog;
use Illuminate\Support\Arr;

/**
 * @var array $export_options
 */
class SearchableController extends Controller {

    use Searchable;

    /**
     * Resource keyword used in routes
     *
     * @var string
     */
    public $resource;

    /**
     * Named route keyword
     *
     * @var string
     */
    public $route_prefix;

    /**
     * View files' parent folder name
     *
     * @var string
     */
    public $view_prefix;

    /**
     * Custom name to be displayed on listing and messages
     *
     * @var string
     */
    public $title = '';

    /**
     * Columns to append on table rows
     *
     * @var array
     */
    public $appendable_row_columns = [
        //
    ];

    /**
     * Determine whether a language where query should be included
     *
     * @var boolean
     */
    public $has_language = false;

    /**
     * Item actions to be shown in edit page
     *
     * @var string
     */
    public $action = 'item-actions';

    /**
     * Model name or instance to be used in CRUD operations
     *
     * @var string|Illuminate\Database\Eloquent\Model
     */
    public $model;

    /**
     * List of columns to display on table
     *
     * [
     *   'type' => 'text',
     *   'bind' => 'title',
     *   'label' => 'Başlık'
     * ]
     *
     * @var null|array
     */
    public $listing_headers = [
        //
    ];

    /**
     * List of backgrounds of status values
     * 'key' => 'color'
     *
     * @var array
     */
    public $status_colors = [
        //
    ];

    public $filter_options = [
        //
    ];

    /**
     * List of sorting options
     *
     * @var null|array
     */
    public $sorting_options = [
        'created_at desc' => 'Oluşturulma (Yeni > Eski)',
        'created_at asc' => 'Oluşturulma (Eski > Yeni)',
    ];

    /**
     * List of column names to search
     *
     * @var null|array
     */
    public $searchable_fields = [
        //
    ];

    /**
     * Total row count in table
     *
     * @var null|int
     */
    protected $rows_total;

    /**
     * Found row count by query if applicable
     *
     * @var null|int
     */
    protected $rows_found;

    /**
     * Exportable options
     *
     * @var array
     */
    protected $export_options = [
        // These columns will be included in exported file if not in exclude list
        // If left empty, it will include all columns of given model's table
        // while still applying exclude rules
        'columns' => [
            //
        ],
        // These columns will be excluded
        'exclude' => [
            // 'created_at',
            // 'updated_at'
        ],
        // Custom Names for exported columns
        'names' => [
            // 'id' => '#',
        ]
    ];

    /**
     * Incoming Request Options to sort / filter the query
     *
     * @var null|array
     */
    public $request_options;

    public function getStatusColor($status, $use_defaults = true) {

        $defaults = [
            'success'   => 'green',
            'error'     => 'red',
            'failed'    => 'red',
            'warning'   => 'orange',
            'waiting'   => 'purple',
            'on_hold'   => 'purple',
            'draft'     => 'orange',
            'published' => 'green'
        ];

        $keys = $this->status_colors;

        if ($use_defaults) {
            $keys = array_merge($defaults, $keys);
        }

        if (isset($keys[$status])) {
            return $keys[$status];
        }


        return 'blue';

    }

    public function getStatusLabel($status) {
        return __('status.' . $status);
    }


    public function getViewData() {
        $meta_tags = null;

        if ($this->title) {
            $meta_tags = [
                'title' => $this->title . ' - Yönetim Paneli'
            ];
        }

        return [
            'controller' => $this,
            'listing' => $this->getSearchableData(),
            // 'meta_tags' => $meta_tags,
        ];
    }

    public function getViewPath($suffix) {
        return 'admin/' . $this->getViewPrefix() . '/' . $suffix;
    }

    public function getRoutePath($suffix) {
        return 'admin.' . $this->getRoutePrefix() . '.' . $suffix;
    }

    public function getRouteLink($header, $item = null) {

        $routeName = Arr::get($header, 'route');
        $routePath = 'admin.' . $this->getRoutePrefix() . '.' . $routeName;

        $attributes = [];

        switch ($routeName) {
            case 'edit':
            case 'show':
                $attributes[$this->getResourceName()] = $item->id;
                return route($routePath, $attributes);
            case 'saved-courses.edit':
                $routePath = 'admin.' . $routeName;
                $attributes['id'] = $item->uuid;
                return route($routePath, $attributes);
            case 'saved-courses.statistic':
                $routePath = 'admin.' . $routeName;
                $attributes['id'] = $item->id;
                return route($routePath, $attributes);
            case 'company.edit':
                $routePath = 'admin.' . $routeName;
                $attributes['company_id'] = $item->company_id;
                return route($routePath, $attributes);
            case 'course.enrolment.detail':
                $routePath = 'admin.' . $routeName;
                $attributes['id'] = $item->course_id;
                $attributes['session_id'] = $item->id;
                return route($routePath, $attributes);
            break;
        }

        $absoluteLink = Arr::get($header, 'link');

        if ($absoluteLink) {
            return $absoluteLink;
        }

        return '#';
    }

    /**
     * Set the export initiating controller for export options
     *
     * @param null|$this $controller
     * @return $this
     */
    public function setExportController($controller = null) {

        if (is_string($controller)) {
            $controller = app($controller);
        }

        $this->export_options['controller'] = $controller;

        return $this;
    }


    public function indexData() {
        return [];
    }

    public function index() {

        // buildSearchableQuery should be called before getViewData to get precise row count
        $merge_data = array_merge(
            ['items' => $this->postFetchSort($this->fetchSort($this->buildSearchableQuery()->get()))],
            ['meta_tags' => [
                'title' => $this->title ? ($this->title) : false
                ]
            ],
            $this->indexData()
        );

        return view(
            $this->getViewPath('index'),
            $this->getViewData(),
            $merge_data
        );
    }

    public function fetchSort($collection) {

        $column = $this->opt('sort.column', 'created_at');
        $order = $this->opt('sort.order', 'DESC');

        if ($this->isSortable() && str_contains($column, '.')) {

            $column = trim($column, " \t\n\r\0\x0B.");

            if ($order == 'DESC') {
                $collection = $collection->sortByDesc($column);
            } else {
                $collection = $collection->sortBy($column);
            }

        }

        return $collection;
    }

    public function postFetchSort($collection) {
        return $collection;
    }

    public function createData() {
        return [];
    }

    public function create() {
        return view(
            $this->getViewPath('edit'),
            $this->getViewData(),
            array_merge(['meta_tags' => [
                'title' => $this->title ? ($this->title) : false
                ]
            ], $this->createData())
        );
    }

    public function editData($item) {
        return [];
    }

    public function edit($id) {
        $item = $this->getModel()->findOrFail($id);

        return view(
            $this->getViewPath('edit'),
            $this->getViewData(),
            array_merge(['item' => $item ], ['meta_tags' => ['title' => $this->title]], $this->editData($item))
        );
    }

    public function destroying($item) {
        //
    }

    public function destroyed() {
        //
    }

    public function destroy($id) {
        $item = $this->getModel()->findOrFail($id);

        $this->destroying($item);

        $title = $item->title ?? $this->title;

        $item->delete();

        $this->destroyed();

        return redirect(route($this->getRoutePath('index')))->with('success', $title . ' başarıyla silindi!');
    }


    public function log($data = [], $subtype = null, $user = null, $resource = null, $activity = null) {

        $type = [];

        if (is_null($resource)) {
            $resource = $this->getResourceName();
        }

        $type[] = $resource;

        if (!is_null($subtype)) {
            $type[] = $subtype;
        }

        if (!is_null($activity)) {
            $type[] = $activity;
        }

        $type = implode('.', $type);

        $_data = [];

        $user_id = null;

        if (is_null($user)) {
            $user = request()->user();
        }

        if (!is_null($user)) {
            $user_id = $user->id;
        }

        $_data['ip'] = request()->ip();

        $data = array_merge($_data, $data);

        ActivityLog::create([
            'user_id' => $user_id,
            'type'    => $type,
            'data'    => $data
        ]);
    }

    public function created_log($data = [], $subtype = null, $user = null, $resource = null) {
        $this->log($data, $subtype, $user, $resource, 'created');
    }

    public function read_log($data = [], $subtype = null, $user = null, $resource = null) {
        $this->log($data, $subtype, $user, $resource, 'read');
    }

    public function updated_log($data = [], $subtype = null, $user = null, $resource = null) {
        $this->log($data, $subtype, $user, $resource, 'updated');
    }

    public function deleted_log($data = [], $subtype = null, $user = null, $resource = null) {
        $this->log($data, $subtype, $user, $resource, 'deleted');
    }

    public function generateExportFileName() {
        return null;
    }

}
