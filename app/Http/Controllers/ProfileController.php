<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;

class ProfileController extends SearchableController {

    public $title = 'Profil';

    public $resource = 'profile';

    public function index() {
        return view('admin/profile/edit', [
            'item' => Auth::user(),
            'listing' => [
                'title' => $this->title,
                'resource' => $this->resource
            ],
            'meta_tags' => [
                'title' => 'Profilimi Düzenle'
            ]
        ]);
    }

    public function update(Request $request) {
        /**
         * @var Illuminate\Database\Eloquent\Model
         */
        $user = Auth::user();

        $validated = $request->validate([
            'name'     => 'required',
            'email'    => 'required|email|unique:users,email,' . $user->id, 
            'phone'    => 'required',
            'password' => 'nullable|confirmed',
        ]);

        if (!empty($validated['password'])) {
            Mail::send('mails.password_changed', ['user' => $user], function ($message) use ($user) {
                $message->to($user->email)
                        ->subject('<PERSON><PERSON><PERSON>i Bildirimi');
            });
            

            
        }

        $user->update($validated);

        $this->updated_log([
            'id'   => $user->id,
            'name' => $user->name,
        ]);

        return redirect()->route('admin.profile.index')->with('success', 'Profiliniz başarıyla güncellendi');
    }

}