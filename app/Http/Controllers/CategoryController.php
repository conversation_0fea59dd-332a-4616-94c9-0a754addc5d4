<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Traits\CanExport;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Lang;

class CategoryController extends SearchableController
{
    use CanExport;

    public $title = 'Kategori';

    public $has_language = true;

    public $listing_headers = [
        [
            'type'         => 'detail',
            'label'        => 'Kategoriler',
            'title_bind'   => 'title',
            'info_bind'    => [
                'url',
            ],
            'route'        => 'edit',
            'aspect_ratio' => '1:1',
            'keep'         => true,
        ]
    ];
    public function __construct()
    {
        if (Auth::check() && Auth::user()->super_admin !== 1) {
            return redirect()->route('admin.dashboard.index')->with('error', __('Yetkisiz erişim.'));
        }
    }

    public $searchable_fields = [
        'title'
    ];

    public $sorting_options = [
        'title asc' => 'Alfabetik (A-Z)',
        'title desc' => 'Alfabetik (Z-A)',
        'created_at desc' => 'Oluşturulma (Yeni > Eski)',
        'created_at asc' => 'Oluşturulma (Eski > Yeni)',
    ];


    public function store(Request $request)
    {
        $validated = $request->validate([
            'title'      => 'required|string|max:255',
            'background' => 'sometimes',
            'color'      => 'sometimes'
        ]);

        $validated['lang'] = Lang::getLocale();
        //$validated['company_id'] = Auth::user()->company_id;

        $category = Category::create($validated);


        $this->created_log([
            'id'    => $category->id,
            'title' => $category->title,
        ]);

        return redirect()->route('admin.category.edit', $category->id)->with('success', 'Kategori başarıyla oluşturuldu.');
    }

    public function update(Request $request, $id)
    {

        $validated = $request->validate([
            'title'      => 'required|string|max:255',
            'background' => 'sometimes',
            'color'      => 'sometimes'
        ]);


        $validated['image_id'] = request('image');

        $category = Category::findOrFail($id);

        $category->update($validated);

        $this->updated_log([
            'id'    => $category->id,
            'title' => $category->title,
        ]);

        return redirect()->route('admin.category.edit', $category->id)->with('success', 'Kategori bilgileri başarıyla güncellendi.');
    }


}
