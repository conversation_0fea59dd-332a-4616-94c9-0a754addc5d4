<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SmsController extends Controller
{
    function sendSMS($phone, $message){
        if ($phone == null || $message == null) {
            return false;
        }

        $sms_msg = array(
            "username" => env("VERIMOR_USERNAME"), // https://oim.verimor.com.tr/sms_settings/edit adresinden öğrenebilirsiniz.
            "password" => env("VERIMOR_PASSWORD"), // https://oim.verimor.com.tr/sms_settings/edit adresinden belirlemeniz gerekir.
            "messages" => array(
                array(
                "msg" => $message,
                "dest" => $phone
                )
            )
        );

        $ch = curl_init('http://sms.verimor.com.tr/v2/send.json');
        curl_setopt_array($ch, array(
            CURLOPT_POST => TRUE,
            CURLOPT_RETURNTRANSFER => TRUE,
            CURLOPT_HTTPHEADER => array('Content-Type: application/json'),
            CURLOPT_POSTFIELDS => json_encode($sms_msg),
        ));
        $http_response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        if($http_code != 200){
          echo "$http_code $http_response\n";
          return false;
        }

        return $http_response;
    }
}
