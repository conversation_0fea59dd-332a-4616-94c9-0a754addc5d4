<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Traits\CanExport;
use App\Models\Bulletin;
use App\Models\Contact;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
class DemoController extends SearchableController {
    use CanExport;

    public $title = "Demo Talepleri";

    public $listing_headers = [
        [
            'type'       => 'detail',
            'label'      => 'E-Posta',
            'title_bind' => 'email',
            'keep'       => true,
        ],
        [
            'type'       => 'detail',
            'label'      => 'Telefon',
            'title_bind' => 'phone',
            'keep'       => true,
        ],
        [
            'type' => 'detail',
            'label' => 'Çalışan Sayısı',
            'title_bind'  => 'training_participants',
            'keep'       => true,
        ],
        [
            'type' => 'detail',
            'label' => '<PERSON><PERSON><PERSON>',
            'title_bind'  => 'hear_about',
            'keep'       => true,
        ],
        [
            'type' => 'date',
            'label' => '<PERSON><PERSON><PERSON><PERSON>',
            'alignment' => 'right',
            'bind' => 'created_at'
        ],
    ];



    public function store(Request $request) {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255',
            'message' => 'required|string|max:255',
        ]);

        $contact = Contact::create([
            'name'    => $validated['name'],
            'email'   => $validated['email'],
            'message' => $validated['message'],
            'ip'      => $request->ip(),
        ]);

        if ($contact) {
            return response()->json([
                'success' => true,
                'message' => 'Mesajınız başarıyla gönderildi.'
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Mesajınız gönderilemedi.'
        ]);
    }

    public function storeBulletin(Request $request) {
        $validated = $request->validate([
            'name' => 'sometimes|nullable',
            'email' => 'required|string|email|max:255',
        ]);

        $bulletin = Bulletin::firstOrCreate(
            [
                'email'   => $validated['email'],
            ],
            [
                'name'    => Arr::get($validated, 'name'),
                'ip'      => $request->ip(),
            ]
        );

        if ($bulletin) {
            return response()->json([
                'success' => true,
                'message' => 'Bültene kayıt olundu.'
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Bültene kaydolurken hata oluştu.'
        ]);
    }

    public function editData($item) {
        $this->read_log([
            'id'    => $item->id,
            'title' => $item->name,
        ]);

        return [];
    }
}
