<?php

namespace App\Http\Controllers;

use App\Enums\MailTemplateType;
use App\Models\CourseEnrolment;
use App\Models\CourseSession;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;

class ApiController extends Controller
{
    public function reminderMail()
    {

        // Cron görevini çalıştır
        $seven_day_receivers = $this->sevenDaysAgo();
        $one_day_receivers = $this->oneDaysAgo();
        $user_feedback = $this->feedback();
        return [
            'seven_day_receivers' => $seven_day_receivers,
            'one_day_receivers' => $one_day_receivers,
            'user_feedback' => $user_feedback,
            'message' => 'Cron çalıştırıldı.'
        ];


    }

    public function reminderOneHourMail()
    {

        // Cron görevini çalıştır
        $receivers = $this->oneHourAgo();
        return [
            'message' => 'Cron çalıştırıldı.',
            'receivers' => $receivers,
        ];


    }

    private function sevenDaysAgo()
    {
        $targetDate = now()->addDays(7); // Bir hafta öncesi
        $courses = CourseSession::whereDate('date', '=', $targetDate->toDateString())
            ->get();
        $receivers = [];

        foreach ($courses as $course) {
            foreach ($course->users as $participant) {
                $receivers[] = $participant->email;
                $this->sendFirstReminder($participant, $course); // Bir hafta önceki hatırlatma
            }
        }
        return $receivers;

    }

    private function feedback()
    {
        $twoDaysAgo = Carbon::now()->subDays(1)->format('Y-m-d');

        $enrolments = CourseEnrolment::whereHas('courseSession', function ($query) use ($twoDaysAgo) {
            $query->whereDate('date', $twoDaysAgo);
        })->with('user')->get();
        $receivers = [];

        foreach ($enrolments as $enrolment) {
            $receivers[] = $enrolment->user->email;
            $this->sendFeedbackReminder($enrolment);
        }

        return $receivers;

    }

    private function oneDaysAgo()
    {
        $targetDate = now()->addDay(); // Bir hafta öncesi
        $courses = CourseSession::whereDate('date', '=', $targetDate->toDateString())
            ->get();

        $receivers = [];
        foreach ($courses as $course) {
            foreach ($course->users as $participant) {
                $receivers[] = $participant->email;
                $this->sendSecondReminder($participant, $course); // Bir hafta önceki hatırlatma
            }
        }
        return $receivers;

    }

    private function oneHourAgo()
    {
        $startDateTime = now(); // 3 saat öncesi
        $endDateTime = now()->addHours(3);; // Şu anki zaman
        $courses = CourseSession::whereDate('date', '=', $startDateTime->toDateString())
            ->whereBetween('start_time', [$startDateTime->toTimeString(), $endDateTime->toTimeString()])
            ->get();
            $receivers = [];
        foreach ($courses as $course) {
            foreach ($course->users as $participant) {
                $receivers[] = $participant->email;
                $this->sendFinalReminder($participant, $course); // Bir hafta önceki hatırlatma
            }
        }
        return $receivers;

    }

    private function sendFirstReminder($participant, $course)
    {
        $sms = new SmsController();
        $data = [
            'name' => $participant->name,
            'email_address' => $participant->email,
            'course_title' => $course->course->title,
            'course_date' => 'Bugün',
            'course_start_time' => Carbon::parse($course->start_time)->format('H:i'),
            'course_connection_link' => "https://istegelis.com/zoom/" . $course->course->slug,
        ];
        $mail = replace_mail_template(MailTemplateType::FIRST_REMINDER,$data);
        $sms_content = replace_sms_template('first_reminder',$data);

        $sms->sendSMS($participant->phone, $sms_content);
        Mail::send('mails.reminder_first', [
            'mail' => $mail,
            'course' => $course
        ], function ($message) use ($participant, $mail) {
            $message->to($participant->email);
            $message->subject($mail['subject']);
        });
    }

    private function sendSecondReminder($participant, $course )
    {
        $sms = new SmsController();
        $data = [
            'name' => $participant->name,
            'email_address' => $participant->email,
            'course_title' => $course->course->title,
            'course_date' => 'Bugün',
            'course_start_time' => Carbon::parse($course->start_time)->format('H:i'),
            'course_connection_link' => "https://istegelis.com/zoom/" . $course->course->slug,
        ];
        $mail = replace_mail_template(MailTemplateType::ONE_DAY_REMINDER,$data);
        $sms_content = replace_sms_template('1_day_reminder',$data);

        $sms->sendSMS($participant->phone, $sms_content);
        Mail::send('mails.reminder_second', [
            'mail' => $mail,
            'course' => $course
        ], function ($message) use ($participant, $mail) {
            $message->to($participant->email);
            $message->subject($mail['subject']);
        });
    }

    public function sendFinalReminder($participant, $course)
    {
        $sms = new SmsController();
        $data = [
            'name' => $participant->name,
            'email_address' => $participant->email,
            'course_title' => $course->course->title,
            'course_date' => 'Bugün',
            'course_start_time' => Carbon::parse($course->start_time)->format('H:i'),
            'course_connection_link' => "https://istegelis.com/zoom/" . $course->course->slug,
        ];
        $mail = replace_mail_template(MailTemplateType::LAST_REMINDER,$data);
        $sms_content = replace_sms_template('final_reminder',$data);

        $sms->sendSMS($participant->phone, $sms_content);
        Mail::send('mails.reminder_final', [
            'mail' => $mail,
            'course' => $course
        ], function ($message) use ($participant, $mail) {
            $message->to($participant->email);
            $message->subject($mail['subject']);
        });
    }

    private function sendFeedbackReminder($enrolment)
    {

        $data = [
            'name' => $enrolment->user->name,
            'email_address' => $enrolment->user->email,
            'course_title' => $enrolment->courseSession->course->title,
            'course_date' => Carbon::parse($enrolment->courseSession->date)->format('d F Y'),
            'course_start_time' => Carbon::parse($enrolment->courseSession->start_time)->format('H:i'),
            'course_connection_link' => "https://istegelis.com/zoom/" . $enrolment->courseSession->course->slug,
            'feedback_link' => "https://istegelis.com/feedback/" . $enrolment->enrolment_hash,
        ];

        $mail = replace_mail_template(MailTemplateType::USER_FEEDBACK,$data);

        Mail::send('mails.reminder_final', [
            'mail' => $mail,
        ], function ($message) use ($enrolment, $mail) {
            $message->to($enrolment->user->email);
            $message->subject($mail['subject']);
        });

    }

}
