<?php

namespace App\Http\Controllers;

use App\Exports\CompanyExport;
use App\Models\CourseSession;
use App\Models\MailTemplate;
use App\Models\SmsTemplate;
use App\Models\UserLog;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use App\Models\CompanyDetail;
use App\Models\CompanyOption;
use App\Models\CompanySetting;
use App\Models\PackageType;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use App\Enums\MailTemplateType;
use App\Models\Banner;
use Maatwebsite\Excel\Facades\Excel;


class DashboardController extends SearchableController
{

    /**
     * How should report be generated (monthly by default)
     *
     * @var string
     */
    protected $view = "monthly";

    /**
     * List of allowed view types
     *
     * @var array
     */
    protected $allowed_views = [
        'daily',
        'monthly',
        'yearly',
        'weekly'
    ];


    public function __construct() {
        $view = request('view', 'daily');

        if (!in_array($view, $this->allowed_views)) {
            $view = 'daily';
        }

        $this->view = $view;
    }

    public function index() {

        $requested_date = Carbon::parse(request('date', date('Y-m-d')));

        $requested_year = Carbon::parse(request('year', date('Y-m-d')));

        $user = Auth::user();

        if($user->super_admin == 1) {
            return $this->superAdminIndex();
        }

        $user_company = $user->company_id;
        $companyId = $user->company_id;
        $userCount = User::where('company_id', $companyId)->count();
        $adminCount = User::where('company_id', $companyId)->where('role', 'admin')->count();

        $companyDetails = CompanyDetail::where('company_id', $companyId)->first();
        $companySettings = CompanySetting::where('company_id', $companyId)->first();
        $company = CompanyDetail::where('company_id', $companyId)->first();

        $setting = CompanySetting::where('company_id', $companyId)->where('setting_key', 'package_type')->first()->setting_value;
        $companyUserCapacity = ($userCount * 100) / $setting;

        $loginDataList = UserLog::select('*', DB::raw('date(`date`) as `day_date`'))
            ->whereDate('date', '>=', now()->subDays(29))
            ->get();

        $divisionList = ['startOfMonth', 'startOfWeek', 'startOfDay'];
        while (count($divisionList) > 0) {
            $divisionName = array_shift($divisionList);

            $newLoginDataList = $loginDataList->groupBy(function ($item) use ($divisionName) {
                return Carbon::parse($item->date)->{$divisionName}()->format('Y-m-d');
            });

            if (count($divisionList) === 0) break;
            if ($newLoginDataList->count() > 30) continue;
        }
        $loginDataList = $newLoginDataList;


        $newLoginDataList = [];
        foreach ($loginDataList as $key => $loginData) {
            $dataList = collect();
            foreach ($loginData as $login) {
                $dataList->push($login->user_id);
            }

            $newLoginDataList['labels'][] = $key;
            $newLoginDataList['counts'][] = $dataList->unique()->count();
        }

        //$loginDataList = array_values($newLoginDataList);
        $loginDataList = $newLoginDataList;

        $totalUser = User::where('company_id',$user->company_id)->count();

        $lastLoginUser = UserLog::select("user_id")->whereDate('date', '>=', now()->subDays(29))
            ->groupBy('user_id') // user_id'leri gruplayarak benzersizliği sağla
            ->get()
            ->count();

        return view('/admin/dashboard', [
            'user' => $user,
            'loginLog' => $loginDataList,
            'totalUser' => $totalUser,
            'lastLoginUser' => $lastLoginUser,
            'userCount' => $userCount,
            'companyUserCapacity' => $companyUserCapacity,
            'adminCount' => $adminCount,
            'company' => $company,
            'companyDetail' => $companyDetails,
            'companySetting' => $companySettings,
        ]);

    }

    private function superAdminIndex()
    {
        $this->resource = 'company';
        $this->title = "Firma Listesi";
        $this->listing_headers = [
            [
                'type'       => 'detail',
                'label'      => 'Firma Adı',
                'title_bind' => 'company_name',
                'route'      => 'company.edit',
                'image_bind' => 'logo',
                'keep'       => true,
            ],
            [
                'type'       => 'detail',
                'label'      => 'Firma İletişim',
                'title_bind' => 'contact_email',
                'route'      => 'company.edit',
                'info_bind'  => [
                    'phone_number'
                ],
                'keep'       => true,
            ],
            [
                'type'       => 'detail',
                'label'      => 'Abonelik Bilgisi',
                'route'      => 'company.edit',
                'title_bind' => 'package_type',
                'keep'       => true,
            ],
            [
                'type'       => 'detail',
                'label'      => 'Toplam Kullanıcı',
                'route'      => 'company.edit',
                'title_bind' => 'user_count',
                'keep'       => true,
            ]
        ];

        $this->sorting_options = [
            'company_details.company_id desc' => 'Oluşturulma (Yeni > Eski)',
            'company_details.company_id asc' => 'Oluşturulma (Eski > Yeni)',
        ];

        $items = $this->postFetchSort($this->fetchSort($this->buildSearchableQuery(CompanyDetail::query()->select('company_details.*', 'company_settings.setting_value as package_type', DB::raw('COUNT(users.id) as user_count'))
            ->leftJoin('company_settings', function($join) {
                $join->on('company_details.company_id', '=', 'company_settings.company_id')
                    ->where('company_settings.setting_key', '=', 'package_type');
            })
            ->leftJoin('users', 'company_details.company_id', '=', 'users.company_id')
            ->groupBy('company_details.company_id', 'company_details.company_name', 'company_details.address', 'company_details.contact_email','company_details.company_slug','company_details.phone_number','company_details.logo', 'company_settings.setting_value'))->get()->map(function ($item) {
            switch ($item->package_type) {
                case 1:
                    $item->package_type = 'Red';
                    break;
                case 2:
                    $item->package_type = 'Blue';
                    break;
                case 3:
                    $item->package_type = 'Yellow';
                    break;
                case 99999:
                    $item->package_type = 'Green';
                    break;
                default:
                    $item->package_type = 'Unknown'; // Diğer durumlar için varsayılan değer
                    break;
            }

            return $item;
        })));

        $data = array_merge(
            ['items' => $items,
                'user' => Auth::user()
            ],
            [
                'meta_tags' => [
                    'title' => $this->title ?? false
                ]
            ],
            $this->indexData()
        );

        return view('/admin/dashboard_superadmin', $this->getViewData(), $data);
    }

    public function banner() {
        $banners = Banner::where('banner_status', 1)->orderBy('banner_order', 'asc')->get();
        return view ('admin/banner/index', [
            'banners' => $banners
        ]);
    }

    public function bannerCreate() {
        return view('admin/banner/create');
    }

    public function bannerCreatePost(Request $request) {
        $request->validate([
            'banner_title' => 'required|string|max:255',
            'banner_image' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'banner_order' => 'required|integer',
        ]);

        $banner = new Banner();
        $banner->banner_title = $request->banner_title;
        $banner->banner_order = $request->banner_order;

        if ($request->hasFile('banner_image')) {
            $file = $request->file('banner_image');
            $newFileName = time(). rand() . '_' . $file->getClientOriginalName();
            $response = Http::attach(
                'file', file_get_contents($file->path()), $newFileName
            )->post('https://istegelis.com/banner-image-upload');

            if ($response->successful()) {
                $banner->banner_image = '/img/banner/' . $newFileName;
            }
        }

        $banner->save();

        return redirect()->route('admin.banner.index')->with('success', 'Banner created successfully');
    }

    public function bannerEdit($banner_id) {
        $banner = Banner::where('id', $banner_id)->first();
        return view('admin/banner/edit', [
            'banner' => $banner
        ]);
    }

    public function companyDetail() {
        $companyId = Auth::user()->company_id;
        $company = CompanyDetail::where('company_id', $companyId)->first();

        return view('admin/company/detail', [
            'company' => $company
        ]);
    }
    public function companyCreate() {
        if(Auth::user()->super_admin != 1) {
            return redirect()->route('admin.dashboard.index')->with('error', __("Bu sayfayı görüntülemeke için yetkiniz yok!"));
        }

        return view('admin/company/create', [
            //'timezones' => $this->getTimezones()
        ]);
    }

    public function companyCreatePost()
    {

        if(Auth::user()->super_admin != 1) {
            return redirect()->route('admin.dashboard.index')->with('error', __("Bu sayfayı görüntülemeke için yetkiniz yok!"));
        }

        request()->validate([
                'company_name' => 'required|string|max:255',
                'address' => 'required|string',
                'contact_email' => 'required|email|max:255',
                'phone_number' => 'required|string|max:15',
                'package_type' => 'required|string',
                'user_limit' => 'required|string',
                'description' => 'nullable|string',
                'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            ]);
            $sms = new SmsController();
            $companySlug = Str::slug(request()->company_name, '-');

            $isCheck = CompanyDetail::where('contact_email', request()->contact_email)
                ->orWhere('phone_number', request()->phone_number)
                ->orWhere('company_slug', $companySlug)
                ->first();

            if($isCheck) {
                return redirect()->back()->with('error', trans('Bu e-posta veya telefon numarası zaten kullanılıyor'));
            }

        $checkUser = User::where('email', request()->contact_email)->orWhere('phone', request()->phone_number)->first();
        if($checkUser) {
            return redirect()->back()->with('error', trans('Bu e-posta veya telefon numarası zaten kullanılıyor. Bu e-posta veya telefon numarasına ati kullanıcıyı silmelisiniz.'));
        }


            // Logo işlemleri
            $logoUrl = null;
            if (request()->hasFile('logo')) {

                $file = request()->file('logo');
                // Dosya adını değiştir
                $newFileName = time(). rand() . '_' . $file->getClientOriginalName();

                // API'ye dosya yükleme
                $response = Http::attach(
                    'file', file_get_contents($file->path()), $newFileName
                )->post('https://istegelis.com/company-logo-upload');

                // API yanıtını kontrol et
                if ($response->successful()) {
                    $logoUrl = '/img/company/logo/' . $newFileName;

                }
            }

            // Kullanıcı bilgilerini veritabanına kaydet
            $companyDetail = CompanyDetail::create([
                'company_name' => request()->company_name,
                'company_slug' => $companySlug,
                'address' => request()->address,
                'contact_email' => request()->contact_email,
                'phone_number' => request()->phone_number,
                'logo' => $logoUrl,
            ]);

            // Şirket seçeneklerini kaydetme
            $options = [
                'name' => request()->company_name,
                'description' => request()->description,
                'email' => request()->contact_email,
                'address' => request()->address,
                //'theme_color' =>request()->theme_color,
            ];

            foreach ($options as $option => $value) {
                CompanyOption::create([
                    'company_id' => $companyDetail->company_id,
                    'option' => $option,
                    'value' => $value,
                ]);
            }

            // Şirket ayarlarını kaydetme
            $settings = [
                //'theme' => 'default',
                //'timezone' => request()->time_zone,
                'language' => 'tr',
                'package_type' => request()->package_type,
                'user_limit' => request()->user_limit,
            ];

            foreach ($settings as $key => $value) {
                CompanySetting::create([
                    'company_id' => $companyDetail->company_id,
                    'setting_key' => $key,
                    'setting_value' => $value,
                ]);
            }

            $random_password = substr(str_shuffle('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 8);

        // Varsayılan SMS şablonları
        $smsTemplates = SmsTemplate::where('company_id', null)->get();
        foreach ($smsTemplates as $template) {
            SmsTemplate::create([
                'company_id' => $companyDetail->company_id,
                'title' => $template->title,
                'content' => $template->content,
                'type' => $template->type,
            ]);
        }

        // Varsayılan E-posta şablonları
        $emailTemplates = MailTemplate::where('company_id', null)->get();
        foreach ($emailTemplates as $template) {
            MailTemplate::create([
                'company_id' => $companyDetail->company_id,
                'name' => $template->name,
                'title' => $template->title,
                'subject' => $template->subject,
                'content' => $template->content,
                'type' => $template->type,
            ]);
        }


            User::create([
                'company_id' => $companyDetail->company_id,
                'email' => request()->contact_email,
                'phone' => request()->phone_number,
                'username' => $companySlug,
                'name' => request()->company_name,
                'password' => $random_password,
                'role' => 'admin',
                'status' => 'active',
            ]);
            $data = [
                'email_address' => request()->contact_email,
                'password' => $random_password,
            ];
            $mail = replace_mail_template(MailTemplateType::COMPANY_CREATE_SUCCESS,$data);
            $sms_content = replace_sms_template('register_success',$data);

            $sms->sendSMS(request()->phone_number, $sms_content);
            Mail::send('mails.new_account_create_mail_', [
                'mail' => $mail,
            ], function ($message) use ($mail, $data) {
                $message->to($data['email_address']);
                $message->subject($mail['subject']);
            });


        return redirect()->route('admin.company.edit',['company_id' => $companyDetail->company_id])->with('success', __('Firma başarı ile oluşturuldu.'));


    }

    public function companyEdit($company_id = null) {
        if(empty($company_id)) {
            $company_id = Auth::user()->company_id;
        }
        if($company_id != Auth::user()->company_id && Auth::user()->super_admin != 1) {
            return redirect()->route('admin.dashboard.index')->with('error', __("Bu sayfayı görüntülemek için yetkiniz yok!"));
        }
        $company = CompanyDetail::where('company_id', $company_id)->first();
        if(!$company) {
            return redirect()->route('admin.dashboard.index')->with('error', __("Firma bulunamadı!"));
        }
        $company->settings = $company->settings()->get();
        $company->options = $company->options()->get();
        $company->description = $this->getOptionValue('description');

        return view('admin/company/edit', [
            'user' => Auth::user(),
            'company' => $company,
            //'timezones' => $this->getTimezones()
        ]);
    }

    public function companyUpdate($company_id = null) {
        if(empty($company_id) && Auth::user()->super_admin != 1) {
            $company_id = Auth::user()->company_id;
        }

        $company = CompanyDetail::where('company_id', $company_id)->first();
        if(!$company) {
            return redirect()->route('admin.dashboard.index')->with('error', __("Firma bulunamadı!"));
        }
        $companyOptionsDescription = CompanyOption::where('company_id', $company_id)->where('option', 'description')->first();
        $companyOptionsDescription->value = request()->description;
        $companyOptionsDescription->save();

        /*$companyOptionsThemeColor = CompanyOption::where('company_id', $company_id)->where('option', 'theme_color')->first();
        $companyOptionsThemeColor->value = request()->theme_color;
        $companyOptionsThemeColor->save();*/


        $companyDetailAddress = CompanyDetail::where('company_id', $company_id)->first();
        if (request()->hasFile('logo')) {

            $file = request()->file('logo');
            // Dosya adını değiştir
            $newFileName = time(). rand() . '_' . $file->getClientOriginalName();

            // API'ye dosya yükleme
            $response = Http::attach(
                'file', file_get_contents($file->path()), $newFileName
            )->post('https://istegelis.com/company-logo-upload');

            // API yanıtını kontrol et
            if ($response->successful()) {
                $logoUrl = '/img/company/logo/' . $newFileName;
                $companyDetailAddress->logo = $logoUrl;

            }

        }
        $companyDetailAddress->address = request()->address;
        $companyDetailAddress->save();

        /*$companySettingsTimezone = CompanySetting::where('company_id', $company_id)->where('setting_key', 'timezone')->first();
        $companySettingsTimezone->setting_value = request()->time_zone;
        $companySettingsTimezone->save();*/
        if(Auth::user()->super_admin == 1) {
            $companySettingsTimezone = CompanySetting::where('company_id', $company_id)->where('setting_key', 'package_type')->first();
            $companySettingsTimezone->setting_value = request()->package_type;
            $companySettingsTimezone->save();
            $companySettingsUserLimit = CompanySetting::where('company_id', $company_id)->where('setting_key', 'user_limit')->first();
            if($companySettingsUserLimit) {
                $companySettingsUserLimit->setting_value = request()->user_limit;
                $companySettingsUserLimit->save();
            } else {
                CompanySetting::create([
                    'company_id' => $company_id,
                    'setting_key' => 'user_limit',
                    'setting_value' => request()->user_limit,
                ]);
            }
        }





        // $options = [
        //     'description' => $request->description,
        //     'address' => $request->address,
        //     'theme_color' => $request->theme_color,
        // ];

        // foreach ($options as $option => $value) {
        //     CompanyOption::where('company_id', $company_id)
        //     ->where('option', $option)
        //     ->update(['value' => $value]);
        // }


        return redirect()->route('admin.company.edit',['company_id' => $company_id])->with('success', 'Company updated successfully');



        $company->logo = $request->logo;
        $company->save();
    }

    private function getTimezones()
    {
        $timezones = \DateTimeZone::listIdentifiers(\DateTimeZone::ALL);

        $data = [];
        foreach ($timezones as $timezone):
            $data[$timezone] = $timezone;
        endforeach;

        return $data;

    }

    public function getOptionValue($setting_key)
    {
        $company_id = Auth::user()->company_id;
        $setting = CompanyOption::select('value')
            ->where('company_id', $company_id)
            ->where('option', $setting_key)
            ->first();

        if ($setting) {
            return $setting->value;
        }

        return null; // veya isteğinize göre başka bir değer döndürebilirsiniz
    }

    public function  getSettingValue($setting_key)
    {
        $company_id = Auth::user()->company_id;
        $setting = CompanySetting::select('setting_value')
            ->where('company_id', $company_id)
            ->where('setting_key', $setting_key)
            ->first();

        if ($setting) {
            return $setting->setting_value;
        }

        return null; // veya isteğinize göre başka bir değer döndürebilirsiniz
    }

    public function companySubscription() {
        $company_id = Auth::user()->company_id;
        $packages = PackageType::all();
        $company = CompanyDetail::where('company_id', $company_id)->first();
        $company->settings = $company->settings()->get();
        $company->options = $company->options()->get();
        $company->description = $this->getOptionValue('description');
        $company->package_type = $this->getSettingValue('package_type');


        return view('admin/company/subscription', [
            'company' => $company,
            'packages' => $packages,
        ]);
    }

    /**
     * Get next, previous, requested and current days
     *
     * @param Carbon|string $date
     * @return array
     */
    public function getDailyDates($current_date) {
        $date = Carbon::parse($current_date)->startOfDay();

        $today = Carbon::now()->startOfDay();
        $previous = $date->copy()->subDay();
        $previou_more = $previous->copy()->subDay();
        $next = $date->copy()->addDay();
        $next_more = $next->copy()->addDay();

        $dates = [
            'current' => [
                'date' => $date->format('Y-m-d'),
                'text' => $date->isoFormat("D MMM Y"),
            ],
            'previous' => [
                'date' => $previous->format('Y-m-d'),
                'text' => $previous->isoFormat('D MMM Y'),
                'more' => [
                    'date' => $previou_more->format('Y-m-d'),
                    'text' => $previou_more->isoFormat('D MMM Y'),
                ],
            ],
            'next' => [
                'date' => $next->format('Y-m-d'),
                'text' => $next->isoFormat('D MMM Y'),
                'is_future' => $next->isFuture(),
                'more' => [
                    'date' => $next_more->format('Y-m-d'),
                    'text' => $next_more->isoFormat('D MMM Y'),
                    'is_future' => $next_more->isFuture(),
                ],
            ],
            'today' => [
                'date' => $today->format('Y-m-d'),
                'text' => $today->isoFormat('D MMM Y'),
                'is_today' => $date == $today,
            ]
        ];

        return $dates;
    }

    /**
     * Get next, previous, requested and current weeks
     *
     * @param Carbon|string $date
     * @return array
     */
    public function getWeeklyDates($current_date) {
        $date = Carbon::parse($current_date)->startOfWeek();

        $today = Carbon::now()->startOfWeek();
        $previous = $date->copy()->subWeek();
        $previou_more = $previous->copy()->subWeek();
        $next = $date->copy()->addWeek();
        $next_more = $next->copy()->addWeek();

        $dates = [
            'current' => [
                'date' => $date->format('Y-m-d'),
                'text' => $date->isoFormat("W[. hafta] MMM Y"),
            ],
            'previous' => [
                'date' => $previous->format('Y-m-d'),
                'text' => $previous->isoFormat('W[. hafta] Y'),
                'more' => [
                    'date' => $previou_more->format('Y-m-d'),
                    'text' => $previou_more->isoFormat('W[. hafta] Y'),
                ],
            ],
            'next' => [
                'date' => $next->format('Y-m-d'),
                'text' => $next->isoFormat('W[. hafta] Y'),
                'is_future' => $next->isFuture(),
                'more' => [
                    'date' => $next_more->format('Y-m-d'),
                    'text' => $next_more->isoFormat('W[. hafta] Y'),
                    'is_future' => $next_more->isFuture(),
                ],
            ],
            'today' => [
                'date' => $today->format('Y-m-d'),
                'text' => $today->isoFormat('W[. hafta] Y'),
                'is_today' => $date == $today,
            ]
        ];

        return $dates;
    }

    /**
     * Get next, previous, requested and current years
     *
     * @param Carbon|string $date
     * @return array
     */
    public function getYearlyDates($current_date) {
        $date = Carbon::parse($current_date)->firstOfYear();

        $today = Carbon::now()->firstOfYear();
        $previous = $date->copy()->subYear();
        $previou_more = $previous->copy()->subYear();
        $next = $date->copy()->addYear();
        $next_more = $next->copy()->addYear();

        $dates = [
            'current' => [
                'date' => $date->format('Y-m-d'),
                'text' => $date->isoFormat('Y'),
            ],
            'previous' => [
                'date' => $previous->format('Y-m-d'),
                'text' => $previous->isoFormat('Y'),
                'more' => [
                    'date' => $previou_more->format('Y-m-d'),
                    'text' => $previou_more->isoFormat('Y'),
                ],
            ],
            'next' => [
                'date' => $next->format('Y-m-d'),
                'text' => $next->isoFormat('Y'),
                'is_future' => $next->isFuture(),
                'more' => [
                    'date' => $next_more->format('Y-m-d'),
                    'text' => $next_more->isoFormat('Y'),
                    'is_future' => $next_more->isFuture(),
                ],
            ],
            'today' => [
                'date' => $today->format('Y-m-d'),
                'text' => $today->isoFormat('Y'),
                'is_today' => $date == $today,
            ]
        ];

        return $dates;
    }

    /**
     * Get next, previous, requested and current months
     *
     * @param Carbon|string $date
     * @return array
     */
    public function getMonthlyDates($current_date) {
        $date = Carbon::parse($current_date)->firstOfMonth();

        $today = Carbon::now()->firstOfMonth();
        $previous = $date->copy()->subMonth();
        $previou_more = $previous->copy()->subMonth();
        $next = $date->copy()->addMonth();
        $next_more = $next->copy()->addMonth();

        $dates = [
            'current' => [
                'date' => $date->format('Y-m-d'),
                'text' => $date->isoFormat('MMMM Y'),
            ],
            'previous' => [
                'date' => $previous->format('Y-m-d'),
                'text' => $previous->isoFormat('MMMM Y'),
                'more' => [
                    'date' => $previou_more->format('Y-m-d'),
                    'text' => $previou_more->isoFormat('MMMM Y'),
                ],
            ],
            'next' => [
                'date' => $next->format('Y-m-d'),
                'text' => $next->isoFormat('MMMM Y'),
                'is_future' => $next->isFuture(),
                'more' => [
                    'date' => $next_more->format('Y-m-d'),
                    'text' => $next_more->isoFormat('MMMM Y'),
                    'is_future' => $next_more->isFuture(),
                ],
            ],
            'today' => [
                'date' => $today->format('Y-m-d'),
                'text' => $today->isoFormat('MMMM Y'),
                'is_today' => $date == $today,
            ]
        ];

        return $dates;
    }


    public function companyDestroy($company_id)
    {
        $company = CompanyDetail::where('company_id', $company_id)->first();
        if(!$company) {
            return redirect()->route('admin.dashboard.index')->with('error', __("Firma bulunamadı!"));
        }

        $authUser = Auth::user();
        if($authUser->company_id == $company_id) {
            $companyList = CompanyDetail::where('company_id', '!=', $company_id)->first();
            if(!$companyList) {
                return redirect()->route('admin.dashboard.index')->with('error', __("Son firma silinemez!"));
            }
            $authUser->company_id = $companyList->company_id;
            $authUser->save();
        }

        $company_user = User::where('company_id', $company_id)->get();
        foreach ($company_user as $user) {
            //if($user->super_admin !== 1){
                $user->delete();
            //}
        }
        $company_option = CompanyOption::where('company_id', $company_id)->get();
        foreach ($company_option as $option) {
            $option->delete();
        }
        $company_setting = CompanySetting::where('company_id', $company_id)->get();
        foreach ($company_setting as $setting) {
            $setting->delete();
        }
        $company->delete();
        return redirect()->route('admin.dashboard.index')->with('success', __("Firma başarı ile silindi."));
    }

    public function scheduleControl()
    {
        try {
            $timeframe = request()->timeframe;

            // Farklı zaman aralıklarına göre doğru metodları kullanarak tarih ekleyin
            switch ($timeframe) {
                case '7days':
                    $targetDate = now()->subDays(7); // Bir hafta öncesi
                    $courses = CourseSession::whereDate('date', '=', $targetDate->toDateString())
                        ->get();
                    break;
                case '1day':
                    $targetDate = now()->subDay(); // Bir gün öncesi
                    $courses = CourseSession::whereDate('date', '=', $targetDate->toDateString())
                        ->get();
                    break;
                case '1hour':
                    $targetDate = now()->subHour(); // Bir saat öncesi
                    $courses = CourseSession::whereDate('date', '=', $targetDate->toDateString())
                        ->whereTime('start_time', '>=', $targetDate->toTimeString())
                        ->get();
                    break;
                default:
                    $targetDate = now();
                    $courses = [];
                    break;
            }


            // Katılımcılar için hatırlatma e-postaları gönder
            foreach ($courses as $course) {
                foreach ($course->users as $participant) {
                    try {
                        if ($timeframe == '7days') {
                            $this->sendFirstReminder($participant, $course->course); // Bir hafta önceki hatırlatma
                        } elseif ($timeframe == '1day') {
                            $this->sendSecondReminder($participant, $course->course); // Bir gün önceki hatırlatma
                        } elseif ($timeframe == '1hour') {
                            $this->sendFinalReminder($participant, $course->course); // Bir saat önceki hatırlatma
                        }
                    } catch (\Exception $e) {
                        // Herhangi bir hatırlatma gönderimi sırasında oluşabilecek hataları yakala
                        Log::error("Hatırlatma e-postası gönderilirken hata oluştu: " . $e->getMessage());
                    }
                }
            }


        } catch (\Exception $e) {
            // Tüm süreci kapsayan bir hata yakalandığında logla
            Log::error("Zamanlanmış hatırlatma işlemi sırasında bir hata oluştu: " . $e->getMessage());
        }
    }

    public function sendFirstReminder($participant, $course)
    {
        Mail::send('mails.reminder_first', [
            'participant' => $participant,
            'course' => $course
        ], function ($message) use ($participant, $course) {
            $message->to($participant->email);
            $message->subject("Hatırlatma: {$course->name} Eğitimi");
        });
    }

    public function sendSecondReminder($participant, $course)
    {
        Mail::send('mails.reminder_second', [
            'participant' => $participant,
            'course' => $course
        ], function ($message) use ($participant, $course) {
            $message->to($participant->email);
            $message->subject("Son Hatırlatma: {$course->name} Eğitimi Yarın");
        });
    }

    public function sendFinalReminder($participant, $course)
    {
        Mail::send('mails.reminder_final', [
            'participant' => $participant,
            'course' => $course
        ], function ($message) use ($participant, $course) {
            $message->to($participant->email);
            $message->subject("Eğitim İçin Son Hazırlıklar: {$course->name}");
        });
    }

    public function companyExport()
    {
        return Excel::download(new CompanyExport(), 'company-' . now()->format('Y-m-d') . '.xlsx');
    }

}
