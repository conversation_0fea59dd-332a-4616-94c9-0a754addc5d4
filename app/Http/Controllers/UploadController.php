<?php

namespace App\Http\Controllers;

use App\Models\File;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class UploadController extends Controller {

    public function upload(Request $request) {

        if ($request->hasFile('file')) {

            if ($request->file('file')->isValid()) {
                //
                $validated = $request->validate([
                    'file' => 'mimes:jpeg,png,gif,pdf|max:10000',
                ]);

                $prefix = str_pad(/* $user_id .'u'. */bin2hex(random_bytes(5)), 10, "0", STR_PAD_RIGHT);
                $infix = uniqid();
                $suffix = bin2hex(random_bytes(5));

                $filename = "{$prefix}_{$infix}_{$suffix}";
                $extension = $request->file->extension();

                $hash = Str::lower(Str::random(40));

                $original_name = $request->file->getClientOriginalName();
                $mime = $request->file->getMimeType();

                $request->file->storeAs('/public', $filename . "." . $extension);
                $url = Storage::url($filename . "." . $extension);

                $file = File::create([
                    //'user_id' => $user_id,
                    'hash' => $hash,
                    'url' => $url,
                    'title' => '',
                    'original_name' => $original_name,
                    'mime' => $mime,
                    'ip' => $request->ip()
                ]);


                $optimizable_mimes = [
                    'image/jpeg',
                    'image/png',
                    'image/gif',
                ];

                if (in_array($mime, $optimizable_mimes)) {

                    $_extension = null;

                    if ($mime == 'image/jpeg') {
                        $_extension = 'jpg';
                    } else if ($mime == 'image/png') {
                        $_extension = 'png';
                    } else if ($mime == 'image/gif') {
                        $_extension = 'gif';
                    }

                    $_file_path = public_path($file->url);
                    $_copy_path = public_path( ltrim($file->url, '/'));

                    rename($_file_path, $_copy_path);

                    $image = \Intervention\Image\Facades\Image::make($_copy_path);

                    $_filename = $image->filename;

                    if ($image->width() > 1500 && $_extension == 'png') {
                        unlink($_copy_path);
                        $file->delete();
                        return response()->json([
                            'message' => 'PNG görselleri 1500px\'den büyük olamaz. JPG yüklemeyi veya çözünürlüğü düşürmeyi deneyin.',
                        ], 400);
                    }

                    if ($image->width() > 2000) {
                        $image->resize(2000, null, function ($constraint) {
                            $constraint->aspectRatio();
                        });
                    }

                    $image->save(public_path('/storage/' . $_filename . '.' . $_extension), 80, $_extension);

                    $file->original_path = '/originals/' . ltrim($file->url, '/');
                    $file->url = '/storage/' . $_filename . '.' . $_extension;
                    $file->save();

                    $url = $file->url;
                }


                return response()->json([
                    'success'       => true,
                    'data'          => [
                        'id'        => $file->id,
                        'url'       => $url,
                        'hash'      => $file->hash,
                        'extension' => $extension
                    ],
                    'location'      => $url
                ], 201);
            }

            return response()->json([
                'success' => false,
                'message' => 'Yüklemeye çalıştığınız dosya doğrulanamadı.'
            ], 422);
        }

        return response()->json([
            'success' => false,
            'message' => 'Yüklenecek dosya bulunamadı.'
        ], 422);

    }

    public function destroy(Request $request) {

        $validated = $request->validate([
            'hash' => 'required'
        ]);

        $file = File::where('hash', $validated['hash'])->firstOrFail();
        $file->delete();

        return response()->json([
            'success' => true,
        ], 200);
    }

}
