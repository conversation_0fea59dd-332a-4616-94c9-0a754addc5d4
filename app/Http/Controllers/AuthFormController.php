<?php

namespace App\Http\Controllers;

use App\Http\Requests\LoginRequest;
use App\Models\School;
use App\Models\User;
use App\Models\UserDefault;
use App\Rules\TCKimlik;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class AuthFormController extends Controller {

    public function showLoginForm() {
        session('locale', 'en');

        if (Auth::check()) {
            if (Auth::user()->role == 'user') {
                return redirect()->route('home.index');
            }

            return redirect()->route('admin.dashboard.index');
        }
        
        return view('auth.login', [
            'meta_tags' => [
                'title' => 'Oturum Aç'
            ]
        ]);
    }

    public function tryToLogin(Request $request) {

        if (Auth::check()) {
            if (Auth::user()->role == 'user') {
                Auth::logout();
                return redirect()->back()->with('error', '<PERSON><PERSON>ya girme yetkiniz bulunmamaktadır.');
            }

            return redirect()->route('admin.dashboard.index');
        }

        $validated = $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        $attempting_user = User::where('email', $validated['email'])->orWhere('username', $validated['email'])->first();

        if (!$attempting_user) {
            return back()->withInput()->with('error', 'Bu bilgilere ait bir kullanıcı bulunamadı.');
        }


        if (Auth::attempt($validated, (bool) $request->get('remember'))) {
            if ($attempting_user->role == 'admin' || $attempting_user->role == 'moderator') {
                return redirect()->route('admin.dashboard.index');
            }
            Auth::logout();
            return redirect()->back()->with('error', 'Buraya girme yetkiniz bulunmamaktadır.');
            
        }

        return back()->withInput()->with('error', 'Bu bilgilere ait bir kullanıcı bulunamadı.');
    }

    public function register(Request $request) {

        if (Auth::check()) {
            if (Auth::user()->role == 'user') {
                return redirect()->route('home.index');
            }

            return redirect()->route('admin.dashboard.index');
        }

        $account = $request->validate([
            'name'     => 'required',
            'email'    => 'required|email|unique:users',
            'password' => 'required|confirmed|min:6',
            'phone'    => 'required',
        ]);

        $phone = parse_phone(Arr::get($account, 'phone'));
        if (!$phone) {
            return back()->withInput()->with('error', 'Telefon numarası geçersiz.');
        }

        $account['phone'] = $phone;
        $account['role'] = 'user';

        // -----

        $validated = $request->validate([
            'gender'            => 'sometimes|nullable',
            'dob'               => 'sometimes|date|nullable',
            'idno'              => ['sometimes', new TCKimlik, 'nullable'],
            // 'allergies'         => 'sometimes|nullable',
            // 'food_preference'   => 'sometimes|nullable',
            'school_id'         => 'sometimes|exists:schools,id|nullable',
            'other_school_name' => 'required_if:school_id,1,2',
            'school_branch'     => 'required_unless:school_id,null',
            'heard'             => 'sometimes|nullable',
            'heard_other'       => 'required_if:heard,other',
        ]);

        // Okul türünü al
        if (request('school_id')) {
            switch ($validated['school_id']) {
                // Lise
                case "1":
                    $validated['school_type'] = 'highschool';
                    $validated['school_name'] = $validated['other_school_name'];
                    break;
                // Diğer
                case "2":
                    $validated['school_type'] = 'other';
                    $validated['school_name'] = $validated['other_school_name'];
                    break;
                // Üniversite
                default:
                    $validated['school_type'] = 'university';
                    $validated['school_name'] = School::find($validated['school_id'])->title;
                    break;
            }
        }

        if (request('dob')) {
            $validated['dob'] = Carbon::parse($validated['dob'])->format('Y-m-d');
        }

        $user = User::create($account);

        UserDefault::create(array_merge([
            'user_id' => $user->id
        ], $validated));


        Auth::login($user);

        return redirect()->route('admin.dashboard.index');

    }

    public function logout(Request $request) {
        // activity_log([], 'user.logged_out');

        Auth::logout();
    
        $request->session()->invalidate();
        $request->session()->regenerateToken();
    
        return redirect('/');
    }


    public function getGenderOptions() {
        return [
            'male'   => 'Erkek',
            'female' => 'Kadın',
            'other'  => 'Belirtmek istemiyorum'
        ];
    }


    public function getFoodOptions() {
        $food = [
            'none'       => 'Yok',
            'vegetarian' => 'Vejetaryen',
            'vegan'      =>  'Vegan',
        ];

        return $food;
    }

    public function getSchoolOptions() {
        $schools = School::all();

        return $schools->pluck('title', 'id');
    }

}
