<?php

namespace App\Http\Requests;

use App\Helpers\FormTransformer;
use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Str;

class UserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name'     => 'required',
            //'username' => ['required', Rule::unique('users')->ignore(request('user'))],
            'email'    => ['required','email', Rule::unique('users')->ignore(request('user'))],
            'phone'    => ['sometimes', Rule::unique('users')->ignore(request('user')), 'nullable'],
            'password' => 'sometimes|confirmed',
            'image_url'=> 'sometimes|mimes:jpg,jpeg,png,bmp,tiff|max:4096',
            'role'     => 'sometimes|nullable',
            'type'     => 'sometimes|nullable',
            'educator_type' => 'sometimes|nullable',
            'beta' => 'sometimes|nullable',
            'super_admin' => 'sometimes|nullable',
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {

        if (request('image')) {
            $this->merge([
                'image_id' => request('image')
            ]);
        }
        /*if (request('username')) {
            $this->merge([
                'username' => Str::of(request('username'))->slug('_')
            ]);
        }*/
        if (request('phone')) {
            $this->merge([
                'phone' =>  FormTransformer::phone(request('phone'))
            ]);
        }
    }
}
