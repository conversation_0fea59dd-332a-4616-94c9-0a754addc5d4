<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;

class VerifyCsrfToken extends Middleware
{
    /**
     * The URIs that should be excluded from CSRF verification.
     *
     * @var array<int, string>
     */
    protected $except = [
        '/kamplar/*',
        '/kariyerler/*',
        '/odeme/callback/sonuc',
        '/odeme/kalan/callback/sonuc',
        '/admin/file',
        '/feedback/*',
        '/admin/organization-chart/upload-image'
    ];
}
