<?php
namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SuperAdminMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        if (Auth::check() && Auth::user()->super_admin !== 1) {
            return redirect()->route('admin.dashboard.index')->with('error', __('Yet<PERSON>iz erişim.'));
        }

        return $next($request);
    }
}
