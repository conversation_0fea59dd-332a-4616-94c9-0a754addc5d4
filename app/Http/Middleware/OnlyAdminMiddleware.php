<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class OnlyAdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $allowed_roles = [
            'admin',
            'moderator'
        ];

        if (!$request->user() || !in_array($request->user()->role, $allowed_roles)) {
            if(Auth::check()){
                Auth::logout();
            }
            return redirect()->route('login');
        }

        return $next($request);
    }
}
