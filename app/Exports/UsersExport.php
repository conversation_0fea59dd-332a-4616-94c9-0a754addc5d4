<?php

namespace App\Exports;

use App\Models\CourseEnrolment;
use App\Models\CourseSession;
use App\Models\User;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;

class UsersExport implements FromCollection, WithHeadings, WithMapping, WithTitle
{
    public function title(): string
    {
        return 'Users'; // İlk sayfanın adı
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return User::where('company_id', auth()->user()->company_id)->get();
    }


    public function map($item): array
    {
        return [
            $item->name,
            $item->email,
            $item->phone
        ];
    }

    public function headings(): array
    {
        return [
            'Full Name',
            'Email Address',
            'Phone Number'
        ];
    }
}
