<?php

namespace App\Exports;

use App\Models\CompanyDetail;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class CompanyExport implements FromCollection, WithMapping, WithHeadings
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return CompanyDetail::all();
    }

    public function map($item): array
    {
        $packageTypes = [
            2 => 'Red - 2',
            3 => 'Blue - 3',
            4 => 'Yellow - 4',
            99999 => trans('Green - Limitsiz'),
        ];

        $userLimits = [
            15 => 'Bronze(10-15)',
            60 => 'Silver(25-60)',
            -1 => 'Gold(60+)',
        ];


        $packageTypeValue = $item->getSettingValue('package_type');
        $userLimitValue = $item->getSettingValue('user_limit');

        $packageTypeText = $packageTypes[$packageTypeValue] ?? 'Unknown Package';
        $userLimitText = $userLimits[$userLimitValue] ?? 'Unknown User Limit';

        return [
            $item->company_name,
            $item->phone_number,
            $item->contact_email,
            $packageTypeText,
            $userLimitText,
            $item->address,
        ];
    }


    public function headings(): array
    {
        return [
            'Company Name',
            'Phone number',
            'Mail Address',
            'Company Subscription',
            'User Limit',
            'Address',
        ];
    }
}
