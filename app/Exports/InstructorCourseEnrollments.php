<?php

namespace App\Exports;

use App\Models\CourseEnrolment;
use App\Models\CourseSession;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;

class InstructorCourseEnrollments  implements FromCollection, WithMapping, WithHeadings
{
    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return CourseSession::whereHas('course.educator')->get();
    }

    public function map($item): array
    {
        return [
            $item->course->educator->title,
            $item->course->title,
            $item->date.' '.$item->start_time. ' - '.$item->end_time,
        ];
    }

    public function headings(): array
    {
        return [
            'Full Name',
            'Course Name',
            'Course Date',
        ];
    }
}
