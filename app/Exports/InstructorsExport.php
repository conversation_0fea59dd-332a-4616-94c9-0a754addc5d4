<?php

namespace App\Exports;

use App\Models\CourseSession;
use App\Models\Educator;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class InstructorsExport implements FromCollection, WithMapping, WithHeadings
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return Educator::all();
    }

    public function map($item): array
    {
        return [
            $item->title,
            $item->user->email,
            $item->user->phone,
            'Genel'
        ];
    }

    public function headings(): array
    {
        return [
            'Full Name',
            'Email',
            'Phone',
            'Category'
        ];
    }
}
