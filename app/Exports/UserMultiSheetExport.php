<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class UserMultiSheetExport implements WithMultipleSheets
{
    /**
    * @return \Illuminate\Support\Collection
    */
    use Exportable;

    public function sheets(): array
    {
        return [
            'Users' => new UsersExport(),
            'Course Enrollments' => new UserCourseEnrollments(),
        ];
    }
}
