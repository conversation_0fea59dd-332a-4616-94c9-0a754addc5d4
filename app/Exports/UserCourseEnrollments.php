<?php

namespace App\Exports;

use App\Models\CourseEnrolment;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;

class UserCourseEnrollments implements FromCollection, WithHeadings, WithMapping, WithTitle
{
    public function title(): string
    {
        return 'Course Enrollments'; // İlk sayfanın adı
    }
    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return CourseEnrolment::with([
            'course' => function ($query) {
                $query->select('id', 'title'); // 'id' ve 'title' sütunlarını çek
            },
            'courseSession' => function ($query) {
                $query->select('id', 'date', 'start_time', 'end_time'); // 'id', 'date', 'start_time', 'end_time' sütunlarını çek
            },
            'user' => function ($query) {
                $query->select('id', 'name', 'email', 'phone'); // 'id', 'name', 'email', 'phone' sütunlarını çek
            }
        ])->whereHas('user', function ($query) {
            $query->where('company_id', Auth::user()->company_id); // Kullanıcının 'company_id' değerine göre filtreleme yap
        })->get();
    }


    public function map($item): array
    {
        return [
            $item->user->name,
            $item->user->email,
            $item->user->phone,
            $item->course->title,
            $item->courseSession->date.' '.$item->courseSession->start_time. ' - '.$item->courseSession->end_time,
        ];
    }

    public function headings(): array
    {
        return [
            'Full Name',
            'Email Address',
            'Phone Number',
            'Course Name',
            'Course Date',
        ];
    }
}
