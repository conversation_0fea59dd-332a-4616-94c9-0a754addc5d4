<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class InstructorMultiSheetExport implements WithMultipleSheets
{
    /**
    * @return \Illuminate\Support\Collection
    */
    use Exportable;

    public function sheets(): array
    {
        return [
            'Instructors' => new InstructorsExport(),
            'Course Enrollments' => new InstructorCourseEnrollments(),
        ];
    }
}
