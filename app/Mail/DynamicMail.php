<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;

class DynamicMail extends Mailable
{
    use Queueable, SerializesModels;

    public $request;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($request)
    {
        $this->request = $request;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        if(isset($this->request['html'])){
            return $this->subject(Arr::get($this->request, 'subject'))
            ->html(Arr::get($this->request, 'content'));
        }
        
        return $this->subject(Arr::get($this->request, 'subject'))
        ->text('mails.dynamic', [
            'content' => Arr::get($this->request, 'content'),
            'request' => $this->request
        ]);
    }
}
