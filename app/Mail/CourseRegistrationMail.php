<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class CourseRegistrationMail extends Mailable
{
    use Queueable, SerializesModels;

    public $participant;
    public $course;
    public $date;
    public $time;
    public $zoom_link;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($participant, $course, $date, $time, $zoom_link)
    {
        $this->participant = $participant;
        $this->course = $course;
        $this->date = $date;
        $this->time = $time;
        $this->zoom_link = $zoom_link;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->subject($this->course . ' Eğitimi İçin Kaydınız Alındı!')
            ->view('mails.course_registration');
    }
}
