<?php

use App\Jobs\SendSMS;
use App\Mail\DynamicMail;
use App\Models\MailTemplate;
use App\Models\SmsTemplate;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Mail;

if (!function_exists('tr_lettermap')) {
    /**
     * Get the list of Turkish specific letters' uppercase and lowercase counterparts
     *
     * @return array
     */
    function tr_lettermap() {
        return [
            'upper' => ['Ç','Ğ','I','İ','Ö','Ş','Ü'],
            'lower' => ['ç','ğ','ı','i','ö','ş','ü']
        ];
    }
}

if (!function_exists('tr_strtolower')) {
    /**
     * Make a string lowercase with Turkish support
     *
     * @param string $string The input string
     * @return string
     */
    function tr_strtolower($string) {

        $map = tr_lettermap();

        $string = str_replace(
            $map['upper'],
            $map['lower'],
            $string
        );

        return mb_strtolower($string, 'UTF-8');
    }
}

if (!function_exists('tr_strtoupper')) {
    /**
     * Make a string uppercase with Turkish support
     *
     * @param string $string The input string
     * @return string
     */
    function tr_strtoupper($string) {

        $map = tr_lettermap();

        $string = str_replace(
            $map['lower'],
            $map['upper'],
            $string
        );

        return mb_strtoupper($string, 'UTF-8');
    }
}

if (!function_exists('tr_ucfirst')) {
    /**
     * Make a string's first character uppercase with Turkish support
     *
     * @param string $string The input string
     * @param boolean $force Whether to force the string to lowercase before transforming
     * @return string
     */
    function tr_ucfirst($string, $force = false) {

        $length = mb_strlen($string);
        $firstChar = mb_substr($string, 0, 1);
        $hasParenthesis = $length >= 2 && ($firstChar == '(' || $firstChar == '[');
        $needLeadingSpace = $hasParenthesis && mb_strpos($string, " ") === 1;

        $initialIndex = 0;

        if ($hasParenthesis) { $initialIndex++; }
        if ($needLeadingSpace) { $initialIndex++; }

        $initial = mb_substr($string, $initialIndex, 1);
        $rest = mb_substr($string, $initialIndex + 1);

        $prefix = ($hasParenthesis ? $firstChar : '') . ($needLeadingSpace ? ' ' : '');

        return $prefix . tr_strtoupper($initial) . ($force ? tr_strtolower($rest) : $rest);
    }
}

if (!function_exists('tr_ucwords')) {
    /**
     * Uppercase the first character of each word in a string with Turkish support
     *
     * @param string $string The input string
     * @param boolean $force Whether to force every word to lowercase before transforming
     * @return string
     */
    function tr_ucwords($string, $force = false) {

        if ($force) { $string = tr_strtolower($string, true); }

        $parts = array_map('tr_ucfirst', explode(' ', $string));

        return implode(' ', $parts);
    }
}

if (!function_exists('tr_strtotime')) {
    /**
     * Try to convert giving date string into datetime string
     *
     * @param string $date_string
     * @return bool|string
     */
    function tr_strtotime($date_string) {

        // Remove accents
        $date_string = transliterator_transliterate('Any-Latin; Latin-ASCII; Lower()', $date_string);

        $month_map = [
            'ocak'    => 'january',
            'oca'     => 'january',
            'subat'   => 'february',
            'sub'     => 'february',
            'mart'    => 'march',
            'mar'     => 'march',
            'nisan'   => 'april',
            'nis'     => 'april',
            'mayis'   => 'may',
            'haziran' => 'june',
            'haz' 	  => 'june',
            'temmuz'  => 'july',
            'tem'     => 'july',
            'agustos' => 'august',
            'agu'     => 'august',
            'eylul'   => 'september',
            'eyl'     => 'september',
            'ekim'    => 'october',
            'eki'     => 'october',
            'kasim'   => 'november',
            'kas'     => 'november',
            'aralik'  => 'december',
            'ara'     => 'december'
        ];

        $date_string = str_replace(array_keys($month_map), array_values($month_map), $date_string);

        $time = strtotime($date_string);

        if (!$time) {
            return false;
        }

        return date('Y-m-d H:i:s', $time);
    }

}


if (!function_exists('format_money')) {

    function format_money($value, $default = null, $dontSayFree = false) {
        if (is_null($value)) {
            if (is_null($default)) {
                return null;
            } else {
                $value = $default;
            }
        }

        $value = (float) $value;

        $suffix = 'TL';

        if ($value <= 0 && !$dontSayFree) {
            return "Ücretsiz";
        }

        return number_format($value, 2, ',', '.') . ($suffix ? (' ' . $suffix) : '');
    }

}

if (!function_exists('format_as_currency')) {

    function format_as_currency($value, $default = null) {
        if (is_null($value)) {
            if (is_null($default)) {
                return null;
            } else {
                $value = $default;
            }
        }

        $value = (float) $value;

        return number_format($value, 2, ',', '.');
    }

}

if (!function_exists('can_receive_sms')) {

    /**
     * Determine if phone number can receive SMS
     *
     * @param string $phone
     * @return boolean
     */
    function can_receive_sms($phone) {
        if (is_null($phone) || empty($phone)) {
            return false;
        }

        $utility = \libphonenumber\PhoneNumberUtil::getInstance();

        try {
            $phone_object = $utility->parse($phone, 'TR');

            return $utility->isValidNumberForRegion($phone_object, 'TR');
        } catch (\libphonenumber\NumberParseException $e) {
            return false;
        }

        return false;
    }

}

if (!function_exists('parse_phone')) {

    function parse_phone($phone) {

        $phone = preg_replace('/[^0-9\+]/', '', $phone);

        if (is_null($phone) || empty($phone)) {
            return null;
        }

        $utility = \libphonenumber\PhoneNumberUtil::getInstance();

        try {
            $phone_object = $utility->parse($phone, 'TR');

            return $utility->format($phone_object, \libphonenumber\PhoneNumberFormat::E164);
        } catch (\libphonenumber\NumberParseException $e) {
            return $phone;
        }

        return $phone;
    }

}

if (!function_exists('replace_mail_template')) {
    /**
     * Verilen verilere göre Mail şablonundaki {anahtar} karakterlerini değerleriyle değiştirir
     *
     * @param  string $type Mail şablonu tipi
     * @param  [type] $data
     *
     * @return array|null
     */
    function replace_mail_template($type, $data) {

        $template = MailTemplate::where('type', $type)->first();

        if (!$template) {
            return null;
        }

        $subject = $template->subject;
        $content = $template->content;
        $title = $template->title;

        foreach ($data as $key => $value) {
            $subject = str_replace('{' . $key . '}', $value, $subject);
            $content = str_replace('{' . $key . '}', $value, $content);
            $title = str_replace('{' . $key . '}', $value, $title);
        }

        $returnData = [
            'subject' => $subject,
            'content' => $content,
            'title' => $title
        ];

        if(isset($data['html'])){
            $returnData['html'] = true;
        }

        return $returnData;

    }

}

if (!function_exists('replace_sms_template')) {
    /**
     * Verilen verilere göre SMS şablonundaki {anahtar} karakterlerini değerleriyle değiştirir
     *
     * @param  string $type SMS şablonu tipi
     * @param  array $data anahtar => değer dizisi
     *
     * @return string|null
     */
    function replace_sms_template($type, $data) {

        $template = SmsTemplate::where('type', $type)->first();

        if (!$template) {
            return null;
        }

        $content = $template->content;

        foreach ($data as $key => $value) {
            $content = str_replace('{' . $key . '}', $value, $content);
        }

        return $content;

    }

}

if (!function_exists('send_mail_to')) {
    /**
     * Taslak üzerinden mail gönder
     *
     * @param  string $to Alıcı e-posta adresi
     * @param  string $type Taslak türü (adı)
     * @param  array $data Taslakta değişecek veriler
     *
     * @return void
     */
    function send_mail_to($to, $type, $data = [], $run = true) {
        if (!$run) {
            return;
        }
        $mail = replace_mail_template($type, $data);
        Mail::to($to)->send(new DynamicMail($mail));
    }

}

if (!function_exists('send_sms_to')) {
    /**
     * Taslak üzerinden SMS gönder
     *
     * @param  string $to Alıcı telefon numarası
     * @param  string $type Taslak türü (adı)
     * @param  array $data Taslakta değişecek veriler
     *
     * @return void
     */
    function send_sms_to($to, $type, $data = [], $run = true) {
        if (!$run) {
            return;
        }
        // Eğer telefon numarası SMS alabilecek şekildeyse (TR formatında) SMS gönder
        if (can_receive_sms($to)) {
            $message = replace_sms_template($type, $data);
            if (!is_null($message) && !empty($message)) {
                SendSMS::dispatchSync($to, $message);
            }
        }
    }

}

if (!function_exists('try_to_decimal')) {
    /**
     * Türk Lirası formatını [1.234,56 (bin iki yüz otuz dört lira elli altı kuruş)] decimal sayıya çevir (1234.56)
     *
     * @param  string $value
     *
     * @return float
     */
    function try_to_decimal($value) {
        $value = str_replace(",", ".", preg_replace('/[^0-9\.\,]/', '', $value));

        $last_decimal_pos = strrpos($value, '.');

        $left  = substr($value, 0, $last_decimal_pos);
        $right = substr($value, $last_decimal_pos, strlen($value) - $last_decimal_pos);

        $left = str_replace('.', '', $left);

        return floatval(($left ?? '') . ($right ?? ''));
    }
}

if (!function_exists('getPosCredentials')) {
    function getPosCredentials($onlyGUID = false) {

        $isTest = getenv('TEST_MODE');

        if (strtolower($isTest) == 'false' || $isTest == '') {
            $isTest = false;
        } else {
            $isTest = true;
        }

        if(!$isTest) {

            if ($onlyGUID) {
                return getenv('PARAM_GUID');
            }

            return [
                getenv('PARAM_CLIENT_CODE'),
                getenv('PARAM_CLIENT_USERNAME'),
                getenv('PARAM_CLIENT_PASSWORD'),
                getenv('PARAM_GUID'),
                'PROD'
            ];
        }

        $testEnv = [
            '10738',
            'Test',
            'Test',
            '0c13d406-873b-403b-9c09-a5766840d98c',
            'TEST'
        ];

        if ($onlyGUID) {
            return $testEnv[3];
        }

        return $testEnv;

    }
}

if (!function_exists('getInstallments')) {
    function getInstallments($posId = false, $price = 0) {

        $taksitReq = new param\GetInstallmentPlanForUser(...getPosCredentials());
        $taksitReq->send();
        $taksitObj = $taksitReq->parse();

        if (!$posId) {
            return $taksitObj;
        }

        $taksit = false;

        // Taksit seçeneği var mı diye kontrol et
        if (is_array($taksitObj)) {
            // Kartın POS_ID'sine göre taksit seçeneğini filtrele
            $taksitObj = array_filter($taksitObj, function($banka) use ($posId) {
                return $banka[0]['SanalPOS_ID'] == $posId;
            });



            // Eğer uygun taksit seçeneğine sahip sonuç bulunursa sonucu parsela
            if (count($taksitObj) == 1) {
                $taksitResult = array_pop($taksitObj);
                $taksitResult = $taksitResult[0];

                $taksit = [
                    'posId' => $taksitResult['SanalPOS_ID'],
                    'bankName' => $taksitResult['Kredi_Karti_Banka'],
                    'bankImage' => $taksitResult['Kredi_Karti_Banka_Gorsel'],
                    'basePrice' => $price,
                    'options' => [
                        'MO_01' => $taksitResult['MO_01'] ?? false,
                        'MO_02' => $taksitResult['MO_02'] ?? false,
                        'MO_03' => $taksitResult['MO_03'] ?? false,
                        'MO_04' => $taksitResult['MO_04'] ?? false,
                        'MO_05' => $taksitResult['MO_05'] ?? false,
                        'MO_06' => $taksitResult['MO_06'] ?? false,
                        'MO_07' => $taksitResult['MO_07'] ?? false,
                        'MO_08' => $taksitResult['MO_08'] ?? false,
                        'MO_09' => $taksitResult['MO_09'] ?? false,
                        'MO_10' => $taksitResult['MO_10'] ?? false,
                        'MO_11' => $taksitResult['MO_11'] ?? false,
                        'MO_12' => $taksitResult['MO_12'] ?? false
                    ]
                ];
            }
        }

        return $taksit;

    }
}

if (!function_exists('format_bytes')) {
    /**
     * Format bytes to human readable format
     *
     * @param  int  $bytes
     * @param  boolean $si
     *
     * @return string
     */
    function format_bytes($bytes, $si = true) {
        $unit = $si ? 1000 : 1024;
        $i = floor(log($bytes) / log($unit));

        $sizes = array('B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB');

        return sprintf('%.02F', $bytes / pow($unit, $i)) * 1 . ' ' . $sizes[$i];
    }
}

if (!function_exists('generate_social_meta')) {
    function generate_social_meta($title = null, $description = null, $image = null) {

        $output = "";
        if ($title) {
            $output .= "<meta name=\"title\" content=\"${title}\">\n";
            $output .= "<meta property=\"og:title\" content=\"${title}\">\n";
            $output .= "<meta property=\"twitter:title\" content=\"${title}\">\n";
        }

        if ($description) {
            $output .= "<meta name=\"description\" content=\"${description}\">\n";
            $output .= "<meta property=\"og:description\" content=\"${description}\">\n";
            $output .= "<meta property=\"twitter:description\" content=\"${description}\">\n";
        }

        if ($image) {
            $output .= "<meta property=\"twitter:card\" content=\"summary_large_image\">\n";
            $output .= "<meta property=\"og:image\" content=\"${image}\">\n";
            $output .= "<meta property=\"twitter:image\" content=\"{$image}\">\n";
        }

        return $output;
    }

}

if (!function_exists('route_locale')) {
    function route_locale($route, $opts = [], $locale = null) {
        if (is_null($locale)) {
            $locale = app()->getLocale();
        }

        return route("home." . $locale . "." . $route, $opts);
    }
}

if (!function_exists('date_range')) {
    function date_range($start_date, $end_date) {
        $start = Carbon::parse($start_date);
        $end = Carbon::parse($end_date);

        if ($start->isSameDay($end)) {
            // İki gün de aynıysa
            return $start->isoFormat('DD MMMM YYYY');
        }

        if ($start->isSameMonth($end, true)) {

            $startOfMonth = $start->copy()->startOfMonth();
            $endOfMonth   = $start->copy()->endOfMonth();

            if ($start->isSameDay($startOfMonth) && $end->isSameDay($endOfMonth)) {
                // Ayın başlangıç ve bitişiyse
                return $start->isoFormat('MMMM YYYY');
            }

            // Aynı yıl ve aynı ay ise
            return $start->isoFormat('DD') . ' - ' . $end->isoFormat('DD MMMM YYYY');
        }

        if ($start->isSameYear($end)) {
            // Aynı yıl ise
            return $start->isoFormat('DD MMMM') . ' - ' . $end->isoFormat('DD MMMM YYYY');
        }

        return $start->isoFormat('DD MMMM YYYY') . ' - ' . $end->isoFormat('DD MMMM YYYY');
    }
}

if (!function_exists('sloppy_date_range_parse')) {
    function sloppy_date_range_parse($date) {
        if (preg_match('/^(\d+)\s*[\-\–]\s*(\d+)\s(\w+)\s(\d+)/u', $date, $ddmy)) {
            $start_date = tr_strtotime($ddmy[1] . "-" . $ddmy[3] . "-" . $ddmy[4]);
            $end_date = tr_strtotime($ddmy[2] . "-" . $ddmy[3] . "-" . $ddmy[4]. " 23:59:59");
            return [
                'start' => $start_date,
                'end'	=> $end_date,
            ];
        } else if (preg_match('/^(\d+)\s*(\w+)\s*[\-\–]\s*(\d+)\s*(\w+)\s(\d+)/u', $date, $dmdmy)) {
            $start_date = tr_strtotime($dmdmy[1] . "-" . $dmdmy[2] . "-" . $dmdmy[5]);
            $end_date = tr_strtotime($dmdmy[3] . "-" . $dmdmy[4] . "-" . $dmdmy[5] . " 23:59:59");
            return [
                'start' => $start_date,
                'end'	=> $end_date,
            ];
        } else if (preg_match('/^(\w+)\s(\d+)/u', $date, $my)) {
            $start_date = tr_strtotime("01-" . $my[1] . "-" . $my[2]);
            $end_date = date("Y-m-t", strtotime($start_date)) . " 23:59:59";

            return [
                'start' => $start_date,
                'end'	=> $end_date,
            ];
        } else if (preg_match('/^(\d+)\s(\w+)\s(\d+)/u', $date, $dmy)) {
            $start_date = tr_strtotime( $dmy[1] . "-" . $dmy[2] . "-" . $dmy[3]);
            $end_date = tr_strtotime( $dmy[1] . "-" . $dmy[2] . "-" . $dmy[3] . "23:59:59");
            return [
                'start' => $start_date,
                'end'	=> $end_date,
            ];
        }
        return null;
    }
}

if (!function_exists('transformCustomAnswers')) {
    function transformCustomAnswers($camp, $answers = []) {

        $custom_answers = [];

        for ($i = 0; $i < count($camp->custom_fields); $i++) {

            $type = Arr::get($camp->custom_fields[$i], 'type');

            $options = Arr::get($camp->custom_fields[$i], 'options');

            $answer = [
                'question' => $camp->custom_fields[$i]['question'],
                'answer'   => getCustomAnswer($camp->custom_fields[$i], Arr::get($answers, $camp->custom_fields[$i]['id'])),
                'type'     => $type,
                'options'  => $options ? json_encode($options) : null
            ];


            if ($type == 'checkbox') {
                $answer['selected_options'] = implode(',', Arr::get($answers, $camp->custom_fields[$i]['id']));
            } else if ($type == 'option' || $type == 'dropdown') {
                $answer['selected_options'] = Arr::get($answers, $camp->custom_fields[$i]['id']);
            }


            $custom_answers[] = $answer;

        }

        return $custom_answers;
    }
}

if (!function_exists('getCustomAnswer')) {
    function getCustomAnswer($field, $answer) {

        switch (Arr::get($field, 'type')) {
            case 'checkbox':
                $answers = [];
                foreach ($answer as $ans) {
                    $answers[] = Arr::get($field, 'options.'.$ans);
                }
                return implode('; ', $answers);
            case 'radio':
            case 'option':
            case 'select':
            case 'dropdown':
                if (is_null(Arr::get($field, 'selected_options'))) {
                    return null;
                }
                return Arr::get(Arr::get($field, 'options'), $answer);
            default:
                return $answer;
        }
    }

}

if (!function_exists('craft_http_error')) {

    function craft_http_error($response) {

        $request = $response->transferStats->getRequest();
        $uri = $request->getUri()->__toString();
        $method = $request->getMethod();
        $payload = $request->getBody()->__toString();
        $statusCode = $response->getStatusCode();
        $reasonPhrase = $response->getReasonPhrase();
        $body = json_decode($payload, true) ?? $payload;

        return [
            'response' => [
                'code' => $statusCode,
                'reason' => $reasonPhrase,
                'content-type' => $response->getHeaderLine('Content-Type'),
                'body' => $response->json() ?? $response->body(),
            ],
            'request' => [
                'uri' => $uri,
                'method' => $method,
                'content-type' => $request->getHeaderLine('Content-Type'),
                'body' => $body,
                'ip' => request()->ip(),
                'date' => date('Y-m-d H:i:s')
            ]
        ];

    }

}

if (!function_exists('fullUrlWithQuery')) {

     function fullUrlWithQuery($params)
    {
        return request()->fullUrlWithQuery($params);
    }

}

