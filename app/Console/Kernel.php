<?php

namespace App\Console;


use App\Models\CourseSession;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Illuminate\Support\Facades\Mail;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // Bir hafta önce hatırlatma
        $schedule->call(function () {
            $this->sendReminders('-7 days');
        })->dailyAt('12:35');

        // Bir gün önce hatırlatma
        $schedule->call(function () {
            $this->sendReminders('-1 day');
        })->dailyAt('09:00');

        // Bir saat önce hatırlatma
        $schedule->call(function () {
            $this->sendReminders('-1 hour');
        })->hourly();
    }

    public function sendReminders($timeframe)
    {
        // Farklı zaman aralıklarına göre doğru metodları kullanarak tarih ekleyin
        switch ($timeframe) {
            case '-7 days':
                $targetDate = now()->subDays(7); // Bir hafta öncesi
                $courses = CourseSession::whereDate('date', '=', $targetDate->toDateString())
                    ->get();
                break;
            case '-1 day':
                $targetDate = now()->subDay(); // Bir gün öncesi
                $courses = CourseSession::whereDate('date', '=', $targetDate->toDateString())
                    ->get();
                break;
            case '-1 hour':
                $targetDate = now()->subHour(); // Bir saat öncesi
                $courses = CourseSession::whereDate('date', '=', $targetDate->toDateString())
                    ->whereTime('start_time', '>=', $targetDate->toTimeString())
                    ->get();
                break;
            default:
                $targetDate = now();
                $courses = [];
                break;
        }

        // Katılımcılar için hatırlatma e-postaları gönder
        foreach ($courses as $course) {
            foreach ($course->users as $participant) {
                if ($timeframe == '-7 days') {
                    $this->sendFirstReminder($participant, $course->course); // Bir hafta önceki hatırlatma
                } elseif ($timeframe == '-1 day') {
                    $this->sendSecondReminder($participant, $course->course); // Bir gün önceki hatırlatma
                } elseif ($timeframe == '-1 hour') {
                    $this->sendFinalReminder($participant, $course->course); // Bir saat önceki hatırlatma
                }
            }
        }
    }

    public function sendFirstReminder($participant, $course)
    {
        Mail::send('mails.reminder_first', [
            'participant' => $participant,
            'course' => $course
        ], function ($message) use ($participant, $course) {
            $message->to($participant->email);
            $message->subject($course->title." İçin Hatırlatma: Eğitim Tarihi ve Saati");
        });
    }

    public function sendSecondReminder($participant, $course)
    {
        Mail::send('mails.reminder_second', [
            'participant' => $participant,
            'course' => $course
        ], function ($message) use ($participant, $course) {
            $message->to($participant->email);
            $message->subject("Yarınki ".$course->title." İçin Son Hatırlatma");
        });
    }

    public function sendFinalReminder($participant, $course)
    {
        Mail::send('mails.reminder_final', [
            'participant' => $participant,
            'course' => $course
        ], function ($message) use ($participant, $course) {
            $message->to($participant->email);
            $message->subject($course->title." Eğitimi İçin Son Hazırlıklar");
        });
    }
    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
