<?php

use App\Http\Controllers\CourseController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\EducatorController;
use App\Http\Controllers\PageController;
use App\Http\Controllers\MailTemplateController;
use App\Http\Controllers\SmsTemplateController;

use App\Http\Controllers\AuthFormController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\ProfileController;

use App\Http\Controllers\CategoryController;
use App\Http\Controllers\DemoController;
use App\Http\Controllers\FeedbackController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\UploadController;
use App\Http\Controllers\LanguageController;



/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('logout', [AuthFormController::class, 'logout'])->name('logout');
Route::get('login', [AuthFormController::class, 'showLoginForm'])->name('login');
Route::post('login', [AuthFormController::class, 'tryToLogin']);
Route::get('kayit', [AuthFormController::class, 'showRegisterForm'])->name('register');
Route::post('kayit', [AuthFormController::class, 'register']);

Route::prefix('admin')->name('admin.')->middleware(['auth', 'only.admin'])->group(function () {

    Route::get('/', [DashboardController::class, 'index'])->name('dashboard.index');

    ## Super Admin Firma Değiştirme ##
    Route::get('company/change',  function() {
        if(auth()->user()->super_admin == 1) {
            $company_id = request()->input('company_id');

            if($company_id) {
                if($company_id === 'master_data'){
                    session()->put('master_data', true);
                    return redirect()->back();
                }else {
                    $company = \App\Models\CompanyDetail::where('company_id', $company_id)->first();
                    if ($company) {
                        auth()->user()->company_id = $company_id;
                        auth()->user()->save();
                        session()->forget('master_data');
                        return redirect('/admin/company/detail');   
                    }
                }
            }
        }

        return redirect()->back();
    });

    Route::resource('course', CourseController::class);
    Route::get('course/enrolment', [CourseController::class,'show'])->name('course.enrolment');
    Route::get('course/enrolment/{id}/{session_id}', [CourseController::class,'enrolmentDetail'])->name('course.enrolment.detail');
    Route::post('course/enrolment/{id}/{session_id}/save', [CourseController::class,'enrolmentSave'])->name('course.enrolment.save');
    Route::delete('course/enrolment/{course_id}/{session_id}/{user_id}/user/delete', [CourseController::class,'enrolmentUserDelete'])->name('course.enrolment.user.delete');
    Route::post('course/enrolled/{user_id}/{course_id}/{session_id}', [CourseController::class,'userEnrolledAjax'])->name('course.enrolled.user');

    //Route::get('banner', [DashboardController::class, 'banner'])->name('banner.index');
    //Route::post('banner', [DashboardController::class, 'bannerPost'])->name('banner.post');
    //Route::get('banner/{id}/edit', [DashboardController::class, 'bannerEdit'])->name('banner.edit');
    //Route::put('banner/{id}', [DashboardController::class, 'bannerUpdate'])->name('banner.update');
    Route::get('educator/export', [EducatorController::class, 'export'])->name('educator.export');
    Route::resource('educator', EducatorController::class)->middleware('super_admin');

    Route::resource('feedback', FeedbackController::class)->only(['index', 'show', 'edit', ]);
    Route::resource('category', CategoryController::class)->middleware('super_admin');

    Route::resource('page', PageController::class);
    Route::resource('mailtemplate', MailTemplateController::class);
    Route::resource('smstemplate', SmsTemplateController::class);
    Route::resource('demo', DemoController::class)->only(['index', 'show', 'update', 'edit'])->middleware('super_admin');

    Route::post('/file', [UploadController::class, 'upload']);
    Route::delete('/file', [UploadController::class, 'destroy']);

    Route::get('/api/educators', [EducatorController::class, 'apiList']);

    Route::resource('allusers', UserController::class);
    Route::get('profile', [ProfileController::class, 'index'])->name('profile.index');
    Route::put('profile', [ProfileController::class, 'update']);

    Route::get('administrators', [UserController::class, 'administrators'])->name('administrators.index')->middleware('super_admin');;

    Route::resource('user', UserController::class);
    Route::get('users', [UserController::class, 'users'])->name('users.index');
    Route::get('users/export', [UserController::class, 'usersExport'])->name('users.export');
    Route::post('users/bulk-import', [UserController::class, 'bulkImport'])->name('users.bulk_import');

    Route::get('educators', [EducatorController::class, 'educators'])->name('educators.index')->middleware('super_admin');
    Route::get('educators/export', [EducatorController::class, 'instructorsExport'])->name('instructors.export')->middleware('super_admin');

    Route::get('/company/create', [DashboardController::class, 'companyCreate'])->name('company.create');
    Route::post('/company/create/post', [DashboardController::class, 'companyCreatePost'])->name('company.create.post');
    Route::get('/company/detail', [DashboardController::class, 'companyDetail'])->name('company.detail');
    Route::get('/company/edit/{company_id?}', [DashboardController::class, 'companyEdit'])->name('company.edit');
    Route::put('/company/update/{company_id?}', [DashboardController::class, 'companyUpdate'])->name('company.update');
    Route::get('/company/subscription/', [DashboardController::class, 'companySubscription'])->name('company.subscription');
    Route::get('company/payment', [DashboardController::class, 'companyPayment'])->name('company.payment');
    Route::delete('company/delete/{company_id}', [DashboardController::class, 'companyDestroy'])->name('company.destroy');
    Route::get('company/export', [DashboardController::class, 'companyExport'])->name('company.export');

    Route::get('schedule/control', [DashboardController::class, 'scheduleControl'])->name('schedule.control');

});

Route::get('test', function() {
    return view('test');
});

Route::get('/language/{lang}', [LanguageController::class, 'changeLanguage'])->name('language.change');

Route::get('admin/course/{id}/copy', 'App\Http\Controllers\CourseController@copy')->name('admin.course.copy');

