<?php

use App\Http\Controllers\ApiController;
use App\Http\Controllers\UserController;
use App\Models\Camp;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

Route::get('/run-reminder/17e7d6a14bc0c67ae2a56a8f01863619', [ApiController::class, 'reminderMail']);
Route::get('/run-reminder/one-hour/17e7d6a14bc0c67ae2a56a8f01863619', [ApiController::class, 'reminderOneHourMail']);


Route::get('/kamplar/ay', function(){

    $month = str_pad(request('m'), 2, "0", STR_PAD_LEFT) ?? date('m');
    $year = request('y') ?? date('Y');

    if( !is_numeric($month) || !is_numeric($year) ) {
        return [];
    }

    $previousMonth = str_pad($month == 1 ? 12 : $month - 1, 2, "0", STR_PAD_LEFT);
    $previousYear = $month == 1 ? $year - 1 : $year;

    $nextMonth = str_pad($month == 12 ? 1 : $month + 1, 2, "0", STR_PAD_LEFT);
    $nextYear = $month == 12 ? $year + 1 : $year;

    $camps = Camp::whereBetween('start_date', [
        "{$year}-{$month}-01",
        "{$year}-{$month}-31"
    ])->get();

    $known = [];

    foreach($camps as $camp) {
        $known[] = [
            'id'         => $camp->id,
            'title'      => $camp->title,
            'slug'       => $camp->slug,
            'cover'      => $camp->cover_image_url ?? $camp->header_image_url ?? null,
            'start_date' => $camp->start_date,
            'end_date'   => $camp->end_date,
            'categories' => $camp->categories()->get(['title', 'slug', 'background', 'color'])->makeHidden('pivot') ?? [],
            'location'   => Arr::get($camp->location, 'title'),
            'date'       => date_range($camp->start_date, $camp->end_date),
        ];
    }


    return [
        'known' => $known,
        'unknown' => []
    ];

});
Route::post('/handle-password-change', [UserController::class, 'handlePasswordChange'])->name('handle.sms');
