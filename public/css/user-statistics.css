/* Fonts Colors */
:root {
    --grey: #eaeaea;
    --darkgrey: #c4c4c4;
    --darkergrey: #4f4f4f;
    --dark: #0a0a1f;
    --darken: #292d32;
    --darker: #231f20;
    --darkeer: #2c2c2c;
    --purple: #8082b8;
    --red: #f94144;
    --orange: #f3722c;
    --yellow: #f9c74f;
    --pink: #6198a2;
    --lightgreen: #90be6d;
    --green: #f8961e;
    --darkgreen: #4d7a90;
}
/* Fonts Colors */

.green-dot {
    width: 16px !important;
    height: 16px !important;
    background-color: var(--green);
    border-radius: 50%;
}

.purple-dot {
    width: 16px;
    height: 16px;
    background-color: var(--purple);
    border-radius: 50%;
}

/************************ Media Queries **********************/

/* Fold Mobil Devices */

@media (max-width: 350px) {
    /* Search Section */

    .search-container img {
        display: none;
    }

    .search-container {
        width: auto !important;
    }

    .profile-section {
        justify-content: space-between !important;
    }

    .search-container input[type="text"] {
        width: 0 !important;
    }
    .profile-pic {
        margin-right: 0 !important;
        height: 25px !important;
        width: 25px !important;
    }
    .search-icon {
        margin-left: 0 !important;
        font-size: 16px !important;
    }
    .icons {
        display: flex;
    }
    .icons .iconsax {
        font-size: 16px !important;
    }

    /* Search Section */

    /* User Info Section */

    .user-info-section {
        margin-top: 2rem !important;
    }

    .user-info-container {
        flex-direction: column !important;
    }

    .user-infos {
        margin-top: 1rem !important;
        margin-left: 1rem !important;
    }

    .user-infos .titles span:last-child {
        margin-top: 1.65rem !important;
    }

    .user-infos .titles span,
    .user-infos .info span {
        font-size: 15px !important;
    }

    .username {
        flex-direction: column !important;
        padding-right: 0 !important;
    }

    .username div {
        display: flex !important;
        flex-direction: row !important;
        justify-content: center !important;
        align-items: center !important;
    }

    .basic-metrics {
        margin-inline: 0 !important;
        margin-top: 1rem !important;
    }

    /* User Info Section */

    /* Watch Section */
    .watch-hr {
        margin-inline: 1rem !important;
        margin-block: 2rem !important;
    }

    .watch-section {
        flex-direction: column !important;
    }

    .watch-bar-graph .play i {
        font-size: 26px !important;
    }

    .watch-bar-graph .play span {
        font-size: 20px !important;
    }

    .watch-line-graph {
        margin-top: 1rem !important;
    }

    .line-graph-header {
        flex-direction: column !important;
        margin-inline: 0 !important;
        align-items: center !important;
    }

    .line-graph-header .total-watch {
        font-size: 18px !important;
        margin-bottom: 0.5rem;
    }

    .brand-hr2 {
        margin: 1rem 1.5rem 0 1.5rem !important;
    }

    .line-graph-brand-header {
        margin-inline: 2rem !important;
    }

    .brand-bar {
        margin-inline: 2rem !important;
    }
    .brand-bar .brand-time .big-sp {
        font-size: 24px !important;
    }

    .brand-bar .brand-time .small-sp {
        font-size: 14px !important;
    }

    .brand-bar .flex-column span {
        font-size: 14px !important;
    }

    .brand-bar .flex-column {
        width: 70% !important;
    }

    /* Watch Section */

    /* Video Section */
    .video-section {
        margin-top: 2rem;
        flex-direction: column !important;
    }


    .most-watched-header {
        flex-direction: column !important;
    }

    .custom-video-table {
        flex-direction: column !important;
        overflow-y: hidden;
        overflow-x: auto;
    }

    .most-watched-header div span {
        font-size: 16px !important;
    }

    .most-watched,
    .most-watched2 {
        justify-content: center !important;
        margin-block: 0.5rem;
    }



    .last-video-container {
        margin-top: 1rem;
    }

    .video-container {
        padding-top: 2rem !important;
        height: 25dvh !important;
    }

    .video-container i {
        font-size: 30px;
        margin-bottom: 1rem !important;
    }

    .last-video-title i {
        font-size: 20px !important;
    }

    .video-texts span:nth-child(1) {
        font-size: 16px !important;
    }

    .last-video-title span {
        font-size: 16px !important;
    }

    /* Video Section */

    /* Time Spent Section */
    .time-spent-section {
        flex-direction: column !important;
    }

    .time-spent-header {
        flex-direction: column !important;
        align-items: center !important;
    }

    .time-spent-header .dropdown {
        margin-top: 0.5rem;
    }

    .pie-chart-container {
        flex-direction: column !important;
    }

    .pie-settings2 {
        height: 14rem !important;
    }

    .app-web-container {
        margin-top: 1rem !important;
    }

    .web-container div i,
    .app-container div i {
        font-size: 25px !important;
    }

    .web-container div .span-hour,
    .app-container div .span-hour {
        font-size: 24px !important;
    }

    .web-container div .hour-text,
    .app-container div .hour-text {
        font-size: 20px !important;
    }

    .web-container div .span-minute,
    .app-container div .span-minute {
        font-size: 40px !important;
    }
    /* Time Spent Section */

    /* Webinar Section */
    .webinar-section {
        margin-top: 1rem;
        flex-direction: column !important;
    }



    .webinar-section .webinar-table {
        overflow-y: hidden;
        overflow-x: auto;
    }

    .webinar-section .all-webinars {
        padding: 1rem !important;
    }

    .webinar-section .all-webinars .table-dropdown {
        display: inline !important;
        text-align: center;
    }

    .webinar-section .all-webinars .table-dropdown .btn {
        padding: 0 !important;
    }

    .webinar-section .all-webinars .peg-buttons {
        margin-top: 0.5rem;
    }

    .webinars-attend {
        margin-top: 1rem !important;
        margin-left:0!important;
    }

    .webinars-attend .attend {
        margin-inline: 0.5rem !important;

    }

    .webinars-attend .attend div span, .all-webinars-header span {
        font-size: 18px !important;
    }

    .webinars-attend .attend div .fa-clipboard, .all-webinars-header i {
        font-size: 20px !important;
    }

    .webinars-attend .attend div .fa-ellipsis-vertical {
        font-size: 16px !important;
    }

    .webinars-attend .total .total-webinars {
        font-size: 20px !important;
    }

    /* Webinar Section */
}

/* Fold Mobil Devices */

/* Mobil devices */

@media (min-width: 350.01px) and (max-width: 575.98px) {
    /* Search Section */

    .search-container img {
        display: none;
    }

    .search-container {
        width: auto !important;
    }

    .profile-section {
        justify-content: space-between !important;
    }

    .search-container input[type="text"] {
        width: 0 !important;
    }
    .profile-pic {
        margin-right: 0 !important;
    }
    .icons {
        display: flex;
    }

    /* Search Section */

    /* User Info Section */

    .user-info-section {
        margin-top: 2rem !important;
    }

    .user-info-container {
        flex-direction: column !important;
    }

    .user-infos {
        margin-top: 1rem !important;
        margin-left: 1rem !important;
    }

    .user-infos .titles span,
    .user-infos .info span {
        font-size: 15px !important;
    }

    .username {
        flex-direction: column !important;
        padding-right: 0 !important;
    }

    .username div {
        display: flex !important;
        flex-direction: row !important;
        justify-content: center !important;
        align-items: center !important;
    }

    .basic-metrics {
        margin-inline: 0 !important;
        margin-top: 1rem !important;
    }

    /* User Info Section */

    /* Watch Section */
    .watch-hr {
        margin-inline: 1rem !important;
        margin-block: 2rem !important;
    }

    .watch-section {
        flex-direction: column !important;
    }



    .watch-bar-graph .play i {
        font-size: 26px !important;
    }

    .watch-bar-graph .play span {
        font-size: 20px !important;
    }

    .watch-line-graph {

        margin-top: 1rem !important;
    }

    .line-graph-header {
        flex-direction: column !important;
        margin-inline: 0 !important;
        align-items: center !important;
    }

    .line-graph-header .total-watch {
        font-size: 18px !important;
        margin-bottom: 0.5rem;
    }

    .brand-hr2 {
        margin: 1rem 1.5rem 0 1.5rem !important;
    }

    .line-graph-brand-header {
        margin-inline: 2rem !important;
    }

    .brand-bar {
        margin-inline: 2rem !important;
    }
    .brand-bar .brand-time .big-sp {
        font-size: 24px !important;
    }

    .brand-bar .brand-time .small-sp {
        font-size: 14px !important;
    }

    .brand-bar .flex-column span {
        font-size: 14px !important;
    }

    .brand-bar .flex-column {
        width: 70% !important;
    }

    /* Watch Section */

    /* Video Section */
    .video-section {
        margin-top: 2rem;
        flex-direction: column !important;
    }


    .most-watched-header {
        flex-direction: column !important;
    }

    .custom-video-table {
        flex-direction: column !important;
        overflow-y: hidden;
        overflow-x: auto;
    }

    .most-watched-header div span {
        font-size: 16px !important;
    }

    .most-watched,
    .most-watched2 {
        justify-content: center !important;
        margin-block: 0.5rem;
    }



    .last-video-container {
        margin-top: 1rem;
    }

    .video-container {
        padding-top: 2rem !important;
        height: 25dvh !important;
    }

    .video-container i {
        font-size: 30px;
        margin-bottom: 1rem !important;
    }

    .last-video-title i {
        font-size: 20px !important;
    }

    .video-texts span:nth-child(1) {
        font-size: 16px !important;
    }

    .last-video-title span {
        font-size: 16px !important;
    }

    /* Video Section */

    /* Time Spent Section */
    .time-spent-section {
        flex-direction: column !important;
    }


    .time-spent-header {
        flex-direction: column !important;
        align-items: center !important;
    }

    .time-spent-header .dropdown {
        margin-top: 0.5rem;
    }

    .pie-chart-container {
        flex-direction: column !important;
    }

    .pie-settings2 {
        height: 14rem !important;
    }

    .app-web-container {
        margin-top: 1rem !important;
    }

    .web-container div i,
    .app-container div i {
        font-size: 25px !important;
    }

    .web-container div .span-hour,
    .app-container div .span-hour {
        font-size: 65px !important;
    }

    .web-container div .hour-text,
    .app-container div .hour-text {
        font-size: 20px !important;
    }

    .web-container div .span-minute,
    .app-container div .span-minute {
        font-size: 40px !important;
    }
    /* Time Spent Section */

    /* Webinar Section */
    .webinar-section {
        margin-top: 1rem;
        flex-direction: column !important;
    }



    .webinar-section .webinar-table {
        overflow-y: hidden;
        overflow-x: auto;
    }

    .webinar-section .all-webinars {
        padding: 1rem !important;
    }

    .webinar-section .all-webinars .table-dropdown {
        display: inline !important;
        text-align: center;
    }

    .webinar-section .all-webinars .table-dropdown .btn {
        padding: 0 !important;
    }

    .webinar-section .all-webinars .peg-buttons {
        margin-top: 0.5rem;
    }

    .webinars-attend {
        margin-top: 1rem !important;
        margin-left:0!important;
    }

    .webinars-attend .attend {
        margin-inline: 0.5rem !important;

    }

    .webinars-attend .attend div span, .all-webinars-header span {
        font-size: 18px !important;
    }

    .webinars-attend .attend div .fa-clipboard, .all-webinars-header i {
        font-size: 20px !important;
    }

    .webinars-attend .attend div .fa-ellipsis-vertical {
        font-size: 16px !important;
    }

    .webinars-attend .total .total-webinars {
        font-size: 20px !important;
    }

    /* Webinar Section */
}

/* Mobil devices */

/* Tablet devices */
@media (min-width: 576px) and (max-width: 767.98px) {
    /* Search Section */

    .profile-section {
        justify-content: space-around !important;
    }

    .search-container input[type="text"] {
        width: 70px !important;
    }
    .icons {
        display: flex;
    }

    /* Search Section */

    /* Watch Section */
    .watch-hr {
        margin-inline: 1rem !important;
        margin-block: 2rem !important;
    }

    .watch-section {
        flex-direction: column !important;
    }


    .watch-bar-graph .play i {
        font-size: 26px !important;
    }

    .watch-bar-graph .play span {
        font-size: 20px !important;
    }

    .watch-line-graph {

        margin-top: 1rem !important;
    }

    .line-graph-header {
        flex-direction: column !important;
        margin-inline: 0 !important;
        align-items: center !important;
    }

    .line-graph-header .total-watch {
        font-size: 18px !important;
        margin-bottom: 0.5rem;
    }

    .brand-hr2 {
        margin: 1rem 1.5rem 0 1.5rem !important;
    }

    .line-graph-brand-header {
        margin-inline: 2rem !important;
    }

    .brand-bar {
        margin-inline: 2rem !important;
    }
    .brand-bar .brand-time .big-sp {
        font-size: 24px !important;
    }

    .brand-bar .brand-time .small-sp {
        font-size: 14px !important;
    }

    .brand-bar .flex-column span {
        font-size: 14px !important;
    }

    .brand-bar .flex-column {
        width: 70% !important;
    }

    /* Watch Section */

    /* Video Section */
    .video-section {
        margin-top: 2rem;
        flex-direction: column !important;
    }


    .most-watched-header {
        flex-direction: column !important;
    }

    .custom-video-table {
        flex-direction: column !important;
        overflow-y: hidden;
        overflow-x: auto;
    }

    .most-watched-header div span {
        font-size: 16px !important;
    }

    .most-watched,
    .most-watched2 {
        justify-content: center !important;
        margin-block: 0.5rem;
    }


    .last-video-container {
        margin-top: 1rem;
    }

    .video-container {
        padding-top: 2rem !important;
        height: 25dvh !important;
    }

    .video-container i {
        font-size: 30px;
        margin-bottom: 1rem !important;
    }

    .last-video-title i {
        font-size: 20px !important;
    }

    .video-texts span:nth-child(1) {
        font-size: 16px !important;
    }

    .last-video-title span {
        font-size: 16px !important;
    }

    /* Video Section */

    /* Time Spent Section */
    .time-spent-section {
        flex-direction: column !important;
    }

    .time-spent-header {
        flex-direction: column !important;
        align-items: center !important;
    }

    .time-spent-header .dropdown {
        margin-top: 0.5rem;
    }

    .pie-chart-container {
        flex-direction: column !important;
    }

    .pie-settings2 {
        height: 14rem !important;
    }

    .app-web-container {
        margin-top: 1rem !important;
    }

    .web-container div i,
    .app-container div i {
        font-size: 25px !important;
    }

    .web-container div .span-hour,
    .app-container div .span-hour {
        font-size: 24px !important;
    }

    .web-container div .hour-text,
    .app-container div .hour-text {
        font-size: 20px !important;
    }

    .web-container div .span-minute,
    .app-container div .span-minute {
        font-size: 40px !important;
    }
    /* Time Spent Section */

    /* Webinar Section */
    .webinar-section {
        margin-top: 1rem;
        flex-direction: column !important;
    }


    .webinar-section .webinar-table {
        overflow-y: hidden;
        overflow-x: auto;
    }

    .webinar-section .all-webinars {
        padding: 1rem !important;
    }

    .webinar-section .all-webinars .table-dropdown {
        display: inline !important;
        text-align: center;
    }

    .webinar-section .all-webinars .table-dropdown .btn {
        padding: 0 !important;
    }

    .webinar-section .all-webinars .peg-buttons {
        margin-top: 0.5rem;
    }

    .webinars-attend {
        margin-top: 1rem !important;
        margin-left:0!important;
    }

    .webinars-attend .attend {
        margin-inline: 0.5rem !important;

    }

    .webinars-attend .attend div span, .all-webinars-header span {
        font-size: 18px !important;
    }

    .webinars-attend .attend div .fa-clipboard, .all-webinars-header i {
        font-size: 20px !important;
    }

    .webinars-attend .attend div .fa-ellipsis-vertical {
        font-size: 16px !important;
    }

    .webinars-attend .total .total-webinars {
        font-size: 20px !important;
    }

    /* Webinar Section */
}

@media (min-width: 650px) and (max-width: 700px) {
    .profile-section {
        justify-content: space-around !important;
    }
}

@media (min-width: 700.01px) and (max-width: 767.98px) {
    .profile-section {
        justify-content: space-around !important;
    }
    .search-container {
        width: auto !important;
    }
}

/* Tablet devices */


/* Notebook devices */
@media (min-width: 768px) and (max-width: 991.98px) {
    /* Search Section */

    .search-container {
        width: auto !important;
    }

    .profile-section {
        justify-content: space-around !important;
    }

    .search-container input[type="text"] {
        width: 180px !important;
    }
    .profile-pic {
        margin-right: 10px !important;
    }
    .icons {
        display: flex;
    }

    /* Search Section */

    /* User Info Section */

    .user-info-section {
        margin-top: 2rem !important;
    }

    /* User Info Section */

    /* Watch Section */
    .watch-hr {
        margin-inline: 1rem !important;
        margin-block: 2rem !important;
    }

    .watch-section {
        justify-content: space-between !important;
    }


    .watch-bar-graph .play i {
        font-size: 26px !important;
    }

    .watch-bar-graph .play span {
        font-size: 20px !important;
    }


    .line-graph-header {
        flex-direction: column !important;
        margin-inline: 0 !important;
        align-items: center !important;
    }

    .line-graph-header .total-watch {
        font-size: 18px !important;
        margin-bottom: 0.5rem;
    }

    .brand-hr2 {
        margin: 1rem 1.5rem 0 1.5rem !important;
    }

    .line-graph-brand-header {
        margin-inline: 2rem !important;
    }

    .brand-bar {
        margin-inline: 2rem !important;
    }
    .brand-bar .brand-time .big-sp {
        font-size: 24px !important;
    }

    .brand-bar .brand-time .small-sp {
        font-size: 14px !important;
    }

    .brand-bar .flex-column span {
        font-size: 14px !important;
    }

    .brand-bar .flex-column {
        width: 70% !important;
    }

    /* Watch Section */

    /* Video Section */
    .video-section {
        margin-top: 2rem;
        flex-direction: column !important;
    }



    .most-watched-header {
        flex-direction: column !important;
    }

    .custom-video-table {
        flex-direction: column !important;
        overflow-y: hidden;
        overflow-x: auto;
    }

    .most-watched-header div span {
        font-size: 16px !important;
    }

    .most-watched,
    .most-watched2 {
        justify-content: center !important;
        margin-block: 0.5rem;
    }


    .last-video-container {
        margin-top: 1rem;
    }

    .video-container {
        padding-top: 2rem !important;
        height: 25dvh !important;
    }

    .video-container i {
        font-size: 30px;
        margin-bottom: 1rem !important;
    }

    .last-video-title i {
        font-size: 20px !important;
    }

    .video-texts span:nth-child(1) {
        font-size: 16px !important;
    }

    .last-video-title span {
        font-size: 16px !important;
    }

    /* Video Section */

    /* Time Spent Section */
    .time-spent-section {
        flex-direction: column !important;
    }


    .time-spent-header .dropdown {
        margin-top: 0.5rem;
    }

    .pie-settings2 {
        height: 11rem !important;
        margin-bottom: 1rem !important;
    }

    .app-web-container .mobile-row {
        flex-direction: column !important;
        justify-content: space-between;
    }

    .app-web-container {
        margin-top: 1rem;
    }

    .web-container {
        margin-top: 0 !important;
    }


    .web-container div i,
    .app-container div i {
        font-size: 25px !important;
    }

    .web-container div .span-hour,
    .app-container div .span-hour {
        font-size: 45px !important;
    }

    .web-container div .hour-text,
    .app-container div .hour-text {
        font-size: 20px !important;
    }

    .web-container div .span-minute,
    .app-container div .span-minute {
        font-size: 40px !important;
    }
    /* Time Spent Section */

    /* Webinar Section */
    .webinar-section {
        margin-top: 1rem;
        flex-direction: column !important;
    }


    .webinar-section .webinar-table {
        overflow-y: hidden;
        overflow-x: auto;
    }

    .webinar-section .all-webinars {
        padding: 1rem !important;
    }

    .webinar-section .all-webinars .table-dropdown {
        display: inline !important;
        text-align: center;
    }

    .webinar-section .all-webinars .table-dropdown .btn {
        padding: 0 !important;
    }

    .webinar-section .all-webinars .peg-buttons {
        margin-top: 0.5rem;
    }

    .webinars-attend {
        margin-top: 1rem !important;
        margin-left:0!important;
    }

    .webinars-attend .attend {
        margin-inline: 0.5rem !important;

    }

    .webinars-attend .attend div span, .all-webinars-header span {
        font-size: 18px !important;
    }

    .webinars-attend .attend div .fa-clipboard, .all-webinars-header i {
        font-size: 20px !important;
    }

    .webinars-attend .attend div .fa-ellipsis-vertical {
        font-size: 16px !important;
    }

    .webinars-attend .total .total-webinars {
        font-size: 20px !important;
    }

    /* Webinar Section */
}

@media (min-width: 850px) and (max-width: 991.98px) {
    .profile-section {
        justify-content: space-around !important;
    }

    .search-container {
        width: auto !important;
    }
}

@media (min-width: 890px) {
    .user-infos .titles span:last-child {
        margin-top: 0 !important;
    }
}

@media (min-width: 915px) and (max-width: 991.98px) {
    .profile-section {
        justify-content: space-around !important;
    }
    .search-container input[type="text"] {
        width: 300px !important;
    }

    .search-container {
        width: auto !important;
    }

    /* User Section */
}

/* Notebook devices */

/* Large Notebook devices */
@media (min-width: 992px) and (max-width: 1199.98px) {
    /* Search Section */
    .search-container {
        width: auto !important;
    }

    .profile-section {
        justify-content: space-around !important;
    }

    .search-container input[type="text"] {
        width: 250px !important;
    }
    .profile-pic {
        margin-right: 10px !important;
    }
    .icons {
        display: flex;
    }

    /* Search Section */

    /* User Info Section */

    .user-info-section {
        margin-top: 2rem !important;
    }

    /* User Info Section */

    /* Watch Section */
    .watch-hr {
        margin-inline: 1rem !important;
        margin-block: 2rem !important;
    }

    .watch-section {
        justify-content: space-evenly !important;
    }

    .watch-bar-graph .play i {
        font-size: 26px !important;
    }

    .watch-bar-graph .play span {
        font-size: 20px !important;
    }


    .line-graph-header {
        align-items: center !important;
    }

    .line-graph-header .total-watch {
        font-size: 18px !important;
    }

    .brand-hr2 {
        margin: 1rem 1.5rem 0 1.5rem !important;
    }

    .line-graph-brand-header {
        margin-inline: 2rem !important;
    }

    .brand-bar {
        margin-inline: 2rem !important;
    }
    .brand-bar .brand-time .big-sp {
        font-size: 24px !important;
    }

    .brand-bar .brand-time .small-sp {
        font-size: 14px !important;
    }

    .brand-bar .flex-column span {
        font-size: 14px !important;
    }

    .brand-bar .flex-column {
        width: 70% !important;
    }

    /* Watch Section */

    /* Video Section */
    .video-section {

        margin-top: 2rem;
    }


    .custom-video-table {
        overflow-y: hidden;
        overflow-x: auto;
    }

    .most-watched-header div span {
        font-size: 16px !important;
    }

    .most-watched,
    .most-watched2 {
        justify-content: center !important;
        margin-block: 0.5rem;
    }

    .video-container {
        padding-top: 2rem !important;
        height: 25dvh !important;
    }

    .video-container i {
        font-size: 26px;
        margin-bottom: 1rem !important;
    }

    .last-video-title i {
        font-size: 18px !important;
    }

    .video-texts span:nth-child(1) {
        font-size: 15px !important;
    }

    .last-video-title span {
        font-size: 15px !important;
    }

    .video-status span {
        font-size: 12px !important;
    }

    /* Video Section */

    /* Time Spent Section */

    .time-spent-header .dropdown {
        margin-top: 0.5rem;
    }

    .pie-settings2 {
        height: 11rem !important;
        margin-bottom: 1rem !important;
    }

    .app-web-container .mobile-row {
        flex-direction: column !important;
        justify-content: space-between;
    }


    .web-container {
        margin-top: 1rem !important;
    }

    .web-container div i,
    .app-container div i {
        font-size: 25px !important;
    }

    .web-container div .span-hour,
    .app-container div .span-hour {
        font-size: 45px !important;
    }

    .web-container div .hour-text,
    .app-container div .hour-text {
        font-size: 20px !important;
    }

    .web-container div .span-minute,
    .app-container div .span-minute {
        font-size: 40px !important;
    }
    /* Time Spent Section */

    /* Webinar Section */
    .webinar-section {
        margin-top: 1rem;

    }

    .webinar-section .webinar-table {
        overflow-y: hidden;
        overflow-x: auto;
    }

    .webinar-section .all-webinars .table-dropdown {
        padding-top: 1rem;
    }

    .webinar-section .all-webinars .table-dropdown .btn {
        padding: 0 !important;
    }



    .webinars-attend .attend div span, .all-webinars-header span {
        font-size: 18px !important;
    }

    .webinars-attend .attend div .fa-clipboard, .all-webinars-header i {
        font-size: 20px !important;
    }

    .webinars-attend .attend div .fa-ellipsis-vertical {
        font-size: 16px !important;
    }

    .webinars-attend .total .total-webinars {
        font-size: 20px !important;
    }

    .webinars-attend .filter-section {
        margin-right: 0 !important;
    }

    .webinars-attend .filter-section .filter2 span {
        margin-right: 0 !important;
    }

    .video-texts2 {
        margin-inline: 0.5rem !important;
    }

    .video-texts {
        align-items: center !important;
        justify-content: space-between !important;
        margin-inline: 0.5rem !important;
    }

    .video-texts span:first-child,
    .video-texts2 span:first-child {
        font-size: 16px !important;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        line-clamp: 1;
        -webkit-box-orient: vertical;

    }

    /* Webinar Section */
}

@media (min-width: 1050px) and (max-width: 1199.98px) {
    /* Search Section */
    .profile-section {
        justify-content: space-around !important;
    }
    .search-container input[type="text"] {
        width: 300px !important;
    }

    .search-container {
        width: auto !important;
    }

    /* Search Section */
}

@media (min-width: 1130px) and (max-width: 1199.98px) {
    /* Search Section */
    .profile-section {
        justify-content: space-around !important;
    }
    .search-container input[type="text"] {
        width: 350px !important;
    }

    .search-container {
        width: auto !important;
    }
    /* Search Section */
}

@media (min-width: 1200px) and (max-width: 1373.98px) {
    .user-infos .titles span:last-child {
        margin-top: 1.9rem !important;
    }
    /* Video Section */
    .video-section {

        margin-top: 2rem;
    }

    .custom-video-table {
        overflow-y: hidden;
        overflow-x: auto;
    }

    .most-watched-header div span {
        font-size: 16px !important;
    }

    .most-watched,
    .most-watched2 {
        justify-content: center !important;
        margin-block: 0.5rem;
    }

    .video-container {
        padding-top: 2rem !important;
    }

    .video-container i {
        font-size: 26px;
        margin-bottom: 1rem !important;
    }

    .last-video-title i {
        font-size: 18px !important;
    }

    .video-texts span:nth-child(1) {
        font-size: 15px !important;
    }

    .last-video-title span {
        font-size: 15px !important;
    }

    .video-status span {
        font-size: 12px !important;
    }

    .video-texts2 {
        margin-inline: 0.5rem !important;
    }

    .video-texts {
        align-items: center !important;
        justify-content: space-between !important;
        margin-inline: 0.5rem !important;
    }

    .video-texts span:first-child,
    .video-texts2 span:first-child {
        font-size: 16px !important;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        line-clamp: 1;
        -webkit-box-orient: vertical;
    }

    /* Video Section */
}

@media (min-width: 1200px) and (max-width: 1465px) {
    .user-info-section {
    }

    .username {
        padding-right: 0 !important;
    }

    /* Watch Section */
    .watch-hr {
        margin-inline: 1rem !important;
        margin-block: 2rem !important;
    }

    .watch-section {
        justify-content: space-evenly !important;

    }

    .watch-bar-graph .play i {
        font-size: 26px !important;
    }

    .watch-bar-graph .play span {
        font-size: 20px !important;
    }



    .line-graph-header {
        align-items: center !important;
    }

    .line-graph-header .total-watch {
        font-size: 18px !important;
    }

    .brand-hr2 {
        margin: 1rem 1.5rem 0 1.5rem !important;
    }

    .line-graph-brand-header {
        margin-inline: 2rem !important;
    }

    .brand-bar {
        margin-inline: 2rem !important;
    }
    .brand-bar .brand-time .big-sp {
        font-size: 24px !important;
    }

    .brand-bar .brand-time .small-sp {
        font-size: 14px !important;
    }

    .brand-bar .flex-column span {
        font-size: 14px !important;
    }

    .brand-bar .flex-column {
        width: 70% !important;
    }

    /* Watch Section */

    /* Video Section */
    .video-section {

        margin-top: 2rem;
    }


    .custom-video-table {
        overflow-y: hidden;
        overflow-x: auto;
    }

    .most-watched-header div span {
        font-size: 16px !important;
    }

    .most-watched,
    .most-watched2 {
        justify-content: center !important;
        margin-block: 0.5rem;
    }

    .video-container {
        padding-top: 2rem !important;
    }

    .video-container i {
        font-size: 26px;
        margin-bottom: 1rem !important;
    }

    .last-video-title i {
        font-size: 18px !important;
    }

    .video-texts span:nth-child(1) {
        font-size: 15px !important;
    }

    .last-video-title span {
        font-size: 15px !important;
    }

    .video-status span {
        font-size: 12px !important;
    }

    /* Video Section */

    /* Time Spent Section */


    .time-spent-header .dropdown {
        margin-top: 0.5rem;
    }

    .pie-settings2 {
        height: 13rem !important;
        margin-bottom: 1rem !important;
    }

    .web-time div .span-hour {
        font-size: 26px !important;
    }

    .web-time div .span-minute {
        font-size: 28px !important;
    }

    .web-time div .hour-text {
        font-size: 16px !important;
    }

    .web-container div i,
    .app-container div i {
        font-size: 28px !important;
    }

    .web-container div .span-hour,
    .app-container div .span-hour {
        font-size: 45px !important;
    }

    .web-container div .hour-text,
    .app-container div .hour-text {
        font-size: 25px !important;
    }

    .web-container div .span-minute,
    .app-container div .span-minute {
        font-size: 45px !important;
    }
    /* Time Spent Section */

    /* Webinar Section */
    .webinar-section {
        margin-top: 1rem;

    }

    .webinar-section .webinar-table {
        overflow-y: hidden;
        overflow-x: auto;
    }

    .webinar-section .all-webinars .table-dropdown {
        padding-top: 1rem;
    }

    .webinar-section .all-webinars .table-dropdown .btn {
        padding: 0 !important;
    }


    .webinars-attend .attend div span, .all-webinars-header span {
        font-size: 18px !important;
    }

    .webinars-attend .attend div .fa-clipboard, .all-webinars-header i {
        font-size: 20px !important;
    }

    .webinars-attend .attend div .fa-ellipsis-vertical {
        font-size: 16px !important;
    }

    .webinars-attend .total .total-webinars {
        font-size: 20px !important;
    }

    .webinars-attend .filter-section {
        margin-right: 0 !important;
    }

    .webinars-attend .filter-section .filter2 span {
        margin-right: 0 !important;
    }

    /* Webinar Section */

    .video-texts2 {
        margin-inline: 0.5rem !important;
    }

    .video-texts {
        flex-direction: column !important;
        align-items: start !important;
        justify-content: start !important;
        margin-inline: 0.5rem !important;
    }

    .video-texts span:first-child,
    .video-texts2 span:first-child {
        font-size: 16px !important;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        line-clamp: 1;
        -webkit-box-orient: vertical;
    }
}

@media (min-width: 1465.01px) and (max-width: 1700px) {
    .user-info-section {

    }

    .username {
        padding-right: 0 !important;
    }

    /* Watch Section */
    .watch-hr {
        margin-inline: 1rem !important;
        margin-block: 2rem !important;
    }

    .watch-section {
        justify-content: space-evenly !important;

    }

    .watch-bar-graph .play i {
        font-size: 26px !important;
    }

    .watch-bar-graph .play span {
        font-size: 20px !important;
    }



    .line-graph-header {
        align-items: center !important;
    }

    .line-graph-header .total-watch {
        font-size: 18px !important;
    }

    .brand-hr2 {
        margin: 1rem 1.5rem 0 1.5rem !important;
    }

    .line-graph-brand-header {
        margin-inline: 2rem !important;
    }

    .brand-bar {
        margin-inline: 2rem !important;
    }
    .brand-bar .brand-time .big-sp {
        font-size: 24px !important;
    }

    .brand-bar .brand-time .small-sp {
        font-size: 14px !important;
    }

    .brand-bar .flex-column span {
        font-size: 14px !important;
    }

    .brand-bar .flex-column {
        width: 70% !important;
    }

    /* Watch Section */

    /* Video Section */
    .video-section {

        margin-top: 2rem;
    }


    .custom-video-table {
        overflow-y: hidden;
        overflow-x: auto;
    }

    .most-watched-header div span {
        font-size: 16px !important;
    }

    .most-watched,
    .most-watched2 {
        justify-content: center !important;
        margin-block: 0.5rem;
    }

    .video-container {
        padding-top: 2rem !important;
    }

    .video-container i {
        font-size: 26px;
        margin-bottom: 1rem !important;
    }

    .last-video-title i {
        font-size: 18px !important;
    }

    .video-texts span:nth-child(1) {
        font-size: 15px !important;
    }

    .last-video-title span {
        font-size: 15px !important;
    }

    .video-status span {
        font-size: 12px !important;
    }

    /* Video Section */
}
/* Large Notebook devices */

/* Monitor TV devices */
@media (min-width: 1200px) and (max-width: 1260px) {
    /* Search Section */
    .search-container {
        width: auto !important;
    }

    .profile-section {
        justify-content: space-between !important;
    }

    .search-container input[type="text"] {
        width: 400px !important;
    }

    .user-search-context {
        padding: 1rem 0 0 1rem !important;
    }
    .icons {
        display: flex;
    }

    /* Search Section */

    /* Video Section */
    .video-section {
        margin-top: 2rem;
    }



    .custom-video-table {
        overflow-y: hidden;
        overflow-x: auto;
    }

    .most-watched-header div span {
        font-size: 16px !important;
    }

    .most-watched,
    .most-watched2 {
        justify-content: center !important;
        margin-block: 0.5rem;
    }

    .video-container {
        padding-top: 2rem !important;
    }

    .video-container i {
        font-size: 26px;
        margin-bottom: 1rem !important;
    }

    .last-video-title i {
        font-size: 18px !important;
    }

    .video-texts span:nth-child(1) {
        font-size: 15px !important;
    }

    .last-video-title span {
        font-size: 15px !important;
    }

    .video-status span {
        font-size: 12px !important;
    }

    /* Video Section */
}

@media (min-width: 1260.01px) and (max-width: 1330px) {
    /* Search Section */
    .search-container {
        width: auto !important;
    }

    .profile-section {
        justify-content: space-between !important;
    }

    .search-container input[type="text"] {
        width: 450px !important;
    }

    .icons {
        display: flex;
    }



    /* Search Section */

    /* Video Section */
    .video-section {

        margin-top: 2rem;
    }


    .custom-video-table {
        overflow-y: hidden;
        overflow-x: auto;
    }

    .most-watched-header div span {
        font-size: 16px !important;
    }

    .most-watched,
    .most-watched2 {
        justify-content: center !important;
        margin-block: 0.5rem;
    }

    .video-container {
        padding-top: 2rem !important;
    }

    .video-container i {
        font-size: 26px;
        margin-bottom: 1rem !important;
    }

    .last-video-title i {
        font-size: 18px !important;
    }

    .video-texts span:nth-child(1) {
        font-size: 15px !important;
    }

    .last-video-title span {
        font-size: 15px !important;
    }

    .video-status span {
        font-size: 12px !important;
    }

    /* Video Section */
}

@media (min-width: 1330.01px) and (max-width: 1400px) {
    /* Search Section */
    .search-container {
        width: auto !important;
    }

    .profile-section {
        justify-content: space-between !important;
    }

    .search-container input[type="text"] {
        width: 500px !important;
    }

    .icons {
        display: flex;
    }


    /* Search Section */

    /* Video Section */
    .video-section {

        margin-top: 2rem;
    }

    .custom-video-table {
        overflow-y: hidden;
        overflow-x: auto;
    }

    .most-watched-header div span {
        font-size: 16px !important;
    }

    .most-watched,
    .most-watched2 {
        justify-content: center !important;
        margin-block: 0.5rem;
    }

    .video-container {
        padding-top: 2rem !important;
    }

    .video-container i {
        font-size: 26px;
        margin-bottom: 1rem !important;
    }

    .last-video-title i {
        font-size: 18px !important;
    }

    .video-texts span:nth-child(1) {
        font-size: 15px !important;
    }

    .last-video-title span {
        font-size: 15px !important;
    }

    .video-status span {
        font-size: 12px !important;
    }

    /* Video Section */
}

@media (max-width: 1250px) {
    .basic-metrics {
        width: 32% !important;
    }
}

@media (min-width: 1400.01px) {
    .icons {
        display: flex;
    }
}
/* Monitor TV devices */

/************************** CSS Styles ***********************/

/* Search Section */

.statistics-section {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(41, 45, 50, 0.2);
}

.search-icon {
    color: var(--grey);
    font-size: 20px;
    margin-left: 1rem;
}

.search-bar {

    font-size: 12px;
    color: var(--dark);
    opacity: 0.3;
    letter-spacing: 0.24px;
    font-weight: 400;
}

.search-container {
    padding: 10px;
    display: flex;
    align-items: center;
}

.search-container img {
    max-height: 40px;
    margin-right: 10px;
}

.search-container input[type="text"] {
    padding: 8px;
    border: none;
    border-radius: 5px;
    width: 400px;
    outline: none;
}

.profile-pic {
    background-color: var(--darkgrey);
    height: 40px;
    width: 40px;
    border-radius: 6px;
    margin-right: 2rem;
}

.profile-texts p {
    text-align: right;
    margin: 0;
    padding: 0;
}

.profile-texts p:nth-child(1) {
    font-size: 14px;

    font-weight: 600;
    line-height: 1.5rem;
}

.profile-texts p:nth-child(2) {
    font-size: 11px;

    font-weight: 400;
    line-height: 0.5rem;
}

.icons .iconsax {
    font-size: 24px;
}

/* Search Section */

/* User Info */

.user-info-section {

    width: auto;
    margin-top: 4rem;
    max-height: 40vh;
}

.user-info-section .dashboard {
    background: rgba(255, 255, 255, 0.4);
    border-radius: 8px;
    box-shadow: 3px 2px 8.9px 1px rgba(0, 0, 0, 0.25);
    backdrop-filter: blur(3px);
    width: auto;
}



.profile-picture-circle {
    width: 45px;
    height: 45px;
    background-color: #949494;
    border-radius: 50%;
}

.username {
    margin-top: 1.5rem;
    padding-right: 11.25rem;
}

.username div:first-child {
    margin-left: 0.75rem;
}

.username span:first-child {
    margin-left: 1.5rem;
}

.dashboard-header {
    padding-top: 1rem;
}
.dashboard-header span {
    margin-left: 0.5rem;
}

.username span:nth-child(2) {
    font-size: 20px;
    font-weight: 500;
    letter-spacing: 0.4px;

    color: #0a0a1f;
}

.username span:last-child {

    font-weight: 400;
    font-size: 18px;
    color: #0a0a1f;
    letter-spacing: 0.2px;
}

.user-infos {
    margin-left: 2.5rem;
    margin-top: 1.5rem;
}

.dashboard-title span {
    color: #333;

    font-size: 18px;
    font-weight: 300;
    letter-spacing: 0.9px;
}

.dashboard-subtitle span {
    color: #383838;
    font-weight: 500;
    letter-spacing: 0.7px;
    font-size: 18px;

}

.user-infos .info {
    margin-left: 1.75rem;
}

.user-infos .titles span,
.user-infos .info span {
    margin-block: 0.25rem;
}

.user-infos .info span:last-child {
    margin-bottom: 2rem;
}

.basic-metrics {
    background: #2c2c2c;
    border-radius: 8px;
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.35);
}

.basic-metrics .metric-title {
    margin-left: 1rem;
    margin-top: 1rem;
    margin-right: 4rem;
}

.basic-metrics .metric-title .iconsax {
    color: #ededed;
    font-size: 20px;
}

.basic-metrics .metric-title span {
    margin-left: 0.25rem;
    color: #f3efff;
    letter-spacing: 0.4px;
    font-size: 20px;

    font-weight: 300;
}

.metric-horizontal {
    border-top: 2px solid #707070;
}

.metric-media {
    padding-inline: 2rem;
    padding-block: 0.75rem;
}

.metric-media .media-icon {
    font-size: 26px;
    color: #c1b6e5;
}

.metric-media .media-title {
    color: #c1b6e5;
    font-weight: 500;

    font-size: 22px;
    margin-left: 0.5rem;
}

.metric-media .fa-circle-xmark {
    font-size: 26px;
    color: var(--red);
}

.metric-media .fa-circle-check {
    font-size: 26px;
    color: var(--green);
}

/* User Info */

/* Watch Graph */



.watch-bar-graph {
    background-color: #2c2c2c;
    border-radius: 12px;
    box-shadow: 0px 4px 3px 0px rgba(0, 0, 0, 0.1);
}

.watch-bar-graph .play i {
    font-size: 34px;
    color: #ffffff;
}

.watch-bar-graph .play span {
    color: #d9d9d9;

    font-size: 28px;
    font-weight: 400;
    margin-left: 0.75rem;
}

.rotate-horizontal {
    transform: rotate(90deg);
    display: inline-block;
}

.watch-bar-graph .filter i,
.watch-bar-graph .filter span {
    font-size: 16px;
    color: #dbdbdb;

    font-weight: 300;
    margin-inline: 0.25rem;
}

.watch-line-graph {
    border-radius: 12px;
    background: #fff;
    box-shadow: 3px 2px 8.9px 1px rgba(0, 0, 0, 0.25);
    margin-left: 1rem;
}

.line-graph-header {
    margin-inline: 2rem;
    margin-top: 1.25rem;
}

.line-graph-header .total-watch {
    color: #3c3c3c;

    font-size: 28px;
    font-weight: 500;
}

.line-graph-header .dropdown {
    max-width: 5rem !important;
    background-color: transparent;
    border-radius: 0.375rem;
}

.line-graph-header .dropdown .btn {
    border: none;
    outline: none;
    width: 5rem;
    box-shadow: 0px 1px 6.3px 0px rgba(0, 0, 0, 0.25);
}

.line-graph-header .dropdown-menu .active {
    background-color: var(--purple) !important;
}

.line-graph-header .dropdown-menu {
    min-width: 5rem !important;
    border: none !important;
    outline: none !important;
}

.line-graph-header .btn span {
    color: #000;

    font-size: 15px;
    font-weight: 200;
}

.line-graph-header .dropdown-menu a {
    font-size: 15px;
    font-weight: 200;
    text-align: center;

    color: #000;
}

.line-graph-header .dropdown-menu a.active {
    color: #f0f0f0;
}

.line-graph-brand-header {
    margin-top: 1rem;
    margin-bottom: 1rem;
    margin-inline: 2rem;
}

.line-graph-brand-header span {
    color: #8c8c8c;

    font-size: 20px;
    font-weight: 200;
}

.brand-bars-container .brand-bar {
    margin-top: 0.5rem;
}

.brand-bar {
    margin-inline: 2rem;
}

.brand-bar .flex-column {
    width: 100%;
}

.brand-bar .flex-column span {
    margin-bottom: 0.5rem;
    color: #262626;

    font-size: 20px;
    font-weight: 300;
}

.brand-bar .flex-column .progress {
    height: 14px;
    border-radius: 10px;
    background: #dfdfdf;
    box-shadow: 0px -4px 4px -2px rgba(0, 0, 0, 0.25) inset,
    0px 3px 2.8px 0px rgba(255, 255, 255, 0.8) inset;
}

.brand-bar .brand-time {
    text-align: right;
    width: 50%;
}

.brand-bar .brand-time span {
    display: inline-block;
}

.brand-bar .brand-time .big-sp {
    color: #146086;

    font-size: 30px;
    font-weight: 400;
}

.brand-bar .brand-time .small-sp {
    color: #3c3c3c;

    font-size: 18px;
    font-weight: 300;
}

.brand-bars-container .justify-content-end span {
    color: #525256;

    font-size: 14px;
    font-weight: 300;
    margin-right: 2rem;
    margin-block: 1rem;
}

/* Watch Graph */

/* Video Section */

.video-section {
    margin-top: 2rem;
}

.most-watched-video-section {
    border-radius: 12px;
    background: #fff;
    box-shadow: 0px 7px 12.2px 3px rgba(0, 0, 0, 0.25);
}

.most-watched-header {
    padding-top: 1rem;
}

.most-watched-header div i {
    font-size: 30px;
    color: #3b3b3b;
    margin-right: 0.5rem;
}

.most-watched-header div span {
    color: #3c3c3c;

    font-size: 24px;
    font-weight: 500;
}

.most-watched-dropdown {
    padding-inline: 1.5rem;
    margin-top: 0.5rem;
}

.most-watched-dropdown .custom-dropdown-container .custom-item {
    display: flex;
    align-items: center;
    justify-content: center;
}

.most-watched-dropdown .custom-dropdown-container {
    padding: 4px;
    border-radius: 8px;
    background-color: white;
    text-align: center;
    box-shadow: 0px 1px 8.4px 0px rgba(0, 0, 0, 0.1);
    width: 8rem;
}

.most-watched-dropdown .dropdown {
    max-width: 8rem !important;
    background-color: transparent;
    border-radius: 0.375rem;
}

.most-watched-dropdown .dropdown .btn {
    border: none;
    outline: none;
    box-shadow: 0px 1px 8.4px 0px rgba(0, 0, 0, 0.1);
}

.most-watched-dropdown .dropdown-menu .active {
    background-color: var(--purple) !important;
}

.most-watched-dropdown .dropdown-menu {
    min-width: 8rem !important;
    border: none !important;
    outline: none !important;
}

.most-watched-dropdown .btn span {
    color: #000;

    font-size: 15px;
    font-weight: 200;
}

.most-watched-dropdown .dropdown-menu a {
    font-size: 15px;
    font-weight: 200;
    text-align: center;

    color: #000;
}
.most-watched-dropdown .dropdown-menu a.active {
    color: #f0f0f0;
}

tbody {
    border-left: 2px solid #4d7a90 !important;
}

.custom-video-table {
    padding-bottom: 1rem;
    margin-top: 1rem;
}

.custom-video-table .table tbody tr td span {
    color: #4d7a90;

    font-size: 24px;
    font-weight: 400;
    letter-spacing: 0.17px;
}

.custom-video-table .table tbody tr td {
    color: rgba(0, 0, 0, 0.87);

    font-size: 16px;
    font-weight: 400;
    letter-spacing: 0.17px;
    padding-block: 0.75rem !important;
}

.custom-video-table .table thead tr th {
    color: rgba(0, 0, 0, 0.87);

    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0.17px;
}

.last-video-container {
    border-radius: 12px;
    background: #2c2c2c;
    box-shadow: 0px 7px 12.2px 3px rgba(0, 0, 0, 0.25);
    margin-left: 1rem;
}

.last-video-container .filter i {
    color: #858585;
    font-size: 16px;
}

.last-video-container .filter span {
    color: #858585;

    font-size: 16px;
    font-weight: 300;
}

.header-filter {
    margin-top: 1rem;
    margin-right: 2rem;
}

.last-video-title {
    margin-left: 1.75rem;
}

.last-video-title i {
    color: #ffffff;
    font-size: 30px;
}

.last-video-title span {
    color: #fff;

    font-size: 22px;
    font-weight: 500;
    margin-left: 0.5rem;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
}

.video-status {
    margin-inline: 2rem;
    margin-top: 1rem;
}

.video-status span {
    color: #fff;

    font-size: 15px;
    font-weight: 300;
}

.book-container {
    background: linear-gradient(26deg, #7c7c7c 16.53%, #fff 85.39%);
    padding-top: 3.5rem;
    margin-inline: 2rem;
    margin-bottom: 1rem;
    border-radius: 6px;
    margin-top: 1rem;
    position: relative;
    height: -webkit-fill-available;
}

.video-container {
    margin-inline: 2rem;
    background: linear-gradient(26deg, #7c7c7c 16.53%, #fff 85.39%);
    border-radius: 6px;
    margin-top: 1rem;
    height: -webkit-fill-available;
    position: relative;
}

.video-container i {
    font-size: 40px;
    color: white;
    cursor: pointer;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}

.video-texts span:first-child,
.video-texts2 span:first-child {
    color: #fff;

    font-size: 24px;
    font-weight: 400;

}

.video-texts span:nth-child(2),
.video-texts2 span:nth-child(2) {
    color: #fff;

    font-size: 15px;
    font-weight: 300;
}

.video-texts2 span:first-child {
    position: absolute;
    bottom: 8px;
    left: 16px;
}

.video-texts2 span:nth-child(2) {
    position: absolute;
    bottom: 8px;
    right: 16px;
}

.video-texts {
    margin-inline: 1rem;
    padding-bottom: 0.5rem;
    position: absolute;
    width: -webkit-fill-available;
    bottom: 0;
}

#videoModal .modal-dialog {
    max-width: 800px;
    margin: 30px auto;
    overflow: hidden;
}

#videoModal .modal-content {
    border: none;
    background: transparent;
}

#videoModal .modal-body {
    padding: 0;
    position: relative;
}

#videoModal .modal-body video {
    width: 100%;
    height: auto;
}

.modal.fade.show {
    display: flex !important;
    align-items: center;
}

.modal-dialog {
    margin: auto;
}

.video-hr {
    margin-inline: 2rem;
}

/* Video Section */

/* Time Spent Section */

.time-spent-section {
    margin-top: 2rem;
}

.time-spent-container {
    border-radius: 12px;
    background: #2c2c2c;
    box-shadow: 7px 11px 12.9px 0px rgba(0, 0, 0, 0.3);
}

.time-spent-header {
    padding-top: 1.5rem;
    margin-inline: 3rem;
}

.time-spent-header span {
    color: #fff;

    font-size: 26px;
    font-weight: 500;
}

.time-spent-header .dropdown {
    max-width: 5rem !important;
    background: transparent;
    border-radius: 3px;
}

.time-spent-header .dropdown .btn {
    border: none;
    outline: none;
    background: #e7e7e7;
    width: 5rem;
    box-shadow: 0px 1px 6.3px 0px rgba(0, 0, 0, 0.25);
}

.time-spent-header .dropdown-menu .active {
    background-color: var(--purple) !important;
}

.time-spent-header .dropdown-menu {
    min-width: 5rem !important;
    border: none !important;
    outline: none !important;
}

.time-spent-header .btn span {
    color: #000;

    font-size: 15px;
    font-weight: 200;
}

.time-spent-header .dropdown-menu a {
    font-size: 15px;
    font-weight: 200;
    text-align: center;

    color: #000;
}

.time-spent-header .dropdown-menu a.active {
    color: #f0f0f0;
}

.time-spent-container hr {
    margin: 0;
    padding: 0;
    margin-top: 0.5rem;
    margin-inline: 2rem;
}
.pie-settings2 {
    height: 17rem;
    display: flex;
    justify-content: center;
}


.web-time div .span-hour {
    color: #e1e1e1;

    font-size: 28px;
    font-weight: 400;
}

.web-time div .span-minute {
    color: #e1e1e1;

    font-size: 28px;
    font-weight: 400;
}

.web-time div .hour-text {
    color: #e1e1e1;

    font-size: 16px;
    font-weight: 400;
}

.spent-time-text {
    color: #e1e1e1;

    font-size: 15px;
    font-weight: 400;
}


.app-container {
    border-radius: 12px;
    background: #8082b8;
    box-shadow: 7px 11px 12.9px 0px rgba(0, 0, 0, 0.3);
}

.web-container div i,
.app-container div i {
    color: #ffffff;
    font-size: 38px;

    padding-right: 1.5rem;
}

.web-container {
    border-radius: 12px;
    background: #7ec752;
    box-shadow: 7px 11px 12.9px 0px rgba(0, 0, 0, 0.3);
    margin-top: 1rem;
}

.web-container div .span-hour,
.app-container div .span-hour {
    color: #e1e1e1;

    font-size: 80px;
    font-weight: 300;
    margin: 0; /* Margin değerini kaldır */
    padding: 0; /* Padding değerini kaldır */
    line-height: 1;
}

.web-container div .span-minute,
.app-container div .span-minute {
    color: #e1e1e1;

    font-size: 52px;
    font-weight: 300;
    margin: 0;
    padding: 0;
    line-height: 1;
    margin-right: 0;
}

.app-web-container{
    margin-left: 1rem;
}

.web-container div .hour-text,
.app-container div .hour-text {
    color: #e1e1e1;

    font-size: 24px;
    font-weight: 600;
    margin: 0;
    padding: 0;
    line-height: 1;
}

.app-container span .device-status,
.web-container span .device-status {
    color: #fff;

    font-size: 24px;
    font-weight: 600;
    margin: 0;
    padding: 0;
    line-height: 1;
}

.app-container span,
.web-container span {
    color: #fff;

    font-size: 22px;
    font-weight: 400;
    margin: 0;
    padding: 0;
    line-height: 1;
}

/* Time Spent Section */

/* Webinar Section */
.webinar-section {
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.webinar-section .all-webinars {
    border-radius: 12px;
    background: #fff;
    box-shadow: 0px 4px 7px 5px rgba(0, 0, 0, 0.17);
}

.all-webinars-header i {
    font-size: 30px;
    color: #292d32;
}

.all-webinars-header span {
    color: #292d32;

    font-size: 28px;
    font-weight: 500;
    margin-left: 0.5rem;
}

.webinar-table tbody {
    border-left: 0 !important;
}

.webinar-table td,
.webinar-table th {
    border: none !important;
}

.webinar-table thead tr:nth-child(1) {
    border-bottom: 3px solid #a4a4a4 !important;
}

.webinar-table thead tr th {
    color: rgba(0, 0, 0, 0.87);
    padding: 10px 0;
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0.17px;
    border: 1px solid #a4a4a4!important;
    padding: 5px 10px;
}

.webinar-table thead tr th:nth-child(1) {
    min-width: 200px;
}

.webinar-table tbody tr th {
    color: (0, 0, 0, 0.87);
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0.17px;
    letter-spacing: 0.17px;
    border: 1px solid #a4a4a4 !important;
    padding: 5px 10px;
}

.webinar-table tbody tr td {
    color: rgba(0, 0, 0, 0.87);
    font-size: 16px;
    font-weight: 400;
    letter-spacing: 0.17px;
    padding-block: 1rem;
    letter-spacing: 0.17px;
    border: 1px solid #a4a4a4 !important;
    padding: 5px 10px;
}

.webinar-table table,
.webinar-table th,
.webinar-table td {
    border-collapse: collapse;
}

.table-dropdown .dropdown {
    background: transparent;
}

.table-dropdown .dropdown .btn {
    border: none;
    outline: none;
}

.table-dropdown .dropdown-menu .active {
    background-color: var(--purple) !important;
}

.table-dropdown .dropdown-menu {
    min-width: 2rem !important;
    border: none !important;
    outline: none !important;
}

.table-dropdown .btn span {
    color: rgba(0, 0, 0, 0.87);

    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0.3px;
}

.table-dropdown .dropdown-menu a {
    color: rgba(0, 0, 0, 0.87);

    font-size: 12px;
    font-weight: 400;
    letter-spacing: 0.3px;
}

.table-dropdown .dropdown-menu a.active {
    color: #f0f0f0;
}

.table-dropdown .dropdown .per-page {
    color: rgba(0, 0, 0, 0.6);

    font-size: 12px;
    font-weight: 400;
    letter-spacing: 0.4px;
}

.table-dropdown div .fa-chevron-left {
    margin-left: 2.5rem;
}

.webinars-attend {
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(20px);
    box-shadow: 0px 4px 7px 5px rgba(0, 0, 0, 0.17);
    margin-left: 0.75rem;
}



.webinars-attend .attend div .fa-clipboard {
    font-size: 32px;
    color: #292d32;
}

.webinars-attend .attend div span {
    color: #292d32;

    font-size: 28px;
    font-weight: 500;
    margin-left: 0.5rem;
}

.webinars-attend .attend div .fa-ellipsis-vertical {
    color: #5e5e5e;
    font-size: 24px;
}

.webinars-attend .filter-section {
    margin-right: 2rem;
}

.webinars-attend .filter-section .filter i,
.webinars-attend .filter-section .filter2 i {
    font-size: 16px;
    color: #525256;
}

.webinars-attend .filter-section .filter span {
    color: #525256;

    font-size: 16px;
    font-weight: 300;
    margin-left: 0.5rem;
    margin-right: 2rem;
}
.webinars-attend .filter-section .filter2 span {
    color: #525256;

    font-size: 16px;
    font-weight: 300;
    margin-left: 0.5rem;
}
.webinars-attend .filter-section .show-all {
    color: #525256;

    font-size: 16px;
    font-weight: 300;
}

.webinars-attend .total {
    margin-left: 1rem;
    margin-top: 1rem;
}

.webinars-attend .total .big-number {
    color: #242424;

    font-size: 48px;
    font-weight: 300;
    margin: 0;
    padding: 0;
    line-height: 1;
}

.webinars-attend .total .piece {
    color: #242424;

    font-size: 20px;
    font-weight: 300;
    margin: 0;
    padding: 0;
    line-height: 1;
}

.webinars-attend .total .total-webinars {
    color: #242424;

    font-size: 24px;
    font-weight: 400;
    margin-left: 0.5rem;
    margin-top: 0.5rem;
}

/* Webinar Section */

/* Latest codes */

@media (max-width: 767px) {
    .dashboard {
        padding-inline: 0.5rem !important;
        min-height: 26vh !important;
    }

    .username-span {
        font-size: 18px !important;
    }

    .dashboard-header {
        padding-inline: 0.5rem !important;
    }

    .dashboard-span,
    .d-body-title,
    .d-body-subtitle {
        font-size: 16px !important;
    }
    .profile-circle {
        width: 36px !important;
        height: 36px !important;
    }

    .basics {
        margin-block: 1rem;
    }

    header .current-date {
        font-size: 1rem !important;
    }
    .weeks li {
        font-size: 0.75rem !important;
    }
    .days li {
        font-size: 1rem !important;
    }
    .days li.active::before {
        width: 1.75rem;
        height: 1.75rem;
    }
    .calendar{
        margin-top: 2rem !important;
    }
}

@media (min-width: 767px) and (max-width: 991px) {
    .dashboard-body {
        padding-block: 1.75rem;
        margin-top: 0 !important;
    }

    header .current-date{
        font-size: 1em !important;
    }

    .calendar {
        margin-top: 1rem !important;
    }

    .calendar-settings li{
        font-size: 0.75rem  !important;
    }

    .days li::before{
        height: 25px !important;
        width: 25px !important;
    }

}

@media (min-width: 991px) and (max-width: 1199px) {
    .basics-header span,
    .basics-header i {
        font-size: 16px !important;
    }

    .basics {
        padding: 0 !important;
    }

    .dashboard-body {
        padding-block: 1rem;
        margin-top: 0 !important;
    }

    .b-body-item {
        margin-inline: 0.5rem !important;
    }

    .basics-header {
        padding-top: 1rem !important;
        padding-left: 0.5rem !important;
    }

    .b-body-item .align-items-center i {
        font-size: 22px !important;
    }

    .b-body-item .align-items-center span {
        font-size: 18px !important;
        margin-left: 0.5rem !important;
    }
    header .current-date {
        font-size: 1rem !important;
    }

    .weeks li {
        font-size: 0.75rem !important;
    }
    .days li {
        font-size: 1rem !important;
    }

    .days li.active::before {
        width: 1.75rem !important;
        height: 1.75rem !important;
    }
    .wp-item {
        padding-bottom: 0.9rem !important;
    }
}

@media (min-width: 1200px) {
    .calendar-settings .days li {
        margin-block: 0.6rem !important;
    }
    .dashboard-body {
        margin-top: 0 !important;
        padding-block: 1.8rem !important;
    }
    .days li.active::before {
        width: 2rem !important;
        height: 2rem !important;
    }
    .weeks li {
        font-size: 0.75rem !important;
    }
    .days li {
        font-size: 1rem !important;
    }
}

.user-section-container {
    margin-top: 2rem;
}

.dashboard {
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.4);
    box-shadow: 3px 2px 8.9px 1px rgba(0, 0, 0, 0.25);
    backdrop-filter: blur(3px);
    padding-inline: 2rem;
    min-height: 30vh;
    height: 100%;
}

.profile-circle {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-color: #949494;
    object-fit: cover;
}

.username-span {
    color: #0a0a1f;

    font-size: 20px;
    font-weight: 500;
    letter-spacing: 0.4px;
    margin-left: 0.5rem;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
}

.dashboard-span {
    color: #0a0a1f;

    font-size: 18px;
    font-weight: 400;
    letter-spacing: 0.18px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
}

.d-body-title {
    color: #333;
    font-size: 18px;
    font-weight: 300;
    letter-spacing: 0.9px;
    /* padding-left: 1rem; */
    flex: 0 0 18%;
}
.d-body-subtitle {
    color: #383838;
    font-size: 18px;
    font-weight: 500;
    letter-spacing: 0.72px;
    /* padding-left: 2rem; */
    flex: 0 0 80%;
    word-break: break-all;
}

.dashboard-body {
    margin-top: 0.5rem;
}

.basics {
    border-radius: 8px;
    background: #2c2c2c;
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.35);
    padding: 1rem;
    height: 100%;
}

.basics-header i {
    font-size: 20px;
}

.basics-header span {
    color: #f3efff;

    font-size: 20px;
    font-weight: 400;
    letter-spacing: 0.4px;
    margin-left: 0.5rem;
}

.basics-hr {
    border-top: 1px solid white;
    margin-block: 1rem;
}

.b-body-item .fa-circle-check {
    color: #2dc748;
    font-size: 26px;
}

.b-body-item .align-items-center i {
    font-size: 26px;
}

.b-body-item .align-items-center span {
    color: #c1b6e5;

    font-size: 22px;
    font-weight: 500;
    margin-left: 1rem;
    margin-block: 0.25rem;
}

.calendar-header {
    box-shadow: 0px 4px 4.8px 0px rgba(0, 0, 0, 0.22);
    backdrop-filter: blur(50px);
    background-color: #7ec752;
    color: #f3efff;
    border-radius: 6px;
    padding-block: 0.5rem;
}

.calendar-header i {
    font-size: 24px;
    margin-left: 0.5rem;
}

.calendar-header span {
    color: #fff;
    font-size: 20px;
    font-weight: 400;
    letter-spacing: 0.4px;
}

.calendar-body {
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.1);
    box-shadow: 0px 4px 4.8px 0px rgba(0, 0, 0, 0.22);
    backdrop-filter: blur(50px);
    margin-top: 0.2rem;
}

.month-header span {
    color: #2d2d2d;

    font-size: 16px;
    font-weight: 600;
    padding-block: 0.5rem;
}

.wrapper {
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
    min-height: auto;
}
.wrapper header {
    display: flex;
    justify-content: center;
    margin-bottom: 10px;
}
header .icons {
    display: flex;
}
header .icons span {
    height: 38px;
    width: 38px;
    margin: 0 1px;
    cursor: pointer;
    color: #878787;
    text-align: center;
    line-height: 38px;
    font-size: 1.9rem;
    user-select: none;
    border-radius: 50%;
}
.icons span:last-child {
    margin-right: -10px;
}
header .icons span:hover {
    background: #f2f2f2;
}
header .current-date {
    font-size: 1.25rem;
    font-weight: 500;
    margin-block: 0.5rem;
    color: #7c7c7c;

}
.calendar-settings ul {
    padding: 0;
    margin: 0;
}

.calendar-settings ul {
    display: flex;
    flex-wrap: wrap;
    list-style: none;
    text-align: center;
}

.calendar-settings li {
    color: #333;
    width: calc(100% / 7);
    font-size: 1.07rem;
}
.calendar-settings .weeks li {
    font-weight: 500;
    cursor: default;
}
.calendar-settings .days li {
    z-index: 1;
    position: relative;
    margin-block: 0.25rem;
}
.days li.inactive {
    color: #aaa;
}
.days li.active {
    color: #fff;
    background: transparent!important;
}
.days li::before {
    position: absolute;
    content: "";
    left: 50%;
    top: 50%;
    height: 40px;
    width: 40px;
    z-index: -1;
    border-radius: 50%;
    transform: translate(-50%, -50%);
}
.days li.active::before,
.days li.active.green::before {
    background: #7ec752;
}
