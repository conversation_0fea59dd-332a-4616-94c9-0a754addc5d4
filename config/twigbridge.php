<?php

/**
 * This file is part of the TwigBridge package.
 *
 * @copyright <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Session;

/**
 * Configuration options for Twig.
 */
return [

    'twig' => [
        /*
        |--------------------------------------------------------------------------
        | Extension
        |--------------------------------------------------------------------------
        |
        | File extension for Twig view files.
        |
        */
        'extension' => 'twig',

        /*
        |--------------------------------------------------------------------------
        | Accepts all Twig environment configuration options
        |--------------------------------------------------------------------------
        |
        | http://twig.sensiolabs.org/doc/api.html#environment-options
        |
        */
        'environment' => [

            // When set to true, the generated templates have a __toString() method
            // that you can use to display the generated nodes.
            // default: false
            'debug' => env('APP_DEBUG', false),

            // The charset used by the templates.
            // default: utf-8
            'charset' => 'utf-8',

            // An absolute path where to store the compiled templates, or false to disable caching. If null
            // then the cache file path is used.
            // default: cache file storage path
            'cache' => null,

            // When developing with Twig, it's useful to recompile the template
            // whenever the source code changes. If you don't provide a value
            // for the auto_reload option, it will be determined automatically based on the debug value.
            'auto_reload' => true,

            // If set to false, Twig will silently ignore invalid variables
            // (variables and or attributes/methods that do not exist) and
            // replace them with a null value. When set to true, Twig throws an exception instead.
            // default: false
            'strict_variables' => false,

            // If set to true, auto-escaping will be enabled by default for all templates.
            // default: 'html'
            'autoescape' => 'html',

            // A flag that indicates which optimizations to apply
            // (default to -1 -- all optimizations are enabled; set it to 0 to disable)
            'optimizations' => -1,
        ],

        /*
        |--------------------------------------------------------------------------
        | Safe Classes
        |--------------------------------------------------------------------------
        |
        | When set, the output of the `__string` method of the following classes will not be escaped.
        | default: Laravel's Htmlable, which the HtmlString class implements.
        |
        */
        'safe_classes' => [
            \Illuminate\Contracts\Support\Htmlable::class => ['html'],
        ],

        /*
        |--------------------------------------------------------------------------
        | Global variables
        |--------------------------------------------------------------------------
        |
        | These will always be passed in and can be accessed as Twig variables.
        | NOTE: these will be overwritten if you pass data into the view with the same key.
        |
        */
        'globals' => [],
    ],

    'extensions' => [

        /*
        |--------------------------------------------------------------------------
        | Extensions
        |--------------------------------------------------------------------------
        |
        | Enabled extensions.
        |
        | `Twig\Extension\DebugExtension` is enabled automatically if twig.debug is TRUE.
        |
        */
        'enabled' => [
            'TwigBridge\Extension\Laravel\Event',
            'TwigBridge\Extension\Loader\Facades',
            'TwigBridge\Extension\Loader\Filters',
            'TwigBridge\Extension\Loader\Functions',
            'TwigBridge\Extension\Loader\Globals',

            'TwigBridge\Extension\Laravel\Auth',
            'TwigBridge\Extension\Laravel\Config',
            'TwigBridge\Extension\Laravel\Dump',
            'TwigBridge\Extension\Laravel\Input',
            'TwigBridge\Extension\Laravel\Session',
            'TwigBridge\Extension\Laravel\Str',
            'TwigBridge\Extension\Laravel\Translator',
            'TwigBridge\Extension\Laravel\Url',
            'TwigBridge\Extension\Laravel\Model',
            // 'TwigBridge\Extension\Laravel\Gate',

            'TwigBridge\Extension\Laravel\Form',
            // 'TwigBridge\Extension\Laravel\Html',
            // 'TwigBridge\Extension\Laravel\Legacy\Facades',
        ],

        /*
        |--------------------------------------------------------------------------
        | Facades
        |--------------------------------------------------------------------------
        |
        | Available facades. Access like `{{ Config.get('foo.bar') }}`.
        |
        | Each facade can take an optional array of options. To mark the whole facade
        | as safe you can set the option `'is_safe' => true`. Setting the facade as
        | safe means that any HTML returned will not be escaped.
        |
        | It is advisable to not set the whole facade as safe and instead mark the
        | each appropriate method as safe for security reasons. You can do that with
        | the following syntax:
        |
        | <code>
        |     'Form' => [
        |         'is_safe' => [
        |             'open'
        |         ]
        |     ]
        | </code>
        |
        | The values of the `is_safe` array must match the called method on the facade
        | in order to be marked as safe.
        |
        */
        'facades' => [],

        /*
        |--------------------------------------------------------------------------
        | Functions
        |--------------------------------------------------------------------------
        |
        | Available functions. Access like `{{ secure_url(...) }}`.
        |
        | Each function can take an optional array of options. These options are
        | passed directly to `Twig\TwigFunction`.
        |
        | So for example, to mark a function as safe you can do the following:
        |
        | <code>
        |     'link_to' => [
        |         'is_safe' => ['html']
        |     ]
        | </code>
        |
        | The options array also takes a `callback` that allows you to name the
        | function differently in your Twig templates than what it's actually called.
        |
        | <code>
        |     'link' => [
        |         'callback' => 'link_to'
        |     ]
        | </code>
        |
        */
        'functions' => [
            'elixir',
            'head',
            'last',
            'mix',
            '_e' => function ($key) {
                $errors = Session::get('errors', new Illuminate\Support\MessageBag);
                if ($errors->has($key)) {
                    return new \Twig\Markup(view('_error', ['messages' => $errors->get($key)])->render(), 'UTF-8');
                }
            },
            'env' => function ($key, $default = null) {
                return env($key, $default);
            },
            'user' => function () {
                return auth()->user();
            },
            'get_url_path' => function ($url = '') {
                $info = parse_url($url);
                return $info['path'] ?? $info['host'] ?? '';
            },
            'generate_meta' => function ($title = null, $description = null, $image = null) {
                return generate_social_meta($title, $description, $image);
            },
            'route_locale' => function ($route, $opts = [], $locale = null) {
                return route_locale($route, $opts, $locale);
            },
            'auth' => function () {
                return auth();
            },
            'waiting_applications' => function () {
                return waiting_applications();
            },
            'career_count' => function () {
                return career_count();
            },
            'get_accommodations' => function ($accommodation_title) {
                return get_accommodations($accommodation_title);
            },
            'get_accommodation_by_title' => function ($accommodation_title) {
                return get_accommodation_by_title($accommodation_title);
            },
            'can_receive_sms' => function ($value) {
                return can_receive_sms($value);
            },
            'route_name' => function () {
                return Illuminate\Support\Facades\Route::currentRouteName();
            },
        ],

        /*
        |--------------------------------------------------------------------------
        | Filters
        |--------------------------------------------------------------------------
        |
        | Available filters. Access like `{{ variable|filter }}`.
        |
        | Each filter can take an optional array of options. These options are
        | passed directly to `Twig\TwigFilter`.
        |
        | So for example, to mark a filter as safe you can do the following:
        |
        | <code>
        |     'studly_case' => [
        |         'is_safe' => ['html']
        |     ]
        | </code>
        |
        | The options array also takes a `callback` that allows you to name the
        | filter differently in your Twig templates than what is actually called.
        |
        | <code>
        |     'snake' => [
        |         'callback' => 'snake_case'
        |     ]
        | </code>
        |
        */
        'filters' => [
            'get' => 'data_get',
            'tr_lower'   => 'tr_strtolower',
            'tr_upper'   => 'tr_strtoupper',
            'tr_ucfirst' => 'tr_ucfirst',
            'tr_ucwords' => 'tr_ucwords',
            'formatAs' => function ($value, $formatter, $data = []) {

                switch ($formatter) {
                    case 'price':
                        $dontSayFree = Arr::get($data, 0, true);
                        $suffix = Arr::get($data, 1, 'TL');

                        if ((float) $value <= 0 && !$dontSayFree) {
                            return "Ücretsiz";
                        }

                        return number_format($value, 2, ',', '.') . ($suffix ? (' ' . $suffix) : '');
                    case 'try':
                        return number_format($value, 2, ',', '.');
                    case 'rate':
                        return str_replace(".00", "", (string)number_format($value, 2, ".", ""));
                    default:
                        return $value;
                }
            },
            'get_filter_labels' => function ($list = [], $values = []) {
                return implode(', ', array_values(array_filter($list, function ($key) use ($values) {
                    return in_array($key, $values);
                }, ARRAY_FILTER_USE_KEY)));
            },
            'date' => function ($value) {

                $date = Carbon::parse($value);

                $isToday = $date->isToday();

                $isYesterday = $date->isYesterday();

                if ($isToday || $isYesterday) {

                    return ($isToday ? 'Bugün' : 'Dün') . ", " . $date->format('H:i');
                }

                return $date->format('d/m/y');
            },
            'date_time' => function ($value, $full = true) {

                $date = Carbon::parse($value);

                if ($full) {
                    return $date->locale('tr_TR')->isoFormat('LLLL');
                }

                return $date->format('d/m/Y H:i');
            },
            'date_range' => function ($start_date, $end_date) {
                return date_range($start_date, $end_date);
            },
            'day_length' => function ($start_date, $end_date) {
                $start = Carbon::parse($start_date);
                $end = Carbon::parse($end_date);

                return $start->diffInDays($end, true);
            },
            'date_time_year' => function ($value, $compare_with, $append_day = true) {
                $date = Carbon::parse($value);
                $with = Carbon::parse($compare_with);

                if ($date->year != date('Y') || $date->year != $with->year) {
                    return $date->locale('tr_TR')->isoFormat('DD MMMM YYYY' . ($append_day ? ', dddd' : ''));
                }

                return $date->locale('tr_TR')->isoFormat('DD MMMM' . ($append_day ? ', dddd' : ''));
            },
            'date_iso' => function ($value, $format = 'lll') {
                $date = Carbon::parse($value);

                return $date->locale(app()->getLocale())->isoFormat($format);
            },
            'censor' => function ($value, $threshold = 3, $percentage = 0.4) {
                $parts = preg_split('/([@.])/', $value, -1, PREG_SPLIT_DELIM_CAPTURE | PREG_SPLIT_NO_EMPTY);

                for ($i = 0; $i < count($parts); $i++) {
                    $length = mb_strlen($parts[$i]);

                    if ($length >= $threshold) {

                        $censor_length = $length > $threshold ? ceil($length * $percentage) : $threshold;
                        $remaining_length = $length - $censor_length;

                        $censored_text = mb_substr($parts[$i], 0, $remaining_length);
                        $censored_text .= str_repeat("*", $censor_length);

                        $parts[$i] = $censored_text;
                    }
                }

                return implode('', $parts);
            },
            'shrink' => function ($value, $length = 30, $mark = '...') {
                if (mb_strlen($value) <= $length) {
                    return $value;
                }

                $mark_length = mb_strlen($mark);

                if ($mark_length > $length) {
                    $mark = '';
                    $mark_length = 0;
                }

                $left_length = round($length / 2, 0, PHP_ROUND_HALF_DOWN);
                $right_length = round($length / 2, 0, PHP_ROUND_HALF_UP);

                $mark_left_length = round($mark_length / 2, 0, PHP_ROUND_HALF_UP);
                $mark_right_length = round($mark_length / 2, 0, PHP_ROUND_HALF_DOWN);

                $left_string = mb_substr($value, 0, $left_length - $mark_left_length);
                $right_string = mb_substr($value, 0 - ($right_length - $mark_right_length));

                return $left_string . $mark . $right_string;
            }


        ],
    ],
];
